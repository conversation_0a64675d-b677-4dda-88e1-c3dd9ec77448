import cv2
import numpy as np
import argparse
from collections import deque

class GridMotionVisualization:
    def __init__(self, grid_spacing=30, motion_sensitivity=1.0):
        self.initial_grid_spacing = grid_spacing
        self.motion_sensitivity = motion_sensitivity
        
        # Parameters for optical flow
        self.lk_params = dict(winSize=(21, 21),
                             maxLevel=3,
                             criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 30, 0.01))
        
        # Grid properties
        self.grid_points = None  # Current positions of grid points
        self.original_grid_points = None  # Original grid layout for reference
        self.frame_width = None
        self.frame_height = None
        self.frame_center = None
        
        # Motion analysis
        self.motion_history = deque(maxlen=10)
        
        # Accumulated transformations
        self.accumulated_zoom = 1.0
        self.accumulated_pan_x = 0.0
        self.accumulated_pan_y = 0.0
        self.accumulated_rotation = 0.0
        
        # Motion thresholds to reduce noise
        self.zoom_threshold = 0.005  # 0.5% zoom change threshold
        self.pan_threshold = 0.5     # 0.5 pixel pan threshold
        self.rotation_threshold = 0.01  # ~0.6 degree rotation threshold
        
    def initialize_grid(self, frame_shape):
        """Initialize regular grid of dots with extended coverage beyond frame"""
        self.frame_height, self.frame_width = frame_shape
        self.frame_center = np.array([self.frame_width/2, self.frame_height/2])
        
        # Extend grid beyond frame boundaries to ensure coverage during panning
        # Add 50% padding on all sides
        padding_x = int(self.frame_width * 0.5)
        padding_y = int(self.frame_height * 0.5)
        
        # Create extended grid points
        x_start = -padding_x + self.initial_grid_spacing//2
        x_end = self.frame_width + padding_x
        y_start = -padding_y + self.initial_grid_spacing//2
        y_end = self.frame_height + padding_y
        
        x_coords = np.arange(x_start, x_end, self.initial_grid_spacing)
        y_coords = np.arange(y_start, y_end, self.initial_grid_spacing)
        
        grid_x, grid_y = np.meshgrid(x_coords, y_coords)
        
        # Store original grid layout
        self.original_grid_points = np.column_stack([grid_x.flatten(), grid_y.flatten()]).astype(np.float32)
        
        # Initialize current grid points as copy of original
        self.grid_points = self.original_grid_points.copy()
        
        print(f"Initialized extended grid with {len(self.grid_points)} dots ({len(y_coords)}x{len(x_coords)})")
        print(f"Grid coverage: {x_start} to {x_end-self.initial_grid_spacing} (width: {x_end-x_start})")
        print(f"                {y_start} to {y_end-self.initial_grid_spacing} (height: {y_end-y_start})")
        
    def calculate_motion_from_optical_flow(self, old_gray, new_gray):
        """Calculate frame-to-frame motion using optical flow"""
        # Select sample points for motion calculation
        sample_points = []
        step = max(40, self.initial_grid_spacing)
        
        for y in range(step, self.frame_height - step, step):
            for x in range(step, self.frame_width - step, step):
                sample_points.append([x, y])
        
        if len(sample_points) < 4:
            return {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0}
        
        sample_points = np.array(sample_points, dtype=np.float32).reshape(-1, 1, 2)
        
        # Calculate optical flow for sample points
        new_points, status, error = cv2.calcOpticalFlowPyrLK(
            old_gray, new_gray, sample_points, None, **self.lk_params)
        
        # Filter good points
        good_mask = (status == 1) & (error < 30)
        
        if np.sum(good_mask) < 4:
            return {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0}
        
        old_pts = sample_points[good_mask.flatten()].reshape(-1, 2)
        new_pts = new_points[good_mask.flatten()].reshape(-1, 2)
        
        # Calculate transformation matrix
        try:
            transform_matrix = cv2.estimateAffinePartial2D(old_pts, new_pts)[0]
            
            if transform_matrix is None:
                return {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0}
            
            # Extract motion parameters from transformation matrix
            a = transform_matrix[0, 0]
            b = transform_matrix[0, 1]
            tx = transform_matrix[0, 2]
            ty = transform_matrix[1, 2]
            
            # Scale (zoom) - frame-to-frame change
            scale = np.sqrt(a*a + b*b)
            
            # Rotation (in radians) - frame-to-frame change
            rotation = np.arctan2(b, a)
            
            # Translation (pan) - frame-to-frame change
            pan_x = tx * self.motion_sensitivity
            pan_y = ty * self.motion_sensitivity
            
            return {
                "zoom": scale,
                "pan_x": pan_x,
                "pan_y": pan_y,
                "rotation": rotation
            }
            
        except Exception as e:
            return {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0}
    
    def apply_motion_thresholds(self, motion):
        """Apply thresholds to reduce noise in motion detection"""
        filtered_motion = motion.copy()
        
        # Apply zoom threshold
        zoom_change = abs(motion["zoom"] - 1.0)
        if zoom_change < self.zoom_threshold:
            filtered_motion["zoom"] = 1.0
        
        # Apply pan thresholds
        if abs(motion["pan_x"]) < self.pan_threshold:
            filtered_motion["pan_x"] = 0.0
        if abs(motion["pan_y"]) < self.pan_threshold:
            filtered_motion["pan_y"] = 0.0
        
        # Apply rotation threshold
        if abs(motion["rotation"]) < self.rotation_threshold:
            filtered_motion["rotation"] = 0.0
        
        return filtered_motion
    
    def update_motion(self, old_gray, new_gray):
        """Update motion analysis and accumulate transformations"""
        # Get frame-to-frame motion
        raw_motion = self.calculate_motion_from_optical_flow(old_gray, new_gray)
        
        # Apply noise thresholds
        frame_motion = self.apply_motion_thresholds(raw_motion)
        
        self.motion_history.append(frame_motion)
        
        # Accumulate the transformations over time
        # Only update if motion exceeds thresholds
        
        # Accumulate zoom (multiplicative)
        zoom_change = frame_motion["zoom"]
        if zoom_change != 1.0:  # Only if it passed threshold
            self.accumulated_zoom *= zoom_change
        
        # Accumulate pan (additive)
        pan_x_change = frame_motion["pan_x"]
        pan_y_change = frame_motion["pan_y"]
        if pan_x_change != 0.0:  # Only if it passed threshold
            self.accumulated_pan_x += pan_x_change
        if pan_y_change != 0.0:  # Only if it passed threshold
            self.accumulated_pan_y += pan_y_change
        
        # Accumulate rotation (additive)
        rotation_change = frame_motion["rotation"]
        if rotation_change != 0.0:  # Only if it passed threshold
            self.accumulated_rotation += rotation_change
        
        # Apply the accumulated transformation to the grid points
        self.transform_grid_points()
        
        return {
            "zoom": self.accumulated_zoom,
            "pan_x": self.accumulated_pan_x,
            "pan_y": self.accumulated_pan_y,
            "rotation": self.accumulated_rotation,
            "raw_motion": raw_motion,
            "filtered_motion": frame_motion
        }
    
    def transform_grid_points(self):
        """Apply accumulated transformations to grid points with camera-centric zoom"""
        if self.original_grid_points is None:
            return
        
        # Start with original grid positions
        points = self.original_grid_points.copy()
        
        # For zoom: When camera zooms in, background appears larger
        # So dots should move away from center to follow background features
        # Center points around frame center, scale them, then move back
        centered_points = points - self.frame_center
        scaled_points = centered_points * self.accumulated_zoom
        points = scaled_points + self.frame_center
        
        # For rotation: rotate points around the frame center
        if abs(self.accumulated_rotation) > 0:
            # Center points around frame center for rotation
            centered_points = points - self.frame_center
            
            # Apply rotation
            cos_r = np.cos(self.accumulated_rotation)
            sin_r = np.sin(self.accumulated_rotation)
            
            rotation_matrix = np.array([[cos_r, -sin_r],
                                       [sin_r, cos_r]])
            
            rotated_points = centered_points @ rotation_matrix.T
            
            # Move back to frame coordinates
            points = rotated_points + self.frame_center
        
        # Apply pan (translation)
        points[:, 0] += self.accumulated_pan_x
        points[:, 1] += self.accumulated_pan_y
        
        # Update current grid points
        self.grid_points = points
    
    def draw_visualization(self, frame, frame_count):
        """Draw the grid visualization"""
        if self.grid_points is None:
            return frame
        
        height, width = frame.shape[:2]
        
        # Calculate current grid spacing for display
        visible_points = []
        for point in self.grid_points:
            x, y = point.astype(int)
            if 0 <= x < width and 0 <= y < height:
                visible_points.append(point)
        
        if len(visible_points) >= 2:
            # Find spacing between nearby visible points
            visible_points = np.array(visible_points)
            distances = []
            for i in range(min(10, len(visible_points)-1)):
                for j in range(i+1, min(i+10, len(visible_points))):
                    dist = np.linalg.norm(visible_points[i] - visible_points[j])
                    if dist > 0:  # Avoid zero distances
                        distances.append(dist)
            
            if distances:
                current_spacing = np.median(distances)
            else:
                current_spacing = self.initial_grid_spacing
        else:
            current_spacing = self.initial_grid_spacing
        
        # Draw grid dots
        dots_drawn = 0
        for point in self.grid_points:
            x, y = point.astype(int)
            
            # Only draw points that are visible or slightly outside frame
            if -10 <= x < width + 10 and -10 <= y < height + 10:
                # Adjust dot size based on zoom level
                dot_size = max(2, min(6, int(3 * self.accumulated_zoom)))
                outline_size = dot_size + 1
                
                # Draw dot with outline
                cv2.circle(frame, (x, y), outline_size, (255, 255, 255), -1)  # White outline
                cv2.circle(frame, (x, y), dot_size, (0, 255, 255), -1)  # Yellow dots
                dots_drawn += 1
        
        # Draw center crosshair
        center_x, center_y = width // 2, height // 2
        cv2.line(frame, (center_x - 30, center_y), (center_x + 30, center_y), (0, 255, 0), 2)
        cv2.line(frame, (center_x, center_y - 30), (center_x, center_y + 30), (0, 255, 0), 2)
        cv2.circle(frame, (center_x, center_y), 5, (0, 255, 0), -1)
        
        # Motion analysis overlay
        self.draw_motion_info(frame, frame_count, dots_drawn, current_spacing)
        
        return frame
    
    def draw_motion_info(self, frame, frame_count, dots_visible, current_spacing):
        """Draw motion information overlay"""
        # Semi-transparent background
        overlay = frame.copy()
        cv2.rectangle(overlay, (10, 10), (520, 260), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.8, frame, 0.2, 0, frame)
        cv2.rectangle(frame, (10, 10), (520, 260), (255, 255, 255), 2)
        
        # Text properties
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.55
        font_thickness = 1
        y_pos = 30
        line_height = 22
        
        # Frame info
        total_dots = len(self.grid_points) if self.grid_points is not None else 0
        cv2.putText(frame, f"Frame: {frame_count} | Visible Dots: {dots_visible}/{total_dots}", 
                   (15, y_pos), font, font_scale, (255, 255, 255), font_thickness)
        y_pos += line_height
        
        # Grid spacing info
        spacing_change = (current_spacing / self.initial_grid_spacing - 1) * 100
        cv2.putText(frame, f"Grid Spacing: {current_spacing:.1f}px ({spacing_change:+.1f}%)", 
                   (15, y_pos), font, font_scale, (200, 200, 255), font_thickness)
        y_pos += line_height
        
        # Accumulated zoom
        zoom_percent = (self.accumulated_zoom - 1.0) * 100
        zoom_text = f"Total Zoom: {zoom_percent:+.1f}%"
        zoom_color = (255, 255, 255)
        
        if zoom_percent > 5:
            zoom_text += " (ZOOMED IN)"
            zoom_color = (0, 255, 0)
        elif zoom_percent < -5:
            zoom_text += " (ZOOMED OUT)"
            zoom_color = (0, 0, 255)
        else:
            zoom_text += " (NEUTRAL)"
        
        cv2.putText(frame, zoom_text, (15, y_pos), font, font_scale, zoom_color, font_thickness)
        y_pos += line_height
        
        # Accumulated pan
        pan_magnitude = np.sqrt(self.accumulated_pan_x**2 + self.accumulated_pan_y**2)
        pan_text = f"Total Pan: {pan_magnitude:.1f}px"
        
        if pan_magnitude > 10:
            if abs(self.accumulated_pan_x) > abs(self.accumulated_pan_y):
                direction = "RIGHT" if self.accumulated_pan_x > 0 else "LEFT"
            else:
                direction = "DOWN" if self.accumulated_pan_y > 0 else "UP"
            pan_text += f" ({direction})"
            pan_color = (255, 255, 0)
        else:
            pan_text += " (CENTERED)"
            pan_color = (255, 255, 255)
        
        cv2.putText(frame, pan_text, (15, y_pos), font, font_scale, pan_color, font_thickness)
        y_pos += line_height
        
        # Accumulated rotation
        rotation_deg = np.degrees(self.accumulated_rotation)
        rotation_text = f"Total Rotation: {rotation_deg:+.1f}°"
        if abs(rotation_deg) > 2:
            rotation_text += " (ROTATED)"
            rotation_color = (255, 0, 255)
        else:
            rotation_text += " (LEVEL)"
            rotation_color = (255, 255, 255)
        
        cv2.putText(frame, rotation_text, (15, y_pos), font, font_scale, rotation_color, font_thickness)
        y_pos += line_height
        
        # Motion thresholds info
        cv2.putText(frame, f"Thresholds: Zoom {self.zoom_threshold*100:.1f}%, Pan {self.pan_threshold:.1f}px, Rot {np.degrees(self.rotation_threshold):.1f}°", 
                   (15, y_pos), font, 0.45, (128, 128, 128), font_thickness)
        y_pos += line_height
        
        # Current frame motion (recent activity)
        if self.motion_history:
            recent_motion = self.motion_history[-1]
            recent_zoom = abs(recent_motion["zoom"] - 1.0) * 100
            recent_pan = np.sqrt(recent_motion["pan_x"]**2 + recent_motion["pan_y"]**2)
            recent_rot = abs(np.degrees(recent_motion["rotation"]))
            
            if recent_zoom > self.zoom_threshold * 100 or recent_pan > self.pan_threshold or recent_rot > np.degrees(self.rotation_threshold):
                activity_text = "Status: MOTION DETECTED"
                activity_color = (0, 255, 255)
            else:
                activity_text = "Status: STABLE (below thresholds)"
                activity_color = (128, 128, 128)
            
            cv2.putText(frame, activity_text, (15, y_pos), font, font_scale, activity_color, font_thickness)
            y_pos += line_height
            
            # Show raw vs filtered motion
            cv2.putText(frame, f"Raw Motion: Z:{recent_zoom:.2f}% P:{recent_pan:.1f}px R:{recent_rot:.1f}°", 
                       (15, y_pos), font, 0.45, (100, 100, 100), font_thickness)

def process_video(input_path, output_path=None, grid_spacing=30, motion_sensitivity=1.0):
    """Process video with grid motion visualization"""
    cap = cv2.VideoCapture(input_path)
    
    if not cap.isOpened():
        print(f"Error: Could not open video file {input_path}")
        return
    
    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"Video: {width}x{height}, {fps} FPS, {total_frames} frames")
    
    # Initialize video writer
    out = None
    if output_path:
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    # Initialize visualizer
    visualizer = GridMotionVisualization(grid_spacing=grid_spacing, 
                                        motion_sensitivity=motion_sensitivity)
    
    # Read first frame
    ret, frame = cap.read()
    if not ret:
        print("Error: Could not read first frame")
        return
    
    old_gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    visualizer.initialize_grid(old_gray.shape)
    
    frame_count = 0
    
    print("Processing video...")
    print("Yellow dots = motion grid with noise filtering")
    print("Green crosshair = camera center (zoom reference point)")
    print("Extended grid ensures dots are always available during panning")
    print("Motion thresholds prevent jitter from small movements")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        frame_gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Update motion analysis (this accumulates motion over time)
        motion_info = visualizer.update_motion(old_gray, frame_gray)
        
        # Draw visualization
        vis_frame = visualizer.draw_visualization(frame.copy(), frame_count)
        
        # Display
        cv2.imshow('Fixed Grid Motion Visualization', vis_frame)
        
        # Save frame
        if out:
            out.write(vis_frame)
        
        # Check for quit
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
        
        # Update for next frame
        old_gray = frame_gray.copy()
        
        # Progress
        if frame_count % 30 == 0:
            progress = (frame_count / total_frames) * 100
            zoom_info = f"Zoom: {(visualizer.accumulated_zoom-1)*100:+.1f}%"
            pan_info = f"Pan: {np.sqrt(visualizer.accumulated_pan_x**2 + visualizer.accumulated_pan_y**2):.1f}px"
            print(f"Progress: {progress:.1f}% | {zoom_info} | {pan_info}")
    
    # Cleanup
    cap.release()
    if out:
        out.release()
    cv2.destroyAllWindows()
    
    print(f"Complete! Processed {frame_count} frames.")
    if output_path:
        print(f"Output saved to: {output_path}")

def main():
    parser = argparse.ArgumentParser(description='Fixed accumulative grid motion visualization')
    parser.add_argument('input', help='Input video file')
    parser.add_argument('-o', '--output', help='Output video file (optional)')
    parser.add_argument('--grid-spacing', type=int, default=30,
                       help='Initial spacing between grid dots (default: 30)')
    parser.add_argument('--sensitivity', type=float, default=0.3,
                       help='Motion sensitivity multiplier (default: 1.0)')
    
    args = parser.parse_args()
    process_video(args.input, args.output, args.grid_spacing, args.sensitivity)

if __name__ == "__main__":
    main()
