import cv2
import numpy as np
import argparse
import os
import csv
from datetime import datetime

class CameraMotionDetector:
    """Detects camera movement using optical flow analysis"""
    
    def __init__(self, motion_sensitivity=0.5):
        self.motion_sensitivity = motion_sensitivity
        
        # Parameters for optical flow
        self.lk_params = dict(winSize=(21, 21),
                             maxLevel=3,
                             criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 30, 0.01))
        
        # Motion thresholds (same as opticalflowshift.py)
        self.zoom_threshold = 0.005  # 0.5% zoom change threshold
        self.pan_threshold = 0.5     # 0.5 pixel pan threshold
        self.rotation_threshold = 0.01  # ~0.6 degree rotation threshold
        
        self.frame_width = None
        self.frame_height = None
        
    def initialize(self, frame_shape):
        """Initialize camera motion detector"""
        self.frame_height, self.frame_width = frame_shape
        
    def detect_camera_motion(self, old_gray, new_gray):
        """Detect if camera is moving using optical flow"""
        # Select sample points for motion calculation
        sample_points = []
        step = 40
        
        for y in range(step, self.frame_height - step, step):
            for x in range(step, self.frame_width - step, step):
                sample_points.append([x, y])
        
        if len(sample_points) < 4:
            return False, {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0}
        
        sample_points = np.array(sample_points, dtype=np.float32).reshape(-1, 1, 2)
        
        # Calculate optical flow for sample points
        new_points, status, error = cv2.calcOpticalFlowPyrLK(
            old_gray, new_gray, sample_points, None, **self.lk_params)
        
        # Filter good points
        good_mask = (status == 1) & (error < 30)
        
        if np.sum(good_mask) < 4:
            return False, {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0}
        
        old_pts = sample_points[good_mask.flatten()].reshape(-1, 2)
        new_pts = new_points[good_mask.flatten()].reshape(-1, 2)
        
        # Calculate transformation matrix
        try:
            transform_matrix = cv2.estimateAffinePartial2D(old_pts, new_pts)[0]
            
            if transform_matrix is None:
                return False, {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0}
            
            # Extract motion parameters from transformation matrix
            a = transform_matrix[0, 0]
            b = transform_matrix[0, 1]
            tx = transform_matrix[0, 2]
            ty = transform_matrix[1, 2]
            
            # Scale (zoom) - frame-to-frame change
            scale = np.sqrt(a*a + b*b)
            
            # Rotation (in radians) - frame-to-frame change
            rotation = np.arctan2(b, a)
            
            # Translation (pan) - frame-to-frame change
            pan_x = tx * self.motion_sensitivity
            pan_y = ty * self.motion_sensitivity
            
            motion_params = {
                "zoom": scale,
                "pan_x": pan_x,
                "pan_y": pan_y,
                "rotation": rotation
            }
            
            # Check if motion exceeds thresholds
            zoom_change = abs(scale - 1.0)
            pan_magnitude = np.sqrt(pan_x*pan_x + pan_y*pan_y)
            rotation_magnitude = abs(rotation)
            
            is_moving = (zoom_change > self.zoom_threshold or 
                        pan_magnitude > self.pan_threshold or 
                        rotation_magnitude > self.rotation_threshold)
            
            return is_moving, motion_params
            
        except Exception as e:
            return False, {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0}

def detect_birds_with_motion_awareness(input_path, output_path, threshold=15, blur_kernel=3, 
                                     min_area=20, max_area=400, motion_sensitivity=0.5, csv_path=None):
    """
    Enhanced bird detection that pauses when camera is moving.
    
    Args:
        input_path (str): Path to input video file
        output_path (str): Path to output video file
        threshold (int): Threshold for motion detection (0-255)
        blur_kernel (int): Kernel size for Gaussian blur (odd number)
        min_area (int): Minimum contour area to consider as bird (pixels)
        max_area (int): Maximum contour area to consider as bird (pixels)
        motion_sensitivity (float): Camera motion sensitivity (0.5 is good default)
        csv_path (str): Optional path to save detection data
    """
    
    # Open the input video
    cap = cv2.VideoCapture(input_path)
    
    if not cap.isOpened():
        print(f"Error: Could not open video file {input_path}")
        return False
    
    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"Processing video: {width}x{height} @ {fps}fps, {total_frames} frames")
    print(f"Bird detection: area {min_area}-{max_area} pixels, threshold {threshold}")
    print(f"Camera motion sensitivity: {motion_sensitivity}")
    
    # Define the codec and create VideoWriter object
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    # Initialize camera motion detector
    motion_detector = CameraMotionDetector(motion_sensitivity)
    motion_detector.initialize((height, width))
    
    # Prepare CSV writer if needed
    csv_file = None
    csv_writer = None
    if csv_path:
        csv_file = open(csv_path, 'w', newline='')
        csv_writer = csv.writer(csv_file)
        csv_writer.writerow(['frame_number', 'timestamp', 'camera_moving', 'bird_count', 'detections', 'motion_info'])
    
    # Read first frame
    ret, prev_frame = cap.read()
    if not ret:
        print("Error: Could not read first frame")
        return False
    
    # Convert to grayscale for motion detection
    prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)
    prev_gray = cv2.GaussianBlur(prev_gray, (blur_kernel, blur_kernel), 0)
    
    frame_count = 0
    camera_moving_frames = 0
    bird_detection_frames = 0
    
    while True:
        ret, current_frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        timestamp = cap.get(cv2.CAP_PROP_POS_MSEC) / 1000.0
        
        # Convert current frame to grayscale
        current_gray = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY)
        current_gray = cv2.GaussianBlur(current_gray, (blur_kernel, blur_kernel), 0)
        
        # Check for camera movement
        is_camera_moving, motion_params = motion_detector.detect_camera_motion(prev_gray, current_gray)
        
        bird_detections = []
        
        if is_camera_moving:
            camera_moving_frames += 1
            # Camera is moving - skip bird detection to avoid false positives
            detection_status = "CAMERA_MOVING"
        else:
            bird_detection_frames += 1
            # Camera is stable - perform bird detection
            detection_status = "DETECTING_BIRDS"
            
            # Compute absolute difference between current and previous frame
            frame_diff = cv2.absdiff(prev_gray, current_gray)
            
            # Apply threshold to get binary image
            _, thresh = cv2.threshold(frame_diff, threshold, 255, cv2.THRESH_BINARY)
            
            # Apply morphological operations optimized for small objects
            kernel_small = np.ones((2, 2), np.uint8)
            kernel_medium = np.ones((3, 3), np.uint8)
            
            thresh = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel_small)
            thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel_medium)
            
            # Find contours
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Filter contours for bird-like objects
            for contour in contours:
                area = cv2.contourArea(contour)
                if min_area <= area <= max_area:
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / h if h > 0 else 0
                    
                    if 0.2 <= aspect_ratio <= 5.0:
                        perimeter = cv2.arcLength(contour, True)
                        if perimeter > 0:
                            compactness = 4 * np.pi * area / (perimeter * perimeter)
                            if compactness > 0.1:
                                bird_detections.append({
                                    'bbox': (x, y, w, h),
                                    'area': area,
                                    'center': (x + w//2, y + h//2),
                                    'compactness': compactness
                                })
        
        # Create output frame
        output_frame = current_frame.copy()
        
        # Draw bounding boxes for detected birds (only when not camera moving)
        if not is_camera_moving:
            for i, detection in enumerate(bird_detections):
                x, y, w, h = detection['bbox']
                area = detection['area']
                center_x, center_y = detection['center']
                
                # Color code by size
                if area <= 100:
                    color = (0, 255, 0)  # Green for small objects
                elif area <= 200:
                    color = (0, 255, 255)  # Yellow for medium objects
                else:
                    color = (0, 0, 255)  # Red for large objects
                
                cv2.rectangle(output_frame, (x, y), (x + w, y + h), color, 2)
                cv2.circle(output_frame, (center_x, center_y), 2, (255, 0, 255), -1)
                
                label = f"B{i+1}:{area}px"
                cv2.putText(output_frame, label, (x, y-5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
        
        # Add status overlay
        status_color = (0, 0, 255) if is_camera_moving else (0, 255, 0)
        cv2.putText(output_frame, f"Status: {detection_status}", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, status_color, 2)
        cv2.putText(output_frame, f"Frame: {frame_count}/{total_frames}", 
                   (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(output_frame, f"Birds: {len(bird_detections)}", 
                   (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(output_frame, f"Time: {timestamp:.1f}s", 
                   (10, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        # Show motion parameters
        if is_camera_moving:
            zoom_change = abs(motion_params["zoom"] - 1.0) * 100
            pan_mag = np.sqrt(motion_params["pan_x"]**2 + motion_params["pan_y"]**2)
            rot_deg = abs(np.degrees(motion_params["rotation"]))
            cv2.putText(output_frame, f"Motion: Z:{zoom_change:.1f}% P:{pan_mag:.1f}px R:{rot_deg:.1f}°", 
                       (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
        
        # Record detection data to CSV if enabled
        if csv_writer:
            detection_data = []
            for i, detection in enumerate(bird_detections):
                x, y, w, h = detection['bbox']
                area = detection['area']
                comp = detection['compactness']
                detection_data.append(f"B{i+1}:({x},{y},{w},{h},{area},{comp:.2f})")
            
            motion_info = f"Z:{motion_params['zoom']:.3f},P:{motion_params['pan_x']:.1f},{motion_params['pan_y']:.1f},R:{motion_params['rotation']:.3f}"
            
            csv_writer.writerow([
                frame_count,
                f"{timestamp:.3f}",
                is_camera_moving,
                len(bird_detections),
                "|".join(detection_data) if detection_data else "None",
                motion_info
            ])
        
        # Write the frame
        out.write(output_frame)
        
        # Update previous frame
        prev_gray = current_gray.copy()
        
        # Progress indicator
        if frame_count % 100 == 0 or frame_count == total_frames:
            progress = (frame_count / total_frames) * 100
            print(f"Progress: {progress:.1f}% ({frame_count}/{total_frames}) | "
                  f"Birds: {len(bird_detections)} | Camera moving: {is_camera_moving}")
    
    # Release everything
    cap.release()
    out.release()
    if csv_file:
        csv_file.close()
    cv2.destroyAllWindows()
    
    print(f"Motion-aware bird detection video saved to: {output_path}")
    print(f"Camera moving frames: {camera_moving_frames}/{frame_count} ({camera_moving_frames/frame_count*100:.1f}%)")
    print(f"Bird detection frames: {bird_detection_frames}/{frame_count} ({bird_detection_frames/frame_count*100:.1f}%)")
    if csv_path:
        print(f"Detection data saved to: {csv_path}")
    return True

def main():
    parser = argparse.ArgumentParser(description='Bird detection with camera motion awareness')
    parser.add_argument('input', help='Input video file path')
    parser.add_argument('output', help='Output video file path')
    parser.add_argument('--threshold', type=int, default=15,
                       help='Motion detection threshold (0-255, default: 15)')
    parser.add_argument('--blur', type=int, default=3,
                       help='Blur kernel size (odd number, default: 3)')
    parser.add_argument('--min-area', type=int, default=20,
                       help='Minimum bird area in pixels (default: 20)')
    parser.add_argument('--max-area', type=int, default=400,
                       help='Maximum bird area in pixels (default: 400)')
    parser.add_argument('--motion-sensitivity', type=float, default=0.5,
                       help='Camera motion sensitivity (default: 0.5)')
    parser.add_argument('--csv', help='Optional path to save detection data as CSV')
    
    args = parser.parse_args()
    
    # Check if input file exists
    if not os.path.exists(args.input):
        print(f"Error: Input file '{args.input}' does not exist")
        return
    
    # Create output directory if it doesn't exist
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Create CSV directory if specified
    if args.csv:
        csv_dir = os.path.dirname(args.csv) or '.'
        if not os.path.exists(csv_dir):
            os.makedirs(csv_dir)
    
    print(f"Input: {args.input}")
    print(f"Output: {args.output}")
    if args.csv:
        print(f"CSV Output: {args.csv}")
    
    success = detect_birds_with_motion_awareness(
        args.input, args.output, args.threshold, args.blur, 
        args.min_area, args.max_area, args.motion_sensitivity, args.csv
    )
    
    if success:
        print("Motion-aware bird detection completed successfully!")
    else:
        print("Motion-aware bird detection failed!")

if __name__ == "__main__":
    main()
