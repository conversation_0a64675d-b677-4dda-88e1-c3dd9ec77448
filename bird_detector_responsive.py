import cv2
import numpy as np
import argparse
import os
import csv
from collections import deque
from datetime import datetime

class ResponsiveCameraMotionDetector:
    """Responsive camera motion detector with immediate transition detection"""
    
    def __init__(self, motion_sensitivity=1.0, temporal_window=3):
        self.motion_sensitivity = motion_sensitivity
        self.temporal_window = temporal_window  # Reduced for faster response
        
        # Parameters for optical flow
        self.lk_params = dict(winSize=(15, 15),
                             maxLevel=2,
                             criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 20, 0.01))
        
        # Enhanced motion thresholds for immediate response
        self.zoom_threshold = 0.008      # 0.8% zoom change (more sensitive for zoom)
        self.pan_threshold = 1.5         # 1.5 pixel pan threshold
        self.rotation_threshold = 0.015  # ~0.9 degree rotation threshold
        
        # Immediate response thresholds (for strong motion)
        self.immediate_zoom_threshold = 0.02     # 2% zoom change for immediate response
        self.immediate_pan_threshold = 3.0       # 3 pixel pan for immediate response
        self.immediate_rotation_threshold = 0.03 # ~1.7 degree for immediate response
        
        # Temporal filtering with faster response
        self.motion_history = deque(maxlen=temporal_window)
        self.motion_confidence_threshold = 0.5  # 50% threshold (more responsive)
        
        # Transition detection
        self.prev_motion_state = False
        self.transition_frames = 0
        self.max_transition_frames = 3  # Allow 3 frames for transition
        
        self.frame_width = None
        self.frame_height = None
        
    def initialize(self, frame_shape):
        """Initialize camera motion detector"""
        self.frame_height, self.frame_width = frame_shape
        
    def detect_camera_motion(self, old_gray, new_gray):
        """Enhanced camera motion detection with immediate response and transition handling"""
        # Select stable sample points
        sample_points = []
        step = 50  # Balanced step size
        border = 60  # Moderate border
        
        for y in range(border, self.frame_height - border, step):
            for x in range(border, self.frame_width - border, step):
                sample_points.append([x, y])
        
        if len(sample_points) < 6:
            motion_detected = False
            motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0, "immediate": False}
        else:
            sample_points = np.array(sample_points, dtype=np.float32).reshape(-1, 1, 2)
            
            # Calculate optical flow
            new_points, status, error = cv2.calcOpticalFlowPyrLK(
                old_gray, new_gray, sample_points, None, **self.lk_params)
            
            # Filter good points
            good_mask = (status == 1) & (error < 15)  # Stricter error threshold
            
            if np.sum(good_mask) < 6:
                motion_detected = False
                motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0, "immediate": False}
            else:
                old_pts = sample_points[good_mask.flatten()].reshape(-1, 2)
                new_pts = new_points[good_mask.flatten()].reshape(-1, 2)
                
                # Calculate transformation with RANSAC
                try:
                    transform_result = cv2.estimateAffinePartial2D(
                        old_pts, new_pts, method=cv2.RANSAC, 
                        ransacReprojThreshold=1.5, maxIters=1000, confidence=0.95)
                    
                    transform_matrix = transform_result[0]
                    inlier_mask = transform_result[1]
                    
                    if transform_matrix is None or inlier_mask is None:
                        motion_detected = False
                        motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0, "immediate": False}
                    else:
                        # Calculate confidence
                        confidence = np.sum(inlier_mask) / len(inlier_mask) if len(inlier_mask) > 0 else 0
                        
                        # Extract motion parameters
                        a = transform_matrix[0, 0]
                        b = transform_matrix[0, 1]
                        tx = transform_matrix[0, 2]
                        ty = transform_matrix[1, 2]
                        
                        scale = np.sqrt(a*a + b*b)
                        rotation = np.arctan2(b, a)
                        pan_x = tx * self.motion_sensitivity
                        pan_y = ty * self.motion_sensitivity
                        
                        motion_params = {
                            "zoom": scale,
                            "pan_x": pan_x,
                            "pan_y": pan_y,
                            "rotation": rotation,
                            "confidence": confidence,
                            "immediate": False
                        }
                        
                        # Check for immediate response (strong motion)
                        zoom_change = abs(scale - 1.0)
                        pan_magnitude = np.sqrt(pan_x*pan_x + pan_y*pan_y)
                        rotation_magnitude = abs(rotation)
                        
                        immediate_motion = (confidence > 0.6 and (
                            zoom_change > self.immediate_zoom_threshold or 
                            pan_magnitude > self.immediate_pan_threshold or 
                            rotation_magnitude > self.immediate_rotation_threshold))
                        
                        # Check for normal motion
                        normal_motion = (confidence > 0.7 and (
                            zoom_change > self.zoom_threshold or 
                            pan_magnitude > self.pan_threshold or 
                            rotation_magnitude > self.rotation_threshold))
                        
                        motion_detected = immediate_motion or normal_motion
                        motion_params["immediate"] = immediate_motion
                        
                except Exception as e:
                    motion_detected = False
                    motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0, "immediate": False}
        
        # Handle transitions more responsively
        if motion_detected != self.prev_motion_state:
            # State change detected
            self.transition_frames = 0
            if motion_detected:
                # Starting motion - immediate response if strong motion
                if motion_params.get("immediate", False):
                    final_motion_detected = True
                else:
                    # Add to history and check
                    self.motion_history.append(motion_detected)
                    motion_ratio = sum(self.motion_history) / len(self.motion_history)
                    final_motion_detected = motion_ratio >= self.motion_confidence_threshold
            else:
                # Stopping motion - be more conservative
                self.motion_history.append(motion_detected)
                if len(self.motion_history) >= 2:  # Need at least 2 frames of no motion
                    motion_ratio = sum(self.motion_history) / len(self.motion_history)
                    final_motion_detected = motion_ratio >= self.motion_confidence_threshold
                else:
                    final_motion_detected = self.prev_motion_state  # Keep previous state
        else:
            # Same state as before
            self.transition_frames += 1
            self.motion_history.append(motion_detected)
            
            if self.transition_frames < self.max_transition_frames:
                # Still in transition period - be more conservative
                if motion_detected and motion_params.get("immediate", False):
                    final_motion_detected = True  # Immediate strong motion
                else:
                    final_motion_detected = self.prev_motion_state  # Keep previous state
            else:
                # Past transition period - use temporal filtering
                motion_ratio = sum(self.motion_history) / len(self.motion_history)
                final_motion_detected = motion_ratio >= self.motion_confidence_threshold
        
        # Update state
        self.prev_motion_state = final_motion_detected
        
        motion_params["temporal_confidence"] = sum(self.motion_history) / len(self.motion_history) if self.motion_history else 0
        motion_params["transition_frames"] = self.transition_frames
        
        return final_motion_detected, motion_params

class MultiFrameBackgroundSubtractor:
    """Multi-frame background subtraction optimized for small objects"""
    
    def __init__(self, history_length=5, learning_rate=0.1):
        self.history_length = history_length
        self.learning_rate = learning_rate
        self.frame_history = deque(maxlen=history_length)
        self.background_model = None
        
    def update_background(self, frame):
        """Update background model with new frame"""
        gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) if len(frame.shape) == 3 else frame
        gray_frame = cv2.GaussianBlur(gray_frame, (3, 3), 0)
        
        self.frame_history.append(gray_frame.astype(np.float32))
        
        if len(self.frame_history) >= 3:
            # Use median for stable background
            frames_array = np.array(list(self.frame_history))
            self.background_model = np.median(frames_array, axis=0).astype(np.uint8)
        
        return self.background_model is not None
    
    def get_foreground_mask(self, current_frame, threshold=20):
        """Get foreground mask with enhanced small object detection"""
        if self.background_model is None:
            return None
        
        gray_frame = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY) if len(current_frame.shape) == 3 else current_frame
        gray_frame = cv2.GaussianBlur(gray_frame, (3, 3), 0)
        
        # Compute difference
        diff = cv2.absdiff(gray_frame, self.background_model)
        
        # Apply threshold
        _, mask = cv2.threshold(diff, threshold, 255, cv2.THRESH_BINARY)
        
        # Optimized morphological operations for small objects
        kernel_tiny = np.ones((2, 2), np.uint8)
        kernel_small = np.ones((3, 3), np.uint8)
        
        # Remove noise but preserve small objects
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel_tiny)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel_small)
        
        return mask

def detect_birds_responsive(input_path, output_path, threshold=18, min_area=25, max_area=400, 
                           motion_sensitivity=1.0, csv_path=None):
    """
    Responsive bird detection with immediate camera motion response.
    """
    
    # Open the input video
    cap = cv2.VideoCapture(input_path)
    
    if not cap.isOpened():
        print(f"Error: Could not open video file {input_path}")
        return False
    
    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"Processing video: {width}x{height} @ {fps}fps, {total_frames} frames")
    print(f"Responsive bird detection: area {min_area}-{max_area} pixels, threshold {threshold}")
    print(f"Motion sensitivity: {motion_sensitivity}")
    
    # Define the codec and create VideoWriter object
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    # Initialize detectors
    motion_detector = ResponsiveCameraMotionDetector(motion_sensitivity, temporal_window=3)
    motion_detector.initialize((height, width))
    
    bg_subtractor = MultiFrameBackgroundSubtractor(history_length=5, learning_rate=0.1)
    
    # Prepare CSV writer
    csv_file = None
    csv_writer = None
    if csv_path:
        csv_file = open(csv_path, 'w', newline='')
        csv_writer = csv.writer(csv_file)
        csv_writer.writerow(['frame_number', 'timestamp', 'camera_moving', 'bird_count', 'detections', 'motion_info'])
    
    # Read first frame
    ret, prev_frame = cap.read()
    if not ret:
        print("Error: Could not read first frame")
        return False
    
    # Initialize
    bg_subtractor.update_background(prev_frame)
    prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)
    
    frame_count = 0
    camera_moving_frames = 0
    bird_detection_frames = 0
    
    while True:
        ret, current_frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        timestamp = cap.get(cv2.CAP_PROP_POS_MSEC) / 1000.0
        
        # Update background model
        bg_ready = bg_subtractor.update_background(current_frame)
        
        # Convert current frame to grayscale
        current_gray = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY)
        
        # Check for camera movement with responsive detection
        is_camera_moving, motion_params = motion_detector.detect_camera_motion(prev_gray, current_gray)
        
        bird_detections = []
        
        if is_camera_moving:
            camera_moving_frames += 1
            detection_status = "CAMERA_MOVING"
            if motion_params.get("immediate", False):
                detection_status = "IMMEDIATE_MOTION"
        elif not bg_ready:
            detection_status = "INITIALIZING_BG"
        else:
            bird_detection_frames += 1
            detection_status = "DETECTING_BIRDS"
            
            # Get foreground mask
            foreground_mask = bg_subtractor.get_foreground_mask(current_frame, threshold)
            
            if foreground_mask is not None:
                # Find contours
                contours, _ = cv2.findContours(foreground_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                # Filter contours for bird-like objects
                for contour in contours:
                    area = cv2.contourArea(contour)
                    if min_area <= area <= max_area:
                        x, y, w, h = cv2.boundingRect(contour)
                        aspect_ratio = w / h if h > 0 else 0
                        
                        if 0.2 <= aspect_ratio <= 5.0:
                            perimeter = cv2.arcLength(contour, True)
                            if perimeter > 0:
                                compactness = 4 * np.pi * area / (perimeter * perimeter)
                                if compactness > 0.15:
                                    bird_detections.append({
                                        'bbox': (x, y, w, h),
                                        'area': area,
                                        'center': (x + w//2, y + h//2),
                                        'compactness': compactness
                                    })
        
        # Create output frame
        output_frame = current_frame.copy()
        
        # Draw bounding boxes for detected birds
        if detection_status == "DETECTING_BIRDS":
            for i, detection in enumerate(bird_detections):
                x, y, w, h = detection['bbox']
                area = detection['area']
                center_x, center_y = detection['center']
                
                # Color code by size
                if area <= 80:
                    color = (0, 255, 0)  # Green for small objects (likely birds)
                elif area <= 150:
                    color = (0, 255, 255)  # Yellow for medium objects
                else:
                    color = (0, 0, 255)  # Red for large objects
                
                cv2.rectangle(output_frame, (x, y), (x + w, y + h), color, 2)
                cv2.circle(output_frame, (center_x, center_y), 2, (255, 0, 255), -1)
                
                label = f"B{i+1}:{area}px"
                cv2.putText(output_frame, label, (x, y-5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
        
        # Add status overlay
        status_color = (0, 0, 255) if is_camera_moving else (0, 255, 0)
        if motion_params.get("immediate", False):
            status_color = (255, 0, 255)  # Magenta for immediate motion
        
        cv2.putText(output_frame, f"Status: {detection_status}", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, status_color, 2)
        cv2.putText(output_frame, f"Frame: {frame_count}/{total_frames}", 
                   (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(output_frame, f"Birds: {len(bird_detections)}", 
                   (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(output_frame, f"Time: {timestamp:.1f}s", 
                   (10, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        # Show motion parameters
        if is_camera_moving:
            conf = motion_params.get("confidence", 0)
            temp_conf = motion_params.get("temporal_confidence", 0)
            trans_frames = motion_params.get("transition_frames", 0)
            zoom_change = abs(motion_params["zoom"] - 1.0) * 100
            pan_mag = np.sqrt(motion_params["pan_x"]**2 + motion_params["pan_y"]**2)
            rot_deg = abs(np.degrees(motion_params["rotation"]))
            
            cv2.putText(output_frame, f"Motion: Z:{zoom_change:.1f}% P:{pan_mag:.1f}px R:{rot_deg:.1f}°", 
                       (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
            cv2.putText(output_frame, f"Conf: {conf:.2f} TC: {temp_conf:.2f} TF: {trans_frames}", 
                       (10, 180), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
            
            if motion_params.get("immediate", False):
                cv2.putText(output_frame, "IMMEDIATE MOTION DETECTED", 
                           (10, 210), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 255), 2)
        
        # Record detection data to CSV
        if csv_writer:
            detection_data = []
            for i, detection in enumerate(bird_detections):
                x, y, w, h = detection['bbox']
                area = detection['area']
                comp = detection['compactness']
                detection_data.append(f"B{i+1}:({x},{y},{w},{h},{area},{comp:.2f})")
            
            motion_info = f"Z:{motion_params['zoom']:.3f},P:{motion_params['pan_x']:.1f},{motion_params['pan_y']:.1f},R:{motion_params['rotation']:.3f},C:{motion_params.get('confidence', 0):.2f},TC:{motion_params.get('temporal_confidence', 0):.2f},TF:{motion_params.get('transition_frames', 0)},IM:{motion_params.get('immediate', False)}"
            
            csv_writer.writerow([
                frame_count,
                f"{timestamp:.3f}",
                is_camera_moving,
                len(bird_detections),
                "|".join(detection_data) if detection_data else "None",
                motion_info
            ])
        
        # Write the frame
        out.write(output_frame)
        
        # Update previous frame
        prev_gray = current_gray.copy()
        
        # Progress indicator
        if frame_count % 100 == 0 or frame_count == total_frames:
            progress = (frame_count / total_frames) * 100
            print(f"Progress: {progress:.1f}% ({frame_count}/{total_frames}) | "
                  f"Birds: {len(bird_detections)} | Camera moving: {is_camera_moving}")
    
    # Release everything
    cap.release()
    out.release()
    if csv_file:
        csv_file.close()
    cv2.destroyAllWindows()
    
    print(f"Responsive bird detection video saved to: {output_path}")
    print(f"Camera moving frames: {camera_moving_frames}/{frame_count} ({camera_moving_frames/frame_count*100:.1f}%)")
    print(f"Bird detection frames: {bird_detection_frames}/{frame_count} ({bird_detection_frames/frame_count*100:.1f}%)")
    if csv_path:
        print(f"Detection data saved to: {csv_path}")
    return True

def main():
    parser = argparse.ArgumentParser(description='Responsive bird detection with immediate motion response')
    parser.add_argument('input', help='Input video file path')
    parser.add_argument('output', help='Output video file path')
    parser.add_argument('--threshold', type=int, default=18,
                       help='Motion detection threshold (0-255, default: 18)')
    parser.add_argument('--min-area', type=int, default=25,
                       help='Minimum bird area in pixels (default: 25)')
    parser.add_argument('--max-area', type=int, default=400,
                       help='Maximum bird area in pixels (default: 400)')
    parser.add_argument('--motion-sensitivity', type=float, default=1.0,
                       help='Camera motion sensitivity (default: 1.0)')
    parser.add_argument('--csv', help='Optional path to save detection data as CSV')
    
    args = parser.parse_args()
    
    # Check if input file exists
    if not os.path.exists(args.input):
        print(f"Error: Input file '{args.input}' does not exist")
        return
    
    # Create output directory if it doesn't exist
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Create CSV directory if specified
    if args.csv:
        csv_dir = os.path.dirname(args.csv) or '.'
        if not os.path.exists(csv_dir):
            os.makedirs(csv_dir)
    
    print(f"Input: {args.input}")
    print(f"Output: {args.output}")
    if args.csv:
        print(f"CSV Output: {args.csv}")
    
    success = detect_birds_responsive(
        args.input, args.output, args.threshold, 
        args.min_area, args.max_area, args.motion_sensitivity, args.csv
    )
    
    if success:
        print("Responsive bird detection completed successfully!")
    else:
        print("Responsive bird detection failed!")

if __name__ == "__main__":
    main()
