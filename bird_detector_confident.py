import cv2
import numpy as np
import argparse
import os
import csv
from collections import deque
from datetime import datetime

class ConfidentBirdTracker:
    """Temporal confidence building to delay detections until confident they are real birds"""
    
    def __init__(self, confidence_frames=5, confidence_threshold=0.6, movement_threshold=5.0):
        self.confidence_frames = confidence_frames  # Frames to build confidence
        self.confidence_threshold = confidence_threshold  # Minimum confidence to report
        self.movement_threshold = movement_threshold  # Minimum movement for confidence
        
        # Track detection candidates over time
        self.candidate_history = deque(maxlen=confidence_frames)
        self.confirmed_detections = []
        
    def add_frame_detections(self, frame_number, detections):
        """Add current frame detections to confidence tracking"""
        detection_centers = []
        for detection in detections:
            x, y, w, h = detection['bbox']
            center_x, center_y = x + w//2, y + h//2
            detection_centers.append({
                'center': (center_x, center_y),
                'area': detection['area'],
                'detection': detection
            })
        
        self.candidate_history.append({
            'frame': frame_number,
            'detections': detection_centers
        })
    
    def get_confident_detections(self):
        """Get detections that have built sufficient confidence over time"""
        if len(self.candidate_history) < self.confidence_frames:
            return []  # Not enough history yet
        
        # Get current frame detections
        current_frame = self.candidate_history[-1]
        current_detections = current_frame['detections']
        
        confident_detections = []
        
        for current_det in current_detections:
            current_center = current_det['center']
            
            # Track this detection across previous frames
            confidence_score = 0
            movement_evidence = 0
            tracking_count = 0
            
            # Look for this detection in previous frames
            for i, hist_frame in enumerate(list(self.candidate_history)[:-1]):  # Exclude current frame
                closest_distance = float('inf')
                closest_detection = None
                
                # Find closest detection in this historical frame
                for hist_det in hist_frame['detections']:
                    hist_center = hist_det['center']
                    distance = np.sqrt((current_center[0] - hist_center[0])**2 + 
                                     (current_center[1] - hist_center[1])**2)
                    
                    if distance < closest_distance and distance < 25:  # Within tracking range
                        closest_distance = distance
                        closest_detection = hist_det
                
                if closest_detection is not None:
                    tracking_count += 1
                    
                    # Award confidence for consistent presence
                    confidence_score += 0.2
                    
                    # Award extra confidence for movement (indicates real bird)
                    if closest_distance > self.movement_threshold:
                        movement_evidence += 1
                        confidence_score += 0.3
                    
                    # Award confidence for size consistency
                    area_ratio = min(current_det['area'], closest_detection['area']) / max(current_det['area'], closest_detection['area'])
                    if area_ratio > 0.7:  # Similar size
                        confidence_score += 0.1
            
            # Calculate final confidence
            if tracking_count > 0:
                # Normalize confidence by number of frames tracked
                final_confidence = confidence_score / tracking_count
                
                # Bonus for movement evidence
                if movement_evidence >= 2:  # Moved in at least 2 frames
                    final_confidence += 0.2
                
                # Bonus for consistent tracking
                tracking_ratio = tracking_count / (len(self.candidate_history) - 1)
                if tracking_ratio > 0.6:  # Tracked in most frames
                    final_confidence += 0.1
                
                # Only report if confidence is high enough
                if final_confidence >= self.confidence_threshold:
                    detection_with_confidence = current_det['detection'].copy()
                    detection_with_confidence['confidence'] = final_confidence
                    detection_with_confidence['tracking_count'] = tracking_count
                    detection_with_confidence['movement_evidence'] = movement_evidence
                    confident_detections.append(detection_with_confidence)
        
        return confident_detections

class FrameAveragingDetector:
    """Bird detection using frame averaging and subtraction for clean movement detection"""
    
    def __init__(self, history_frames=10, learning_rate=0.1):
        self.history_frames = history_frames
        self.learning_rate = learning_rate
        
        # Frame history for averaging
        self.frame_buffer = deque(maxlen=history_frames)
        self.running_average = None
        
        # Detection parameters
        self.min_area = 8  # Very small for distant birds
        self.max_area = 300
        self.threshold = 15  # Difference threshold
        
    def update_average(self, frame):
        """Update running average of frames"""
        gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) if len(frame.shape) == 3 else frame
        
        # Apply slight blur to reduce noise
        gray_frame = cv2.GaussianBlur(gray_frame, (3, 3), 0)
        
        self.frame_buffer.append(gray_frame.astype(np.float32))
        
        if len(self.frame_buffer) >= 5:  # Need at least 5 frames
            # Calculate average of previous frames (excluding current)
            frames_array = np.array(list(self.frame_buffer)[:-1])  # Exclude current frame
            self.running_average = np.mean(frames_array, axis=0).astype(np.uint8)
            return True
        
        return False
    
    def detect_movement(self, current_frame):
        """Detect movement by subtracting average from current frame"""
        if self.running_average is None:
            return None, None
        
        gray_current = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY) if len(current_frame.shape) == 3 else current_frame
        gray_current = cv2.GaussianBlur(gray_current, (3, 3), 0)
        
        # Subtract average from current frame
        diff = cv2.absdiff(gray_current, self.running_average)
        
        # Apply threshold
        _, binary_diff = cv2.threshold(diff, self.threshold, 255, cv2.THRESH_BINARY)
        
        # Clean up noise with morphological operations
        kernel_small = np.ones((2, 2), np.uint8)
        kernel_medium = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        
        # Remove small noise
        binary_diff = cv2.morphologyEx(binary_diff, cv2.MORPH_OPEN, kernel_small, iterations=1)
        # Fill small gaps
        binary_diff = cv2.morphologyEx(binary_diff, cv2.MORPH_CLOSE, kernel_medium, iterations=1)
        
        return diff, binary_diff
    
    def find_bird_candidates(self, binary_mask):
        """Find bird candidates from binary mask"""
        if binary_mask is None:
            return []
        
        # Find contours
        contours, _ = cv2.findContours(binary_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        candidates = []
        for contour in contours:
            area = cv2.contourArea(contour)
            
            if self.min_area <= area <= self.max_area:
                x, y, w, h = cv2.boundingRect(contour)
                
                # Basic shape filtering
                aspect_ratio = w / h if h > 0 else 0
                if 0.2 <= aspect_ratio <= 5.0:  # Reasonable aspect ratio
                    
                    # Calculate compactness
                    perimeter = cv2.arcLength(contour, True)
                    if perimeter > 0:
                        compactness = 4 * np.pi * area / (perimeter * perimeter)
                        
                        if compactness > 0.1:  # Not too elongated
                            candidates.append({
                                'bbox': (x, y, w, h),
                                'area': area,
                                'center': (x + w//2, y + h//2),
                                'aspect_ratio': aspect_ratio,
                                'compactness': compactness,
                                'contour': contour
                            })
        
        return candidates

class ZoomAwareCameraMotionDetector:
    """Enhanced camera motion detector"""
    
    def __init__(self, motion_sensitivity=1.0, temporal_window=3):
        self.motion_sensitivity = motion_sensitivity
        self.temporal_window = temporal_window
        
        # Parameters for optical flow
        self.lk_params = dict(winSize=(15, 15),
                             maxLevel=2,
                             criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 20, 0.01))
        
        # Base motion thresholds
        self.base_zoom_threshold = 0.008
        self.base_pan_threshold = 1.5
        self.base_rotation_threshold = 0.015
        
        # Immediate response thresholds
        self.immediate_zoom_threshold = 0.02
        self.immediate_pan_threshold = 3.0
        self.immediate_rotation_threshold = 0.03
        
        # Zoom level tracking
        self.current_zoom_level = 1.0
        self.zoom_history = deque(maxlen=10)
        
        # Temporal filtering
        self.motion_history = deque(maxlen=temporal_window)
        self.motion_confidence_threshold = 0.5
        
        # Transition detection
        self.prev_motion_state = False
        self.transition_frames = 0
        self.max_transition_frames = 5  # Increased for more conservative transitions
        
        # Water/tide motion detection
        self.water_motion_threshold = 0.3
        
        # False positive detection
        self.high_detection_count_threshold = 30  # Lowered threshold
        self.zoom_shake_threshold = 1.015
        
        self.frame_width = None
        self.frame_height = None
        
    def initialize(self, frame_shape):
        """Initialize camera motion detector"""
        self.frame_height, self.frame_width = frame_shape
        
    def update_zoom_level(self, zoom_factor):
        """Update current zoom level"""
        self.zoom_history.append(zoom_factor)
        if len(self.zoom_history) >= 5:
            self.current_zoom_level = np.median(list(self.zoom_history))
        else:
            self.current_zoom_level = zoom_factor
            
    def get_adaptive_thresholds(self):
        """Get motion thresholds adapted to current zoom level"""
        zoom_factor = max(1.0, self.current_zoom_level)
        sensitivity_multiplier = 1.0 / zoom_factor
        
        adapted_pan_threshold = self.base_pan_threshold * sensitivity_multiplier
        adapted_rotation_threshold = self.base_rotation_threshold * sensitivity_multiplier
        
        return {
            'zoom': self.base_zoom_threshold,
            'pan': max(0.8, adapted_pan_threshold),
            'rotation': max(0.008, adapted_rotation_threshold),
            'immediate_pan': self.immediate_pan_threshold * sensitivity_multiplier,
            'immediate_rotation': self.immediate_rotation_threshold * sensitivity_multiplier
        }
    
    def detect_water_motion(self, flow_vectors):
        """Detect water/tide-like motion patterns"""
        if len(flow_vectors) < 10:
            return False
            
        # Calculate motion coherence
        mean_flow = np.mean(flow_vectors, axis=0)
        flow_deviations = flow_vectors - mean_flow
        flow_magnitudes = np.linalg.norm(flow_deviations, axis=1)
        
        # Water motion tends to be coherent and horizontal
        coherence = 1.0 - (np.std(flow_magnitudes) / (np.mean(flow_magnitudes) + 1e-6))
        horizontal_bias = abs(mean_flow[0]) / (abs(mean_flow[1]) + 1e-6)
        
        is_water_motion = (coherence > self.water_motion_threshold and 
                          horizontal_bias > 2.0 and 
                          np.linalg.norm(mean_flow) > 0.5)
        
        return is_water_motion

    def detect_camera_motion(self, old_gray, new_gray, bird_count=0):
        """Enhanced camera motion detection with more conservative transitions"""
        # Select stable sample points
        sample_points = []
        step = 50
        border = 60

        for y in range(border, self.frame_height - border, step):
            for x in range(border, self.frame_width - border, step):
                sample_points.append([x, y])

        if len(sample_points) < 6:
            motion_detected = False
            motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0, "immediate": False}
        else:
            sample_points = np.array(sample_points, dtype=np.float32).reshape(-1, 1, 2)

            # Calculate optical flow
            new_points, status, error = cv2.calcOpticalFlowPyrLK(
                old_gray, new_gray, sample_points, None, **self.lk_params)

            # Filter good points
            good_mask = (status == 1) & (error < 15)

            if np.sum(good_mask) < 6:
                motion_detected = False
                motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0, "immediate": False}
            else:
                old_pts = sample_points[good_mask.flatten()].reshape(-1, 2)
                new_pts = new_points[good_mask.flatten()].reshape(-1, 2)

                # Calculate flow vectors for water detection
                flow_vectors = new_pts - old_pts
                is_water_motion = self.detect_water_motion(flow_vectors)

                # Calculate transformation with RANSAC
                try:
                    transform_result = cv2.estimateAffinePartial2D(
                        old_pts, new_pts, method=cv2.RANSAC,
                        ransacReprojThreshold=1.5, maxIters=1000, confidence=0.95)

                    transform_matrix = transform_result[0]
                    inlier_mask = transform_result[1]

                    if transform_matrix is None or inlier_mask is None:
                        motion_detected = False
                        motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0, "immediate": False}
                    else:
                        # Calculate confidence
                        confidence = np.sum(inlier_mask) / len(inlier_mask) if len(inlier_mask) > 0 else 0

                        # Extract motion parameters
                        a = transform_matrix[0, 0]
                        b = transform_matrix[0, 1]
                        tx = transform_matrix[0, 2]
                        ty = transform_matrix[1, 2]

                        scale = np.sqrt(a*a + b*b)
                        rotation = np.arctan2(b, a)
                        pan_x = tx * self.motion_sensitivity
                        pan_y = ty * self.motion_sensitivity

                        # Update zoom level tracking
                        self.update_zoom_level(scale)

                        # Get adaptive thresholds
                        thresholds = self.get_adaptive_thresholds()

                        motion_params = {
                            "zoom": scale,
                            "pan_x": pan_x,
                            "pan_y": pan_y,
                            "rotation": rotation,
                            "confidence": confidence,
                            "immediate": False,
                            "zoom_level": self.current_zoom_level,
                            "water_motion": is_water_motion
                        }

                        # Check for immediate response (strong motion)
                        zoom_change = abs(scale - 1.0)
                        pan_magnitude = np.sqrt(pan_x*pan_x + pan_y*pan_y)
                        rotation_magnitude = abs(rotation)

                        immediate_motion = (confidence > 0.6 and (
                            zoom_change > self.immediate_zoom_threshold or
                            pan_magnitude > thresholds['immediate_pan'] or
                            rotation_magnitude > thresholds['immediate_rotation']))

                        # Check for normal motion with adaptive thresholds
                        normal_motion = (confidence > 0.7 and (
                            zoom_change > thresholds['zoom'] or
                            pan_magnitude > thresholds['pan'] or
                            rotation_magnitude > thresholds['rotation']))

                        # Water motion override
                        if is_water_motion and confidence > 0.5:
                            motion_detected = True
                            motion_params["immediate"] = False
                            motion_params["water_motion_detected"] = True
                        # High zoom + many detections = likely false positive from shake
                        elif (self.current_zoom_level > self.zoom_shake_threshold and
                              bird_count > self.high_detection_count_threshold):
                            motion_detected = True
                            motion_params["immediate"] = True
                            motion_params["false_positive_suppression"] = True
                        else:
                            motion_detected = immediate_motion or normal_motion
                            motion_params["immediate"] = immediate_motion
                            motion_params["false_positive_suppression"] = False
                            motion_params["water_motion_detected"] = False

                except Exception as e:
                    motion_detected = False
                    motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0, "immediate": False}

        # More conservative transition handling
        if motion_detected != self.prev_motion_state:
            self.transition_frames = 0
            if motion_detected:
                # Starting motion - be more conservative
                if (motion_params.get("immediate", False) or
                    motion_params.get("water_motion_detected", False) or
                    motion_params.get("false_positive_suppression", False)):
                    final_motion_detected = True
                else:
                    # Require more evidence for motion start
                    self.motion_history.append(motion_detected)
                    motion_ratio = sum(self.motion_history) / len(self.motion_history)
                    final_motion_detected = motion_ratio >= 0.7  # Higher threshold
            else:
                # Stopping motion - be even more conservative
                self.motion_history.append(motion_detected)
                if len(self.motion_history) >= 3:  # Require more frames
                    motion_ratio = sum(self.motion_history) / len(self.motion_history)
                    final_motion_detected = motion_ratio >= 0.3  # Lower threshold for stopping
                else:
                    final_motion_detected = self.prev_motion_state
        else:
            # Same state as before
            self.transition_frames += 1
            self.motion_history.append(motion_detected)

            if self.transition_frames < self.max_transition_frames:
                # Extended transition period
                if motion_detected and (motion_params.get("immediate", False) or
                                      motion_params.get("water_motion_detected", False) or
                                      motion_params.get("false_positive_suppression", False)):
                    final_motion_detected = True
                else:
                    final_motion_detected = self.prev_motion_state
            else:
                # Past transition period - use temporal filtering
                motion_ratio = sum(self.motion_history) / len(self.motion_history)
                final_motion_detected = motion_ratio >= self.motion_confidence_threshold

        # Update state
        self.prev_motion_state = final_motion_detected

        motion_params["temporal_confidence"] = sum(self.motion_history) / len(self.motion_history) if self.motion_history else 0
        motion_params["transition_frames"] = self.transition_frames

        return final_motion_detected, motion_params

def detect_birds_confident(input_path, output_path, threshold=15, motion_sensitivity=1.0, csv_path=None):
    """
    Confident bird detection with temporal confidence building to delay detections.
    """

    # Open the input video
    cap = cv2.VideoCapture(input_path)

    if not cap.isOpened():
        print(f"Error: Could not open video file {input_path}")
        return False

    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    print(f"Processing video: {width}x{height} @ {fps}fps, {total_frames} frames")
    print(f"Confident bird detection: building temporal confidence over 5 frames")
    print(f"Delayed detection to reduce camera movement false positives")

    # Define the codec and create VideoWriter object
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

    # Initialize detectors
    motion_detector = ZoomAwareCameraMotionDetector(motion_sensitivity, temporal_window=5)  # Increased window
    motion_detector.initialize((height, width))

    frame_detector = FrameAveragingDetector(history_frames=10, learning_rate=0.1)
    frame_detector.threshold = threshold

    # Initialize confident tracker
    confident_tracker = ConfidentBirdTracker(
        confidence_frames=5,      # Build confidence over 5 frames
        confidence_threshold=0.6, # Require 60% confidence
        movement_threshold=5.0    # Require 5 pixel movement
    )

    # Prepare CSV writer
    csv_file = None
    csv_writer = None
    if csv_path:
        csv_file = open(csv_path, 'w', newline='')
        csv_writer = csv.writer(csv_file)
        csv_writer.writerow(['frame_number', 'timestamp', 'camera_moving', 'candidate_count', 'confident_count', 'detections', 'motion_info'])

    # Read first frame
    ret, prev_frame = cap.read()
    if not ret:
        print("Error: Could not read first frame")
        return False

    # Initialize
    frame_detector.update_average(prev_frame)
    prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)

    frame_count = 0
    camera_moving_frames = 0
    bird_detection_frames = 0
    water_motion_frames = 0
    total_candidates = 0
    total_confident = 0

    while True:
        ret, current_frame = cap.read()
        if not ret:
            break

        frame_count += 1
        timestamp = cap.get(cv2.CAP_PROP_POS_MSEC) / 1000.0

        # Convert current frame to grayscale
        current_gray = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY)

        # Update frame averaging
        avg_ready = frame_detector.update_average(current_frame)

        # Preliminary bird detection for motion detector feedback
        preliminary_bird_count = 0
        if avg_ready:
            _, binary_mask = frame_detector.detect_movement(current_frame)
            if binary_mask is not None:
                preliminary_candidates = frame_detector.find_bird_candidates(binary_mask)
                preliminary_bird_count = len(preliminary_candidates)

        # Check for camera movement
        is_camera_moving, motion_params = motion_detector.detect_camera_motion(
            prev_gray, current_gray, preliminary_bird_count)

        candidate_detections = []
        confident_detections = []

        if is_camera_moving:
            camera_moving_frames += 1
            if motion_params.get("water_motion_detected", False):
                detection_status = "WATER_MOTION"
                water_motion_frames += 1
            elif motion_params.get("false_positive_suppression", False):
                detection_status = "SHAKE_SUPPRESSION"
            elif motion_params.get("immediate", False):
                detection_status = "IMMEDIATE_MOTION"
            else:
                detection_status = "CAMERA_MOVING"
        elif not avg_ready:
            detection_status = "BUILDING_AVERAGE"
        else:
            bird_detection_frames += 1
            detection_status = "CONFIDENT_DETECTING"

            # Detect movement using frame averaging
            diff_frame, binary_mask = frame_detector.detect_movement(current_frame)

            if binary_mask is not None:
                # Find candidate detections
                candidate_detections = frame_detector.find_bird_candidates(binary_mask)
                total_candidates += len(candidate_detections)

                # Add candidates to confidence tracker
                confident_tracker.add_frame_detections(frame_count, candidate_detections)

                # Get confident detections (delayed)
                confident_detections = confident_tracker.get_confident_detections()
                total_confident += len(confident_detections)

        # Create output frame
        output_frame = current_frame.copy()

        # Draw detection results
        if detection_status == "CONFIDENT_DETECTING":
            # Draw candidate detections in light colors (unconfirmed)
            for i, detection in enumerate(candidate_detections):
                x, y, w, h = detection['bbox']
                area = detection['area']

                # Light colors for candidates
                if area <= 20:
                    color = (128, 255, 128)  # Light green
                elif area <= 50:
                    color = (128, 255, 255)  # Light yellow
                else:
                    color = (128, 128, 255)  # Light red

                cv2.rectangle(output_frame, (x, y), (x + w, y + h), color, 1)
                cv2.putText(output_frame, f"C{i+1}", (x, y-5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)

            # Draw confident detections in bright colors (confirmed)
            for i, detection in enumerate(confident_detections):
                x, y, w, h = detection['bbox']
                area = detection['area']
                confidence = detection['confidence']
                tracking_count = detection['tracking_count']
                movement_evidence = detection['movement_evidence']

                # Bright colors for confident detections
                if area <= 20:
                    color = (0, 255, 0)  # Bright green
                elif area <= 50:
                    color = (0, 255, 255)  # Bright yellow
                else:
                    color = (0, 0, 255)  # Bright red

                cv2.rectangle(output_frame, (x, y), (x + w, y + h), color, 2)
                cv2.circle(output_frame, (x + w//2, y + h//2), 3, (255, 0, 255), -1)

                # Label with confidence info
                label = f"B{i+1}:{area}px C:{confidence:.2f}"
                cv2.putText(output_frame, label, (x, y-5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

                # Show tracking info for larger detections
                if area > 15:
                    track_label = f"T:{tracking_count} M:{movement_evidence}"
                    cv2.putText(output_frame, track_label, (x, y+h+15),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)

        # Add status overlay
        status_color = (0, 0, 255) if is_camera_moving else (0, 255, 0)
        if motion_params.get("water_motion_detected", False):
            status_color = (255, 165, 0)  # Orange for water motion
        elif motion_params.get("false_positive_suppression", False):
            status_color = (255, 0, 255)  # Magenta for shake suppression
        elif motion_params.get("immediate", False):
            status_color = (255, 255, 0)  # Cyan for immediate motion

        cv2.putText(output_frame, f"Status: {detection_status}",
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, status_color, 2)
        cv2.putText(output_frame, f"Frame: {frame_count}/{total_frames}",
                   (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(output_frame, f"Candidates: {len(candidate_detections)} | Confident: {len(confident_detections)}",
                   (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(output_frame, f"Time: {timestamp:.1f}s",
                   (10, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        # Show confidence building info
        if detection_status == "CONFIDENT_DETECTING":
            confidence_buffer = len(confident_tracker.candidate_history)
            cv2.putText(output_frame, f"Confidence Buffer: {confidence_buffer}/5 frames",
                       (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            cv2.putText(output_frame, f"Threshold: {frame_detector.threshold} | Min Conf: 0.6",
                       (10, 180), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        # Show motion parameters
        if is_camera_moving:
            conf = motion_params.get("confidence", 0)
            temp_conf = motion_params.get("temporal_confidence", 0)
            transition_frames = motion_params.get("transition_frames", 0)

            cv2.putText(output_frame, f"Motion Conf: {conf:.2f} TC: {temp_conf:.2f} TF: {transition_frames}",
                       (10, 210), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)

            if motion_params.get("water_motion_detected", False):
                cv2.putText(output_frame, "WATER/TIDE MOTION DETECTED",
                           (10, 240), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 165, 0), 2)
            elif motion_params.get("false_positive_suppression", False):
                cv2.putText(output_frame, "SHAKE SUPPRESSION ACTIVE",
                           (10, 240), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 255), 2)

        # Record detection data to CSV
        if csv_writer:
            detection_data = []
            for i, detection in enumerate(confident_detections):
                x, y, w, h = detection['bbox']
                area = detection['area']
                confidence = detection['confidence']
                tracking_count = detection['tracking_count']
                movement_evidence = detection['movement_evidence']
                detection_data.append(f"B{i+1}:({x},{y},{w},{h},{area},{confidence:.3f},{tracking_count},{movement_evidence})")

            motion_info = f"Z:{motion_params['zoom']:.3f},P:{motion_params['pan_x']:.1f},{motion_params['pan_y']:.1f},R:{motion_params['rotation']:.3f},C:{motion_params.get('confidence', 0):.2f},TC:{motion_params.get('temporal_confidence', 0):.2f},TF:{motion_params.get('transition_frames', 0)},WM:{motion_params.get('water_motion_detected', False)},FPS:{motion_params.get('false_positive_suppression', False)},CB:{len(confident_tracker.candidate_history)}"

            csv_writer.writerow([
                frame_count,
                f"{timestamp:.3f}",
                is_camera_moving,
                len(candidate_detections),
                len(confident_detections),
                "|".join(detection_data) if detection_data else "None",
                motion_info
            ])

        # Write the frame
        out.write(output_frame)

        # Update previous frame
        prev_gray = current_gray.copy()

        # Progress indicator
        if frame_count % 100 == 0 or frame_count == total_frames:
            progress = (frame_count / total_frames) * 100
            print(f"Progress: {progress:.1f}% ({frame_count}/{total_frames}) | "
                  f"Candidates: {len(candidate_detections)} | Confident: {len(confident_detections)} | Status: {detection_status}")

    # Release everything
    cap.release()
    out.release()
    if csv_file:
        csv_file.close()
    cv2.destroyAllWindows()

    print(f"Confident bird detection video saved to: {output_path}")
    print(f"Camera moving frames: {camera_moving_frames}/{frame_count} ({camera_moving_frames/frame_count*100:.1f}%)")
    print(f"Water motion frames: {water_motion_frames}/{frame_count} ({water_motion_frames/frame_count*100:.1f}%)")
    print(f"Bird detection frames: {bird_detection_frames}/{frame_count} ({bird_detection_frames/frame_count*100:.1f}%)")
    print(f"Total candidates: {total_candidates}, Total confident: {total_confident}")
    if total_candidates > 0:
        confidence_ratio = total_confident / total_candidates * 100
        print(f"Confidence ratio: {confidence_ratio:.1f}% (confident/candidates)")
    if csv_path:
        print(f"Detection data saved to: {csv_path}")
    return True

def main():
    parser = argparse.ArgumentParser(description='Confident bird detection with temporal confidence building')
    parser.add_argument('input', help='Input video file path')
    parser.add_argument('output', help='Output video file path')
    parser.add_argument('--threshold', type=int, default=15,
                       help='Difference threshold for movement detection (default: 15)')
    parser.add_argument('--motion-sensitivity', type=float, default=1.0,
                       help='Camera motion sensitivity (default: 1.0)')
    parser.add_argument('--csv', help='Optional path to save detection data as CSV')

    args = parser.parse_args()

    # Check if input file exists
    if not os.path.exists(args.input):
        print(f"Error: Input file '{args.input}' does not exist")
        return

    # Create output directory if it doesn't exist
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Create CSV directory if specified
    if args.csv:
        csv_dir = os.path.dirname(args.csv) or '.'
        if not os.path.exists(csv_dir):
            os.makedirs(csv_dir)

    print(f"Input: {args.input}")
    print(f"Output: {args.output}")
    if args.csv:
        print(f"CSV Output: {args.csv}")

    success = detect_birds_confident(
        args.input, args.output, args.threshold, args.motion_sensitivity, args.csv
    )

    if success:
        print("Confident bird detection completed successfully!")
    else:
        print("Confident bird detection failed!")

if __name__ == "__main__":
    main()
