[YOLO]
# YOLO confidence threshold (0.0-1.0)
# Lower values = more detections but more false positives
# Higher values = fewer detections but higher quality
# Previous tries: 0.3 (too many), 0.45 (too few)
# Balanced default: 0.375 (middle ground)
confidence_threshold = 0.375

[BACKGROUND]
# Background subtraction threshold (1-50)
# Lower values = more sensitive detection
# Higher values = less sensitive but cleaner
# Previous tries: 15 (too high), 8 (very sensitive)
# Balanced default: 12 (middle ground)
difference_threshold = 12

[PID_TRACKING]
# PID controller gains for smooth tracking
proportional_gain = 0.8
integral_gain = 0.1
derivative_gain = 0.3

# Tracking parameters
max_tracking_distance = 50
min_track_confidence = 0.3
confidence_frames = 5
movement_threshold = 5.0

[CAMERA_MOTION]
# Camera motion detection sensitivity
motion_sensitivity = 1.0
temporal_window = 5

# Movement delay frames
movement_start_delay = 5
movement_end_delay = 10

[DETECTION]
# Background subtraction parameters
history_frames = 10
min_area = 6
max_area = 400

# Morphological operations
open_iterations = 1
close_iterations = 1

[OUTPUT]
# Video output settings
fourcc = mp4v
show_tracking_info = true
show_quality_metrics = true
show_prediction_info = true

# Color coding for different detection sources
yolo_color = [0, 255, 0]        # Green
background_color = [255, 255, 0] # Cyan
predicted_color = [255, 0, 255]  # Magenta
low_quality_color = [0, 0, 255] # Red
