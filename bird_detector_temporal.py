import cv2
import numpy as np
import argparse
import os
import csv
from collections import deque
from datetime import datetime

class TemporalBirdTracker:
    """Temporal tracking to filter out static false positives"""
    
    def __init__(self, history_frames=5, movement_threshold=3.0, consistency_threshold=0.6):
        self.history_frames = history_frames
        self.movement_threshold = movement_threshold  # Minimum movement in pixels
        self.consistency_threshold = consistency_threshold  # Minimum ratio of frames with movement
        
        # Store detection history: {frame_number: [(x, y, w, h, area), ...]}
        self.detection_history = deque(maxlen=history_frames)
        
    def add_detections(self, frame_number, detections):
        """Add current frame detections to history"""
        detection_centers = []
        for detection in detections:
            x, y, w, h = detection['bbox']
            center_x, center_y = x + w//2, y + h//2
            detection_centers.append((center_x, center_y, detection['area']))
        
        self.detection_history.append({
            'frame': frame_number,
            'detections': detection_centers
        })
    
    def filter_static_detections(self, current_detections):
        """Filter out detections that appear static across multiple frames"""
        if len(self.detection_history) < 3:  # Need at least 3 frames for comparison
            return current_detections
        
        filtered_detections = []
        
        for detection in current_detections:
            x, y, w, h = detection['bbox']
            current_center = (x + w//2, y + h//2)
            
            # Check if this detection shows movement compared to previous frames
            movement_count = 0
            total_comparisons = 0
            
            for hist_frame in list(self.detection_history)[-3:]:  # Check last 3 frames
                closest_distance = float('inf')
                
                # Find closest detection in historical frame
                for hist_center_x, hist_center_y, hist_area in hist_frame['detections']:
                    distance = np.sqrt((current_center[0] - hist_center_x)**2 + 
                                     (current_center[1] - hist_center_y)**2)
                    closest_distance = min(closest_distance, distance)
                
                # If there was a detection nearby, check if it moved enough
                if closest_distance < 20:  # Within reasonable tracking distance
                    total_comparisons += 1
                    if closest_distance > self.movement_threshold:
                        movement_count += 1
            
            # Keep detection if it shows sufficient movement or is new
            if total_comparisons == 0:  # New detection
                filtered_detections.append(detection)
            elif movement_count / total_comparisons >= self.consistency_threshold:
                filtered_detections.append(detection)
            # Else: likely static false positive, filter out
        
        return filtered_detections

class ZoomAwareCameraMotionDetector:
    """Enhanced camera motion detector with zoom-aware thresholds and water motion filtering"""
    
    def __init__(self, motion_sensitivity=1.0, temporal_window=3):
        self.motion_sensitivity = motion_sensitivity
        self.temporal_window = temporal_window
        
        # Parameters for optical flow
        self.lk_params = dict(winSize=(15, 15),
                             maxLevel=2,
                             criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 20, 0.01))
        
        # Base motion thresholds
        self.base_zoom_threshold = 0.008
        self.base_pan_threshold = 1.5
        self.base_rotation_threshold = 0.015
        
        # Immediate response thresholds
        self.immediate_zoom_threshold = 0.02
        self.immediate_pan_threshold = 3.0
        self.immediate_rotation_threshold = 0.03
        
        # Zoom level tracking and adaptive thresholds
        self.current_zoom_level = 1.0
        self.zoom_history = deque(maxlen=10)
        self.zoom_change_threshold = 0.005
        
        # Temporal filtering
        self.motion_history = deque(maxlen=temporal_window)
        self.motion_confidence_threshold = 0.5
        
        # Transition detection
        self.prev_motion_state = False
        self.transition_frames = 0
        self.max_transition_frames = 3
        
        # Water/tide motion detection
        self.water_motion_threshold = 0.3
        self.water_detection_enabled = True
        
        # False positive detection for high zoom
        self.high_detection_count_threshold = 50
        self.zoom_shake_threshold = 1.015
        
        self.frame_width = None
        self.frame_height = None
        
    def initialize(self, frame_shape):
        """Initialize camera motion detector"""
        self.frame_height, self.frame_width = frame_shape
        
    def update_zoom_level(self, zoom_factor):
        """Update current zoom level and detect zoom changes"""
        self.zoom_history.append(zoom_factor)
        if len(self.zoom_history) >= 5:
            self.current_zoom_level = np.median(list(self.zoom_history))
        else:
            self.current_zoom_level = zoom_factor
            
    def get_adaptive_thresholds(self):
        """Get motion thresholds adapted to current zoom level"""
        zoom_factor = max(1.0, self.current_zoom_level)
        sensitivity_multiplier = 1.0 / zoom_factor
        
        adapted_pan_threshold = self.base_pan_threshold * sensitivity_multiplier
        adapted_rotation_threshold = self.base_rotation_threshold * sensitivity_multiplier
        
        return {
            'zoom': self.base_zoom_threshold,
            'pan': max(0.8, adapted_pan_threshold),
            'rotation': max(0.008, adapted_rotation_threshold),
            'immediate_pan': self.immediate_pan_threshold * sensitivity_multiplier,
            'immediate_rotation': self.immediate_rotation_threshold * sensitivity_multiplier
        }
    
    def detect_water_motion(self, flow_vectors):
        """Detect water/tide-like motion patterns"""
        if len(flow_vectors) < 10:
            return False
            
        # Calculate motion coherence (how similar the motion vectors are)
        mean_flow = np.mean(flow_vectors, axis=0)
        flow_deviations = flow_vectors - mean_flow
        flow_magnitudes = np.linalg.norm(flow_deviations, axis=1)
        
        # Water motion tends to be coherent (low deviation) and horizontal
        coherence = 1.0 - (np.std(flow_magnitudes) / (np.mean(flow_magnitudes) + 1e-6))
        
        # Check if motion is predominantly horizontal (water/tide characteristic)
        horizontal_bias = abs(mean_flow[0]) / (abs(mean_flow[1]) + 1e-6)
        
        # Water motion detection criteria
        is_water_motion = (coherence > self.water_motion_threshold and 
                          horizontal_bias > 2.0 and 
                          np.linalg.norm(mean_flow) > 0.5)
        
        return is_water_motion
        
    def detect_camera_motion(self, old_gray, new_gray, bird_count=0):
        """Enhanced camera motion detection with water motion filtering"""
        # Select stable sample points
        sample_points = []
        step = 50
        border = 60
        
        for y in range(border, self.frame_height - border, step):
            for x in range(border, self.frame_width - border, step):
                sample_points.append([x, y])
        
        if len(sample_points) < 6:
            motion_detected = False
            motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0, "immediate": False}
        else:
            sample_points = np.array(sample_points, dtype=np.float32).reshape(-1, 1, 2)
            
            # Calculate optical flow
            new_points, status, error = cv2.calcOpticalFlowPyrLK(
                old_gray, new_gray, sample_points, None, **self.lk_params)
            
            # Filter good points
            good_mask = (status == 1) & (error < 15)
            
            if np.sum(good_mask) < 6:
                motion_detected = False
                motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0, "immediate": False}
            else:
                old_pts = sample_points[good_mask.flatten()].reshape(-1, 2)
                new_pts = new_points[good_mask.flatten()].reshape(-1, 2)
                
                # Calculate flow vectors for water detection
                flow_vectors = new_pts - old_pts
                is_water_motion = self.detect_water_motion(flow_vectors) if self.water_detection_enabled else False
                
                # Calculate transformation with RANSAC
                try:
                    transform_result = cv2.estimateAffinePartial2D(
                        old_pts, new_pts, method=cv2.RANSAC, 
                        ransacReprojThreshold=1.5, maxIters=1000, confidence=0.95)
                    
                    transform_matrix = transform_result[0]
                    inlier_mask = transform_result[1]
                    
                    if transform_matrix is None or inlier_mask is None:
                        motion_detected = False
                        motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0, "immediate": False}
                    else:
                        # Calculate confidence
                        confidence = np.sum(inlier_mask) / len(inlier_mask) if len(inlier_mask) > 0 else 0
                        
                        # Extract motion parameters
                        a = transform_matrix[0, 0]
                        b = transform_matrix[0, 1]
                        tx = transform_matrix[0, 2]
                        ty = transform_matrix[1, 2]
                        
                        scale = np.sqrt(a*a + b*b)
                        rotation = np.arctan2(b, a)
                        pan_x = tx * self.motion_sensitivity
                        pan_y = ty * self.motion_sensitivity
                        
                        # Update zoom level tracking
                        self.update_zoom_level(scale)
                        
                        # Get adaptive thresholds
                        thresholds = self.get_adaptive_thresholds()
                        
                        motion_params = {
                            "zoom": scale,
                            "pan_x": pan_x,
                            "pan_y": pan_y,
                            "rotation": rotation,
                            "confidence": confidence,
                            "immediate": False,
                            "zoom_level": self.current_zoom_level,
                            "water_motion": is_water_motion
                        }
                        
                        # Check for immediate response (strong motion)
                        zoom_change = abs(scale - 1.0)
                        pan_magnitude = np.sqrt(pan_x*pan_x + pan_y*pan_y)
                        rotation_magnitude = abs(rotation)
                        
                        immediate_motion = (confidence > 0.6 and (
                            zoom_change > self.immediate_zoom_threshold or 
                            pan_magnitude > thresholds['immediate_pan'] or 
                            rotation_magnitude > thresholds['immediate_rotation']))
                        
                        # Check for normal motion with adaptive thresholds
                        normal_motion = (confidence > 0.7 and (
                            zoom_change > thresholds['zoom'] or 
                            pan_magnitude > thresholds['pan'] or 
                            rotation_magnitude > thresholds['rotation']))
                        
                        # Water motion override - if detected, treat as camera motion to pause bird detection
                        if is_water_motion and confidence > 0.5:
                            motion_detected = True
                            motion_params["immediate"] = False
                            motion_params["water_motion_detected"] = True
                        # High zoom + many detections = likely false positive from shake
                        elif (self.current_zoom_level > self.zoom_shake_threshold and 
                              bird_count > self.high_detection_count_threshold):
                            motion_detected = True
                            motion_params["immediate"] = True
                            motion_params["false_positive_suppression"] = True
                        else:
                            motion_detected = immediate_motion or normal_motion
                            motion_params["immediate"] = immediate_motion
                            motion_params["false_positive_suppression"] = False
                            motion_params["water_motion_detected"] = False
                        
                except Exception as e:
                    motion_detected = False
                    motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0, "immediate": False}
        
        # Handle transitions more responsively
        if motion_detected != self.prev_motion_state:
            self.transition_frames = 0
            if motion_detected:
                # Starting motion - immediate response if strong motion, water motion, or false positive suppression
                if (motion_params.get("immediate", False) or 
                    motion_params.get("water_motion_detected", False) or 
                    motion_params.get("false_positive_suppression", False)):
                    final_motion_detected = True
                else:
                    self.motion_history.append(motion_detected)
                    motion_ratio = sum(self.motion_history) / len(self.motion_history)
                    final_motion_detected = motion_ratio >= self.motion_confidence_threshold
            else:
                # Stopping motion - be more conservative
                self.motion_history.append(motion_detected)
                if len(self.motion_history) >= 2:
                    motion_ratio = sum(self.motion_history) / len(self.motion_history)
                    final_motion_detected = motion_ratio >= self.motion_confidence_threshold
                else:
                    final_motion_detected = self.prev_motion_state
        else:
            # Same state as before
            self.transition_frames += 1
            self.motion_history.append(motion_detected)
            
            if self.transition_frames < self.max_transition_frames:
                # Still in transition period
                if motion_detected and (motion_params.get("immediate", False) or 
                                      motion_params.get("water_motion_detected", False) or 
                                      motion_params.get("false_positive_suppression", False)):
                    final_motion_detected = True
                else:
                    final_motion_detected = self.prev_motion_state
            else:
                # Past transition period - use temporal filtering
                motion_ratio = sum(self.motion_history) / len(self.motion_history)
                final_motion_detected = motion_ratio >= self.motion_confidence_threshold
        
        # Update state
        self.prev_motion_state = final_motion_detected
        
        motion_params["temporal_confidence"] = sum(self.motion_history) / len(self.motion_history) if self.motion_history else 0
        motion_params["transition_frames"] = self.transition_frames
        
        return final_motion_detected, motion_params

class ZoomAdaptiveBackgroundSubtractor:
    """Enhanced background subtraction with zoom-adaptive detection and ultra-sensitive small object detection"""

    def __init__(self, history_length=7, learning_rate=0.03):
        self.history_length = history_length
        self.learning_rate = learning_rate
        self.frame_history = deque(maxlen=history_length)
        self.background_model = None
        self.background_variance = None
        self.current_zoom_level = 1.0

    def update_background(self, frame, zoom_level=1.0):
        """Update background model with zoom level awareness"""
        self.current_zoom_level = zoom_level

        gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) if len(frame.shape) == 3 else frame

        # Adaptive blur based on zoom level - less blur for higher zoom to preserve small details
        blur_kernel = max(3, int(5 / zoom_level))
        if blur_kernel % 2 == 0:
            blur_kernel += 1
        gray_frame = cv2.GaussianBlur(gray_frame, (blur_kernel, blur_kernel), 0)

        self.frame_history.append(gray_frame.astype(np.float32))

        if len(self.frame_history) >= 5:
            # Use median for stable background
            frames_array = np.array(list(self.frame_history))
            self.background_model = np.median(frames_array, axis=0).astype(np.uint8)

            # Calculate variance for adaptive thresholding
            self.background_variance = np.std(frames_array, axis=0).astype(np.uint8)

        return self.background_model is not None

    def get_zoom_adaptive_area_thresholds(self):
        """Get area thresholds adapted to current zoom level"""
        zoom_factor = max(1.0, self.current_zoom_level)

        # Base thresholds for 1x zoom
        base_min_area = 12  # Very small for 8x8 pixel birds
        base_max_area = 400

        # Scale area thresholds with zoom level
        # At higher zoom, birds appear larger, so increase thresholds
        min_area = int(base_min_area * (zoom_factor ** 1.5))  # Exponential scaling
        max_area = int(base_max_area * (zoom_factor ** 1.2))

        return min_area, max_area

    def get_foreground_mask(self, current_frame, base_threshold=6):
        """Get foreground mask with ultra-sensitive detection for small objects"""
        if self.background_model is None:
            return None

        gray_frame = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY) if len(current_frame.shape) == 3 else current_frame

        # Adaptive blur based on zoom level
        blur_kernel = max(3, int(5 / self.current_zoom_level))
        if blur_kernel % 2 == 0:
            blur_kernel += 1
        gray_frame = cv2.GaussianBlur(gray_frame, (blur_kernel, blur_kernel), 0)

        # Compute difference
        diff = cv2.absdiff(gray_frame, self.background_model)

        # Ultra-sensitive threshold for small objects
        zoom_adjusted_threshold = max(base_threshold, int(base_threshold * (1.0 / self.current_zoom_level)))

        # Adaptive threshold based on background variance
        if self.background_variance is not None:
            adaptive_threshold = np.maximum(zoom_adjusted_threshold, self.background_variance * 1.5)
            mask = np.where(diff > adaptive_threshold, 255, 0).astype(np.uint8)
        else:
            _, mask = cv2.threshold(diff, zoom_adjusted_threshold, 255, cv2.THRESH_BINARY)

        # Ultra-gentle morphological operations to preserve tiny objects
        kernel_tiny = np.ones((2, 2), np.uint8)
        kernel_small = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))

        # Very gentle noise removal that preserves small objects
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel_tiny, iterations=1)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel_small, iterations=1)

        return mask

def detect_birds_temporal(input_path, output_path, base_threshold=6, motion_sensitivity=1.0, csv_path=None):
    """
    Enhanced bird detection with temporal tracking to filter static false positives.
    """

    # Open the input video
    cap = cv2.VideoCapture(input_path)

    if not cap.isOpened():
        print(f"Error: Could not open video file {input_path}")
        return False

    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    print(f"Processing video: {width}x{height} @ {fps}fps, {total_frames} frames")
    print(f"Temporal bird detection: ultra-sensitive with movement validation")
    print(f"Zoom-adaptive thresholds and water motion filtering enabled")

    # Define the codec and create VideoWriter object
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

    # Initialize detectors
    motion_detector = ZoomAwareCameraMotionDetector(motion_sensitivity, temporal_window=3)
    motion_detector.initialize((height, width))

    bg_subtractor = ZoomAdaptiveBackgroundSubtractor(history_length=7, learning_rate=0.03)

    # Initialize temporal tracker
    temporal_tracker = TemporalBirdTracker(history_frames=5, movement_threshold=3.0, consistency_threshold=0.6)

    # Prepare CSV writer
    csv_file = None
    csv_writer = None
    if csv_path:
        csv_file = open(csv_path, 'w', newline='')
        csv_writer = csv.writer(csv_file)
        csv_writer.writerow(['frame_number', 'timestamp', 'camera_moving', 'bird_count', 'filtered_count', 'zoom_level', 'area_thresholds', 'detections', 'motion_info'])

    # Read first frame
    ret, prev_frame = cap.read()
    if not ret:
        print("Error: Could not read first frame")
        return False

    # Initialize
    bg_subtractor.update_background(prev_frame, 1.0)
    prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)

    frame_count = 0
    camera_moving_frames = 0
    bird_detection_frames = 0
    water_motion_frames = 0
    total_filtered_out = 0

    while True:
        ret, current_frame = cap.read()
        if not ret:
            break

        frame_count += 1
        timestamp = cap.get(cv2.CAP_PROP_POS_MSEC) / 1000.0

        # Convert current frame to grayscale
        current_gray = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY)

        # Preliminary bird detection for motion detector feedback
        preliminary_bird_count = 0
        current_zoom_level = 1.0

        # Update background model first
        bg_ready = bg_subtractor.update_background(current_frame, current_zoom_level)

        if bg_ready:
            foreground_mask = bg_subtractor.get_foreground_mask(current_frame, base_threshold)
            if foreground_mask is not None:
                contours, _ = cv2.findContours(foreground_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                min_area, max_area = bg_subtractor.get_zoom_adaptive_area_thresholds()
                preliminary_bird_count = len([c for c in contours if min_area <= cv2.contourArea(c) <= max_area])

        # Check for camera movement with bird count feedback
        is_camera_moving, motion_params = motion_detector.detect_camera_motion(
            prev_gray, current_gray, preliminary_bird_count)

        # Update zoom level from motion detector
        current_zoom_level = motion_params.get("zoom_level", 1.0)
        bg_subtractor.current_zoom_level = current_zoom_level

        bird_detections = []
        filtered_bird_detections = []

        if is_camera_moving:
            camera_moving_frames += 1
            if motion_params.get("water_motion_detected", False):
                detection_status = "WATER_MOTION"
                water_motion_frames += 1
            elif motion_params.get("false_positive_suppression", False):
                detection_status = "SHAKE_SUPPRESSION"
            elif motion_params.get("immediate", False):
                detection_status = "IMMEDIATE_MOTION"
            else:
                detection_status = "CAMERA_MOVING"
        elif not bg_ready:
            detection_status = "INITIALIZING_BG"
        else:
            bird_detection_frames += 1
            detection_status = "DETECTING_BIRDS"

            # Get zoom-adaptive area thresholds
            min_area, max_area = bg_subtractor.get_zoom_adaptive_area_thresholds()

            # Get foreground mask with enhanced detection
            foreground_mask = bg_subtractor.get_foreground_mask(current_frame, base_threshold)

            if foreground_mask is not None:
                # Find contours
                contours, _ = cv2.findContours(foreground_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                # Filter contours for bird-like objects with enhanced criteria
                for contour in contours:
                    area = cv2.contourArea(contour)
                    if min_area <= area <= max_area:
                        x, y, w, h = cv2.boundingRect(contour)
                        aspect_ratio = w / h if h > 0 else 0

                        # More lenient aspect ratio for very small objects
                        if 0.15 <= aspect_ratio <= 6.0:
                            perimeter = cv2.arcLength(contour, True)
                            if perimeter > 0:
                                compactness = 4 * np.pi * area / (perimeter * perimeter)
                                # Very lenient compactness for tiny objects
                                if compactness > 0.08:
                                    bird_detections.append({
                                        'bbox': (x, y, w, h),
                                        'area': area,
                                        'center': (x + w//2, y + h//2),
                                        'compactness': compactness
                                    })

                # Apply temporal filtering to remove static false positives
                filtered_bird_detections = temporal_tracker.filter_static_detections(bird_detections)
                total_filtered_out += len(bird_detections) - len(filtered_bird_detections)

        # Add current detections to temporal tracker
        temporal_tracker.add_detections(frame_count, filtered_bird_detections)

        # Create output frame
        output_frame = current_frame.copy()

        # Draw bounding boxes for detected birds
        if detection_status == "DETECTING_BIRDS":
            min_area, max_area = bg_subtractor.get_zoom_adaptive_area_thresholds()

            # Draw filtered detections in green
            for i, detection in enumerate(filtered_bird_detections):
                x, y, w, h = detection['bbox']
                area = detection['area']
                center_x, center_y = detection['center']

                # Color code by size relative to zoom level
                small_threshold = min_area * 3
                medium_threshold = min_area * 8

                if area <= small_threshold:
                    color = (0, 255, 0)  # Green for small objects (likely birds)
                elif area <= medium_threshold:
                    color = (0, 255, 255)  # Yellow for medium objects
                else:
                    color = (0, 0, 255)  # Red for large objects

                cv2.rectangle(output_frame, (x, y), (x + w, y + h), color, 2)
                cv2.circle(output_frame, (center_x, center_y), 2, (255, 0, 255), -1)

                label = f"B{i+1}:{area}px"
                cv2.putText(output_frame, label, (x, y-5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

            # Draw filtered out detections in red (for debugging)
            filtered_out = [d for d in bird_detections if d not in filtered_bird_detections]
            for i, detection in enumerate(filtered_out):
                x, y, w, h = detection['bbox']
                cv2.rectangle(output_frame, (x, y), (x + w, y + h), (0, 0, 128), 1)  # Dark red
                cv2.putText(output_frame, f"F{i+1}", (x, y-5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 0, 128), 1)

        # Add status overlay
        status_color = (0, 0, 255) if is_camera_moving else (0, 255, 0)
        if motion_params.get("water_motion_detected", False):
            status_color = (255, 165, 0)  # Orange for water motion
        elif motion_params.get("false_positive_suppression", False):
            status_color = (255, 0, 255)  # Magenta for shake suppression
        elif motion_params.get("immediate", False):
            status_color = (255, 255, 0)  # Cyan for immediate motion

        cv2.putText(output_frame, f"Status: {detection_status}",
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, status_color, 2)
        cv2.putText(output_frame, f"Frame: {frame_count}/{total_frames}",
                   (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(output_frame, f"Birds: {len(filtered_bird_detections)} ({len(bird_detections)} raw)",
                   (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(output_frame, f"Time: {timestamp:.1f}s",
                   (10, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(output_frame, f"Zoom: {current_zoom_level:.3f}",
                   (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        # Show area thresholds
        if detection_status == "DETECTING_BIRDS":
            min_area, max_area = bg_subtractor.get_zoom_adaptive_area_thresholds()
            cv2.putText(output_frame, f"Area: {min_area}-{max_area}px",
                       (10, 180), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        # Show temporal filtering info
        if len(bird_detections) != len(filtered_bird_detections):
            filtered_count = len(bird_detections) - len(filtered_bird_detections)
            cv2.putText(output_frame, f"Filtered: {filtered_count} static",
                       (10, 210), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

        # Show motion parameters
        if is_camera_moving:
            conf = motion_params.get("confidence", 0)
            temp_conf = motion_params.get("temporal_confidence", 0)
            zoom_change = abs(motion_params["zoom"] - 1.0) * 100
            pan_mag = np.sqrt(motion_params["pan_x"]**2 + motion_params["pan_y"]**2)
            rot_deg = abs(np.degrees(motion_params["rotation"]))

            cv2.putText(output_frame, f"Motion: Z:{zoom_change:.1f}% P:{pan_mag:.1f}px R:{rot_deg:.1f}°",
                       (10, 240), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
            cv2.putText(output_frame, f"Conf: {conf:.2f} TC: {temp_conf:.2f}",
                       (10, 270), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)

            if motion_params.get("water_motion_detected", False):
                cv2.putText(output_frame, "WATER/TIDE MOTION DETECTED",
                           (10, 300), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 165, 0), 2)
            elif motion_params.get("false_positive_suppression", False):
                cv2.putText(output_frame, "SHAKE SUPPRESSION ACTIVE",
                           (10, 300), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 255), 2)

        # Record detection data to CSV
        if csv_writer:
            detection_data = []
            for i, detection in enumerate(filtered_bird_detections):
                x, y, w, h = detection['bbox']
                area = detection['area']
                comp = detection['compactness']
                detection_data.append(f"B{i+1}:({x},{y},{w},{h},{area},{comp:.2f})")

            min_area, max_area = bg_subtractor.get_zoom_adaptive_area_thresholds()
            area_thresholds = f"{min_area}-{max_area}"

            motion_info = f"Z:{motion_params['zoom']:.3f},P:{motion_params['pan_x']:.1f},{motion_params['pan_y']:.1f},R:{motion_params['rotation']:.3f},C:{motion_params.get('confidence', 0):.2f},TC:{motion_params.get('temporal_confidence', 0):.2f},ZL:{current_zoom_level:.3f},WM:{motion_params.get('water_motion_detected', False)},FPS:{motion_params.get('false_positive_suppression', False)}"

            csv_writer.writerow([
                frame_count,
                f"{timestamp:.3f}",
                is_camera_moving,
                len(bird_detections),
                len(filtered_bird_detections),
                f"{current_zoom_level:.3f}",
                area_thresholds,
                "|".join(detection_data) if detection_data else "None",
                motion_info
            ])

        # Write the frame
        out.write(output_frame)

        # Update previous frame
        prev_gray = current_gray.copy()

        # Progress indicator
        if frame_count % 100 == 0 or frame_count == total_frames:
            progress = (frame_count / total_frames) * 100
            print(f"Progress: {progress:.1f}% ({frame_count}/{total_frames}) | "
                  f"Birds: {len(filtered_bird_detections)} ({len(bird_detections)} raw) | Status: {detection_status}")

    # Release everything
    cap.release()
    out.release()
    if csv_file:
        csv_file.close()
    cv2.destroyAllWindows()

    print(f"Temporal bird detection video saved to: {output_path}")
    print(f"Camera moving frames: {camera_moving_frames}/{frame_count} ({camera_moving_frames/frame_count*100:.1f}%)")
    print(f"Water motion frames: {water_motion_frames}/{frame_count} ({water_motion_frames/frame_count*100:.1f}%)")
    print(f"Bird detection frames: {bird_detection_frames}/{frame_count} ({bird_detection_frames/frame_count*100:.1f}%)")
    print(f"Total static detections filtered out: {total_filtered_out}")
    if csv_path:
        print(f"Detection data saved to: {csv_path}")
    return True

def main():
    parser = argparse.ArgumentParser(description='Temporal bird detection with movement validation')
    parser.add_argument('input', help='Input video file path')
    parser.add_argument('output', help='Output video file path')
    parser.add_argument('--threshold', type=int, default=6,
                       help='Base motion detection threshold (0-255, default: 6 - ultra-sensitive)')
    parser.add_argument('--motion-sensitivity', type=float, default=1.0,
                       help='Camera motion sensitivity (default: 1.0)')
    parser.add_argument('--csv', help='Optional path to save detection data as CSV')

    args = parser.parse_args()

    # Check if input file exists
    if not os.path.exists(args.input):
        print(f"Error: Input file '{args.input}' does not exist")
        return

    # Create output directory if it doesn't exist
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Create CSV directory if specified
    if args.csv:
        csv_dir = os.path.dirname(args.csv) or '.'
        if not os.path.exists(csv_dir):
            os.makedirs(csv_dir)

    print(f"Input: {args.input}")
    print(f"Output: {args.output}")
    if args.csv:
        print(f"CSV Output: {args.csv}")

    success = detect_birds_temporal(
        args.input, args.output, args.threshold, args.motion_sensitivity, args.csv
    )

    if success:
        print("Temporal bird detection completed successfully!")
    else:
        print("Temporal bird detection failed!")

if __name__ == "__main__":
    main()
