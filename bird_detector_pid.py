import cv2
import numpy as np
import argparse
import os
import csv
from collections import deque
from datetime import datetime
import torch
from ultralytics import YOLO
import configparser

class PIDTracker:
    """PID-based bird tracker for smooth tracking and false positive suppression"""
    
    def __init__(self, track_id, initial_detection, kp=0.8, ki=0.1, kd=0.3):
        self.track_id = track_id
        self.kp = kp  # Proportional gain
        self.ki = ki  # Integral gain  
        self.kd = kd  # Derivative gain
        
        # Current state
        self.position = np.array([initial_detection['center'][0], initial_detection['center'][1]], dtype=np.float32)
        self.velocity = np.array([0.0, 0.0], dtype=np.float32)
        self.size = initial_detection['area']
        
        # PID control variables
        self.error_integral = np.array([0.0, 0.0], dtype=np.float32)
        self.previous_error = np.array([0.0, 0.0], dtype=np.float32)
        
        # Tracking state
        self.confidence = 1.0
        self.frames_since_detection = 0
        self.total_detections = 1
        self.detection_history = deque(maxlen=10)
        self.detection_history.append(initial_detection)
        
        # Prediction state
        self.predicted_position = self.position.copy()
        self.predicted_size = self.size
        
        # Quality metrics
        self.stability_score = 1.0
        self.false_positive_score = 0.0
        
    def predict_next_position(self):
        """Predict next position using current velocity"""
        self.predicted_position = self.position + self.velocity
        return self.predicted_position
    
    def update_with_detection(self, detection):
        """Update tracker with new detection using PID control"""
        detection_center = np.array([detection['center'][0], detection['center'][1]], dtype=np.float32)
        
        # Calculate error (difference between predicted and actual position)
        error = detection_center - self.predicted_position
        
        # PID control calculation
        self.error_integral += error
        error_derivative = error - self.previous_error
        
        # PID output (correction to apply)
        pid_output = (self.kp * error + 
                     self.ki * self.error_integral + 
                     self.kd * error_derivative)
        
        # Update position with PID correction
        self.position = self.predicted_position + pid_output
        
        # Update velocity (smoothed)
        new_velocity = detection_center - self.position
        self.velocity = 0.7 * self.velocity + 0.3 * new_velocity  # Smooth velocity update
        
        # Update size (smoothed)
        self.size = 0.8 * self.size + 0.2 * detection['area']
        
        # Update tracking state
        self.frames_since_detection = 0
        self.total_detections += 1
        self.detection_history.append(detection)
        
        # Update confidence based on consistency
        position_consistency = 1.0 / (1.0 + np.linalg.norm(error) / 10.0)
        size_consistency = min(detection['area'], self.size) / max(detection['area'], self.size)
        self.confidence = 0.7 * self.confidence + 0.3 * (position_consistency * size_consistency)
        
        # Update stability score
        if len(self.detection_history) >= 3:
            recent_positions = [np.array(d['center']) for d in list(self.detection_history)[-3:]]
            position_variance = np.var([np.linalg.norm(pos - self.position) for pos in recent_positions])
            self.stability_score = 1.0 / (1.0 + position_variance / 100.0)
        
        # Update false positive score (lower is better)
        if self.total_detections > 5:
            detection_rate = self.total_detections / (self.total_detections + self.frames_since_detection)
            self.false_positive_score = max(0.0, 1.0 - detection_rate)
        
        self.previous_error = error
        
    def update_without_detection(self):
        """Update tracker when no detection is found (prediction only)"""
        self.frames_since_detection += 1
        
        # Predict position using current velocity
        self.position += self.velocity
        
        # Decay velocity (objects tend to slow down)
        self.velocity *= 0.95
        
        # Decay confidence
        confidence_decay = 0.9 if self.frames_since_detection < 5 else 0.8
        self.confidence *= confidence_decay
        
        # Update stability score (decreases without detections)
        self.stability_score *= 0.9
        
    def is_valid_track(self, min_confidence=0.3, max_frames_lost=10):
        """Check if track is still valid"""
        return (self.confidence >= min_confidence and 
                self.frames_since_detection <= max_frames_lost and
                self.false_positive_score < 0.7)
    
    def get_current_bbox(self):
        """Get current bounding box based on position and size"""
        # Estimate width and height from area (assuming roughly square)
        side_length = int(np.sqrt(self.size))
        x = int(self.position[0] - side_length // 2)
        y = int(self.position[1] - side_length // 2)
        w = side_length
        h = side_length
        return (x, y, w, h)
    
    def get_detection_info(self):
        """Get detection information for output"""
        x, y, w, h = self.get_current_bbox()
        return {
            'bbox': (x, y, w, h),
            'area': int(self.size),
            'center': (int(self.position[0]), int(self.position[1])),
            'confidence': self.confidence,
            'stability': self.stability_score,
            'false_positive_score': self.false_positive_score,
            'frames_since_detection': self.frames_since_detection,
            'total_detections': self.total_detections,
            'source': 'PID_Track'
        }

def load_config(config_path="bird_detection_config.ini"):
    """Load configuration from INI file with fallback defaults"""
    config = configparser.ConfigParser()

    # Default values (balanced between previous extremes)
    defaults = {
        'YOLO': {
            'confidence_threshold': '0.375'  # Between 0.3 and 0.45
        },
        'BACKGROUND': {
            'difference_threshold': '12'  # Between 8 and 15
        },
        'PID_TRACKING': {
            'proportional_gain': '0.8',
            'integral_gain': '0.1',
            'derivative_gain': '0.3',
            'max_tracking_distance': '50',
            'min_track_confidence': '0.3',
            'confidence_frames': '5',
            'movement_threshold': '5.0'
        },
        'CAMERA_MOTION': {
            'motion_sensitivity': '1.0',
            'temporal_window': '5',
            'movement_start_delay': '5',
            'movement_end_delay': '10'
        },
        'DETECTION': {
            'history_frames': '10',
            'min_area': '6',
            'max_area': '400',
            'open_iterations': '1',
            'close_iterations': '1'
        }
    }

    # Set defaults
    for section, options in defaults.items():
        config.add_section(section)
        for option, value in options.items():
            config.set(section, option, value)

    # Try to read config file
    if os.path.exists(config_path):
        try:
            config.read(config_path)
            print(f"Configuration loaded from {config_path}")
        except Exception as e:
            print(f"Error reading config file {config_path}: {e}")
            print("Using default values")
    else:
        print(f"Config file {config_path} not found, using default values")

    return config

class YOLOBirdDetector:
    """YOLO-based bird detection with configurable confidence threshold"""

    def __init__(self, model_path="./best0313.pt", confidence_threshold=0.375):
        self.model_path = model_path
        self.confidence_threshold = confidence_threshold
        
        # Load YOLO model
        try:
            self.model = YOLO(model_path)
            print(f"YOLO model loaded successfully from {model_path}")
        except Exception as e:
            print(f"Error loading YOLO model: {e}")
            self.model = None
    
    def detect_birds(self, frame):
        """Detect birds using YOLO model with higher confidence threshold"""
        if self.model is None:
            return []
        
        try:
            # Run YOLO inference
            results = self.model(frame, conf=self.confidence_threshold, verbose=False)
            
            detections = []
            for result in results:
                if result.boxes is not None:
                    for box in result.boxes:
                        # Extract bounding box coordinates
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        
                        # Convert to our detection format
                        x, y, w, h = int(x1), int(y1), int(x2-x1), int(y2-y1)
                        area = w * h
                        
                        detections.append({
                            'bbox': (x, y, w, h),
                            'area': area,
                            'center': (x + w//2, y + h//2),
                            'confidence': float(confidence),
                            'source': 'YOLO'
                        })
            
            return detections
            
        except Exception as e:
            print(f"YOLO detection error: {e}")
            return []

class FrameAveragingDetector:
    """Background subtraction with configurable threshold"""

    def __init__(self, history_frames=10, threshold=12, min_area=6, max_area=400):
        self.history_frames = history_frames
        self.frame_buffer = deque(maxlen=history_frames)
        self.running_average = None
        self.min_area = min_area
        self.max_area = max_area
        self.threshold = threshold
        
    def update_average(self, frame):
        """Update running average of frames"""
        gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) if len(frame.shape) == 3 else frame
        gray_frame = cv2.GaussianBlur(gray_frame, (3, 3), 0)
        
        self.frame_buffer.append(gray_frame.astype(np.float32))
        
        if len(self.frame_buffer) >= 5:
            frames_array = np.array(list(self.frame_buffer)[:-1])
            self.running_average = np.mean(frames_array, axis=0).astype(np.uint8)
            return True
        
        return False
    
    def detect_movement(self, current_frame):
        """Detect movement with lower threshold for higher sensitivity"""
        if self.running_average is None:
            return None, None
        
        gray_current = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY) if len(current_frame.shape) == 3 else current_frame
        gray_current = cv2.GaussianBlur(gray_current, (3, 3), 0)
        
        diff = cv2.absdiff(gray_current, self.running_average)
        _, binary_diff = cv2.threshold(diff, self.threshold, 255, cv2.THRESH_BINARY)
        
        # Gentler morphological operations to preserve small detections
        kernel_small = np.ones((2, 2), np.uint8)
        kernel_medium = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (2, 2))  # Smaller kernel
        
        binary_diff = cv2.morphologyEx(binary_diff, cv2.MORPH_OPEN, kernel_small, iterations=1)
        binary_diff = cv2.morphologyEx(binary_diff, cv2.MORPH_CLOSE, kernel_medium, iterations=1)
        
        return diff, binary_diff
    
    def find_bird_candidates(self, binary_mask):
        """Find bird candidates with more sensitive parameters"""
        if binary_mask is None:
            return []
        
        contours, _ = cv2.findContours(binary_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        candidates = []
        for contour in contours:
            area = cv2.contourArea(contour)
            
            if self.min_area <= area <= self.max_area:
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = w / h if h > 0 else 0
                
                if 0.1 <= aspect_ratio <= 8.0:  # More lenient aspect ratio
                    perimeter = cv2.arcLength(contour, True)
                    if perimeter > 0:
                        compactness = 4 * np.pi * area / (perimeter * perimeter)
                        
                        if compactness > 0.05:  # More lenient compactness
                            candidates.append({
                                'bbox': (x, y, w, h),
                                'area': area,
                                'center': (x + w//2, y + h//2),
                                'aspect_ratio': aspect_ratio,
                                'compactness': compactness,
                                'source': 'Background'
                            })
        
        return candidates

class PIDTrackingSystem:
    """PID-based tracking system for smooth bird tracking"""
    
    def __init__(self, max_distance=50, min_track_confidence=0.3):
        self.trackers = []
        self.next_track_id = 1
        self.max_distance = max_distance
        self.min_track_confidence = min_track_confidence
        
    def update(self, detections):
        """Update tracking system with new detections"""
        # Predict next positions for all trackers
        for tracker in self.trackers:
            tracker.predict_next_position()
        
        # Match detections to existing trackers
        matched_trackers = set()
        matched_detections = set()
        
        for i, detection in enumerate(detections):
            best_tracker = None
            best_distance = float('inf')
            
            detection_center = np.array(detection['center'])
            
            for j, tracker in enumerate(self.trackers):
                if j in matched_trackers:
                    continue
                
                distance = np.linalg.norm(detection_center - tracker.predicted_position)
                
                if distance < self.max_distance and distance < best_distance:
                    best_distance = distance
                    best_tracker = j
            
            if best_tracker is not None:
                self.trackers[best_tracker].update_with_detection(detection)
                matched_trackers.add(best_tracker)
                matched_detections.add(i)
        
        # Update unmatched trackers (prediction only)
        for j, tracker in enumerate(self.trackers):
            if j not in matched_trackers:
                tracker.update_without_detection()
        
        # Create new trackers for unmatched detections
        for i, detection in enumerate(detections):
            if i not in matched_detections:
                new_tracker = PIDTracker(self.next_track_id, detection)
                self.trackers.append(new_tracker)
                self.next_track_id += 1
        
        # Remove invalid trackers
        self.trackers = [t for t in self.trackers if t.is_valid_track(self.min_track_confidence)]
        
    def get_tracked_detections(self):
        """Get current tracked detections"""
        tracked_detections = []
        for tracker in self.trackers:
            if tracker.confidence > self.min_track_confidence:
                detection_info = tracker.get_detection_info()
                detection_info['track_id'] = tracker.track_id
                tracked_detections.append(detection_info)
        
        return tracked_detections
    
    def get_tracking_stats(self):
        """Get tracking system statistics"""
        total_tracks = len(self.trackers)
        active_tracks = len([t for t in self.trackers if t.frames_since_detection == 0])
        predicted_tracks = len([t for t in self.trackers if t.frames_since_detection > 0])
        
        return {
            'total_tracks': total_tracks,
            'active_tracks': active_tracks,
            'predicted_tracks': predicted_tracks
        }

class ZoomAwareCameraMotionDetector:
    """Enhanced camera motion detector"""

    def __init__(self, motion_sensitivity=1.0, temporal_window=5):
        self.motion_sensitivity = motion_sensitivity
        self.temporal_window = temporal_window

        self.lk_params = dict(winSize=(15, 15), maxLevel=2,
                             criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 20, 0.01))

        self.base_zoom_threshold = 0.008
        self.base_pan_threshold = 1.5
        self.base_rotation_threshold = 0.015

        self.immediate_zoom_threshold = 0.02
        self.immediate_pan_threshold = 3.0
        self.immediate_rotation_threshold = 0.03

        self.current_zoom_level = 1.0
        self.zoom_history = deque(maxlen=10)

        self.motion_history = deque(maxlen=temporal_window)
        self.motion_confidence_threshold = 0.5

        self.prev_motion_state = False
        self.transition_frames = 0
        self.max_transition_frames = 5

        self.water_motion_threshold = 0.3
        self.high_detection_count_threshold = 30
        self.zoom_shake_threshold = 1.015

        self.frame_width = None
        self.frame_height = None

    def initialize(self, frame_shape):
        """Initialize camera motion detector"""
        self.frame_height, self.frame_width = frame_shape

    def detect_camera_motion(self, old_gray, new_gray, bird_count=0):
        """Enhanced camera motion detection"""
        sample_points = []
        step = 50
        border = 60

        for y in range(border, self.frame_height - border, step):
            for x in range(border, self.frame_width - border, step):
                sample_points.append([x, y])

        if len(sample_points) < 6:
            motion_detected = False
            motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0}
        else:
            sample_points = np.array(sample_points, dtype=np.float32).reshape(-1, 1, 2)

            new_points, status, error = cv2.calcOpticalFlowPyrLK(
                old_gray, new_gray, sample_points, None, **self.lk_params)

            good_mask = (status == 1) & (error < 15)

            if np.sum(good_mask) < 6:
                motion_detected = False
                motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0}
            else:
                old_pts = sample_points[good_mask.flatten()].reshape(-1, 2)
                new_pts = new_points[good_mask.flatten()].reshape(-1, 2)

                try:
                    transform_result = cv2.estimateAffinePartial2D(
                        old_pts, new_pts, method=cv2.RANSAC,
                        ransacReprojThreshold=1.5, maxIters=1000, confidence=0.95)

                    transform_matrix = transform_result[0]
                    inlier_mask = transform_result[1]

                    if transform_matrix is None or inlier_mask is None:
                        motion_detected = False
                        motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0}
                    else:
                        confidence = np.sum(inlier_mask) / len(inlier_mask) if len(inlier_mask) > 0 else 0

                        a = transform_matrix[0, 0]
                        b = transform_matrix[0, 1]
                        tx = transform_matrix[0, 2]
                        ty = transform_matrix[1, 2]

                        scale = np.sqrt(a*a + b*b)
                        rotation = np.arctan2(b, a)
                        pan_x = tx * self.motion_sensitivity
                        pan_y = ty * self.motion_sensitivity

                        motion_params = {
                            "zoom": scale,
                            "pan_x": pan_x,
                            "pan_y": pan_y,
                            "rotation": rotation,
                            "confidence": confidence
                        }

                        zoom_change = abs(scale - 1.0)
                        pan_magnitude = np.sqrt(pan_x*pan_x + pan_y*pan_y)
                        rotation_magnitude = abs(rotation)

                        immediate_motion = (confidence > 0.6 and (
                            zoom_change > self.immediate_zoom_threshold or
                            pan_magnitude > self.immediate_pan_threshold or
                            rotation_magnitude > self.immediate_rotation_threshold))

                        normal_motion = (confidence > 0.7 and (
                            zoom_change > self.base_zoom_threshold or
                            pan_magnitude > self.base_pan_threshold or
                            rotation_magnitude > self.base_rotation_threshold))

                        motion_detected = immediate_motion or normal_motion

                except Exception as e:
                    motion_detected = False
                    motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0}

        # Temporal filtering
        self.motion_history.append(motion_detected)
        motion_ratio = sum(self.motion_history) / len(self.motion_history)
        final_motion_detected = motion_ratio >= self.motion_confidence_threshold

        motion_params["temporal_confidence"] = motion_ratio

        return final_motion_detected, motion_params

def detect_birds_pid(input_path, output_path, yolo_model_path="./best0313.pt", config_path="bird_detection_config.ini", csv_path=None):
    """
    PID-based bird detection with smooth tracking and false positive suppression.
    """

    cap = cv2.VideoCapture(input_path)

    if not cap.isOpened():
        print(f"Error: Could not open video file {input_path}")
        return False

    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    print(f"Processing video: {width}x{height} @ {fps}fps, {total_frames} frames")
    print(f"PID tracking system: YOLO conf={yolo_confidence}, Background threshold={background_threshold}")
    print(f"Smooth tracking with false positive suppression enabled")

    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

    # Initialize detectors and tracking system
    yolo_detector = YOLOBirdDetector(yolo_model_path, yolo_confidence)
    motion_detector = ZoomAwareCameraMotionDetector(motion_sensitivity=1.0, temporal_window=5)
    motion_detector.initialize((height, width))

    frame_detector = FrameAveragingDetector(history_frames=10, threshold=background_threshold)
    tracking_system = PIDTrackingSystem(max_distance=50, min_track_confidence=0.3)

    # Movement delay tracking
    movement_end_delay_frames = 10
    movement_start_delay_frames = 5
    frames_since_movement_end = 0
    frames_since_movement_start = 0

    # Prepare CSV writer
    csv_file = None
    csv_writer = None
    if csv_path:
        csv_file = open(csv_path, 'w', newline='')
        csv_writer = csv.writer(csv_file)
        csv_writer.writerow(['frame_number', 'timestamp', 'camera_moving', 'yolo_count', 'background_count',
                           'tracked_count', 'detection_source', 'detections', 'tracking_stats', 'motion_info'])

    ret, prev_frame = cap.read()
    if not ret:
        print("Error: Could not read first frame")
        return False

    frame_detector.update_average(prev_frame)
    prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)

    frame_count = 0
    camera_moving_frames = 0
    yolo_detection_frames = 0
    background_detection_frames = 0
    tracking_frames = 0
    total_yolo_detections = 0
    total_background_detections = 0
    total_tracked_detections = 0
    movement_delay_frames = 0
    movement_start_delay_frames_count = 0

    while True:
        ret, current_frame = cap.read()
        if not ret:
            break

        frame_count += 1
        timestamp = cap.get(cv2.CAP_PROP_POS_MSEC) / 1000.0

        current_gray = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY)

        # Update frame averaging
        avg_ready = frame_detector.update_average(current_frame)

        # Check for camera movement
        is_camera_moving, motion_params = motion_detector.detect_camera_motion(
            prev_gray, current_gray, 0)

        # Track movement delays
        if is_camera_moving:
            frames_since_movement_end = 0
            frames_since_movement_start += 1
            camera_moving_frames += 1
        else:
            frames_since_movement_end += 1
            frames_since_movement_start = 0

        # Always try YOLO detection (higher confidence threshold)
        yolo_detections = yolo_detector.detect_birds(current_frame)

        # Collect all detections for tracking
        all_detections = []
        background_detections = []
        detection_source = "NONE"

        if is_camera_moving:
            # Camera is moving - check start delay
            if frames_since_movement_start >= movement_start_delay_frames:
                # Past start delay - use YOLO
                if len(yolo_detections) > 0:
                    all_detections.extend(yolo_detections)
                    detection_source = "YOLO_MOVING"
                    yolo_detection_frames += 1
                    total_yolo_detections += len(yolo_detections)
                else:
                    detection_source = "CAMERA_MOVING"
            else:
                # Still in start delay period
                detection_source = f"MOVEMENT_START_DELAY_{frames_since_movement_start}/{movement_start_delay_frames}"
                movement_start_delay_frames_count += 1
        else:
            # Camera is stable
            if len(yolo_detections) > 0:
                # YOLO found birds - use YOLO results
                all_detections.extend(yolo_detections)
                detection_source = "YOLO_STABLE"
                yolo_detection_frames += 1
                total_yolo_detections += len(yolo_detections)
            elif frames_since_movement_end >= movement_end_delay_frames and avg_ready:
                # YOLO found nothing, camera stable for enough time - try background subtraction
                diff_frame, binary_mask = frame_detector.detect_movement(current_frame)

                if binary_mask is not None:
                    background_detections = frame_detector.find_bird_candidates(binary_mask)

                    if len(background_detections) > 0:
                        all_detections.extend(background_detections)
                        detection_source = "BACKGROUND"
                        background_detection_frames += 1
                        total_background_detections += len(background_detections)
                    else:
                        detection_source = "NO_MOVEMENT"
                else:
                    detection_source = "NO_DIFFERENCE"
            elif frames_since_movement_end < movement_end_delay_frames:
                # Still in delay period after movement end
                detection_source = f"MOVEMENT_DELAY_{frames_since_movement_end}/{movement_end_delay_frames}"
                movement_delay_frames += 1
            else:
                detection_source = "BUILDING_AVERAGE"

        # Update tracking system with all detections
        tracking_system.update(all_detections)
        tracked_detections = tracking_system.get_tracked_detections()
        tracking_stats = tracking_system.get_tracking_stats()

        if len(tracked_detections) > 0:
            tracking_frames += 1
            total_tracked_detections += len(tracked_detections)

        # Create output frame
        output_frame = current_frame.copy()

        # Draw tracked detections with PID information
        for detection in tracked_detections:
            x, y, w, h = detection['bbox']
            track_id = detection['track_id']
            confidence = detection['confidence']
            stability = detection['stability']
            false_positive_score = detection['false_positive_score']
            frames_since_detection = detection['frames_since_detection']
            source = detection['source']

            # Color code by tracking quality
            if confidence > 0.8 and stability > 0.7:
                color = (0, 255, 0)  # Green for high quality tracks
            elif confidence > 0.5 and stability > 0.5:
                color = (0, 255, 255)  # Yellow for medium quality tracks
            elif frames_since_detection > 0:
                color = (255, 0, 255)  # Magenta for predicted tracks
            else:
                color = (0, 0, 255)  # Red for low quality tracks

            # Draw bounding box
            cv2.rectangle(output_frame, (x, y), (x + w, y + h), color, 2)
            cv2.circle(output_frame, (int(detection['center'][0]), int(detection['center'][1])), 3, (255, 0, 255), -1)

            # Label with track ID and quality metrics
            label = f"T{track_id}:{detection['area']}px"
            cv2.putText(output_frame, label, (x, y-5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

            # Show tracking quality for larger detections
            if detection['area'] > 20:
                quality_label = f"C:{confidence:.2f} S:{stability:.2f}"
                cv2.putText(output_frame, quality_label, (x, y+h+15),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)

                # Show prediction info if tracking without detection
                if frames_since_detection > 0:
                    pred_label = f"PRED:{frames_since_detection}f"
                    cv2.putText(output_frame, pred_label, (x, y+h+30),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)

        # Add status overlay
        status_color = (0, 0, 255) if is_camera_moving else (0, 255, 0)

        cv2.putText(output_frame, f"Status: {detection_source}",
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, status_color, 2)
        cv2.putText(output_frame, f"Frame: {frame_count}/{total_frames}",
                   (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(output_frame, f"YOLO: {len(yolo_detections)} | BG: {len(background_detections)} | Tracked: {len(tracked_detections)}",
                   (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(output_frame, f"Time: {timestamp:.1f}s | Delays: E:{frames_since_movement_end}/{movement_end_delay_frames} S:{frames_since_movement_start}/{movement_start_delay_frames}",
                   (10, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)

        # Show tracking statistics
        cv2.putText(output_frame, f"Tracks: Total:{tracking_stats['total_tracks']} Active:{tracking_stats['active_tracks']} Pred:{tracking_stats['predicted_tracks']}",
                   (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        # Show detection method info
        if detection_source in ["YOLO_MOVING", "YOLO_STABLE"]:
            cv2.putText(output_frame, f"YOLO Detection Active (conf>{yolo_confidence})",
                       (10, 180), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        elif detection_source == "BACKGROUND":
            cv2.putText(output_frame, f"Background Subtraction (thresh={background_threshold})",
                       (10, 180), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 2)
        elif detection_source.startswith("MOVEMENT_DELAY"):
            cv2.putText(output_frame, f"Movement End Delay: {frames_since_movement_end}/{movement_end_delay_frames}",
                       (10, 180), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 128, 0), 2)
        elif detection_source.startswith("MOVEMENT_START_DELAY"):
            cv2.putText(output_frame, f"Movement Start Delay: {frames_since_movement_start}/{movement_start_delay_frames}",
                       (10, 180), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 128), 2)

        # Show PID tracking info
        cv2.putText(output_frame, f"PID Tracking: Smooth motion prediction & false positive suppression",
                   (10, 210), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (128, 255, 128), 1)

        # Record detection data to CSV
        if csv_writer:
            detection_data = []
            for i, detection in enumerate(tracked_detections):
                x, y, w, h = detection['bbox']
                track_id = detection['track_id']
                confidence = detection['confidence']
                stability = detection['stability']
                false_positive_score = detection['false_positive_score']
                frames_since_detection = detection['frames_since_detection']
                total_detections = detection['total_detections']
                detection_data.append(f"T{track_id}:({x},{y},{w},{h},{detection['area']},{confidence:.3f},{stability:.3f},{false_positive_score:.3f},{frames_since_detection},{total_detections})")

            tracking_stats_str = f"Total:{tracking_stats['total_tracks']},Active:{tracking_stats['active_tracks']},Pred:{tracking_stats['predicted_tracks']}"
            motion_info = f"Z:{motion_params['zoom']:.3f},P:{motion_params['pan_x']:.1f},{motion_params['pan_y']:.1f},R:{motion_params['rotation']:.3f},C:{motion_params.get('confidence', 0):.2f},TC:{motion_params.get('temporal_confidence', 0):.2f},FSME:{frames_since_movement_end},FSMS:{frames_since_movement_start}"

            csv_writer.writerow([
                frame_count,
                f"{timestamp:.3f}",
                is_camera_moving,
                len(yolo_detections),
                len(background_detections),
                len(tracked_detections),
                detection_source,
                "|".join(detection_data) if detection_data else "None",
                tracking_stats_str,
                motion_info
            ])

        out.write(output_frame)
        prev_gray = current_gray.copy()

        # Progress indicator
        if frame_count % 100 == 0 or frame_count == total_frames:
            progress = (frame_count / total_frames) * 100
            print(f"Progress: {progress:.1f}% ({frame_count}/{total_frames}) | "
                  f"YOLO: {len(yolo_detections)} | BG: {len(background_detections)} | Tracked: {len(tracked_detections)} | Source: {detection_source}")

    cap.release()
    out.release()
    if csv_file:
        csv_file.close()
    cv2.destroyAllWindows()

    print(f"PID bird detection video saved to: {output_path}")
    print(f"Camera moving frames: {camera_moving_frames}/{frame_count} ({camera_moving_frames/frame_count*100:.1f}%)")
    print(f"Movement end delay frames: {movement_delay_frames}/{frame_count} ({movement_delay_frames/frame_count*100:.1f}%)")
    print(f"Movement start delay frames: {movement_start_delay_frames_count}/{frame_count} ({movement_start_delay_frames_count/frame_count*100:.1f}%)")
    print(f"YOLO detection frames: {yolo_detection_frames}/{frame_count} ({yolo_detection_frames/frame_count*100:.1f}%)")
    print(f"Background detection frames: {background_detection_frames}/{frame_count} ({background_detection_frames/frame_count*100:.1f}%)")
    print(f"Tracking frames: {tracking_frames}/{frame_count} ({tracking_frames/frame_count*100:.1f}%)")
    print(f"Total YOLO detections: {total_yolo_detections}")
    print(f"Total background detections: {total_background_detections}")
    print(f"Total tracked detections: {total_tracked_detections}")
    if csv_path:
        print(f"Detection data saved to: {csv_path}")
    return True

def main():
    parser = argparse.ArgumentParser(description='PID-based bird detection with smooth tracking')
    parser.add_argument('input', help='Input video file path')
    parser.add_argument('output', help='Output video file path')
    parser.add_argument('--yolo-model', default='./best0313.pt',
                       help='Path to YOLO model file (default: ./best0313.pt)')
    parser.add_argument('--yolo-confidence', type=float, default=0.45,
                       help='YOLO confidence threshold (default: 0.45)')
    parser.add_argument('--background-threshold', type=int, default=8,
                       help='Background subtraction threshold (default: 8)')
    parser.add_argument('--csv', help='Optional path to save detection data as CSV')

    args = parser.parse_args()

    if not os.path.exists(args.input):
        print(f"Error: Input file '{args.input}' does not exist")
        return

    if not os.path.exists(args.yolo_model):
        print(f"Error: YOLO model file '{args.yolo_model}' does not exist")
        return

    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    if args.csv:
        csv_dir = os.path.dirname(args.csv) or '.'
        if not os.path.exists(csv_dir):
            os.makedirs(csv_dir)

    print(f"Input: {args.input}")
    print(f"Output: {args.output}")
    print(f"YOLO Model: {args.yolo_model}")
    print(f"YOLO Confidence: {args.yolo_confidence}")
    print(f"Background Threshold: {args.background_threshold}")
    if args.csv:
        print(f"CSV Output: {args.csv}")

    success = detect_birds_pid(
        args.input, args.output, args.yolo_model, args.yolo_confidence,
        args.background_threshold, args.csv
    )

    if success:
        print("PID bird detection completed successfully!")
    else:
        print("PID bird detection failed!")

if __name__ == "__main__":
    main()
