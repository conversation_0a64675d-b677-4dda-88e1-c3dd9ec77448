import cv2
import numpy as np
import argparse
import os
import csv
from collections import deque
from datetime import datetime
import torch
from ultralytics import YOLO

class YOLOBirdDetector:
    """YOLO-based bird detection using fine-tuned model"""
    
    def __init__(self, model_path="./best0313.pt", confidence_threshold=0.3):
        self.model_path = model_path
        self.confidence_threshold = confidence_threshold
        
        # Load YOLO model
        try:
            self.model = YOLO(model_path)
            print(f"YOLO model loaded successfully from {model_path}")
        except Exception as e:
            print(f"Error loading YOLO model: {e}")
            self.model = None
    
    def detect_birds(self, frame):
        """Detect birds using YOLO model"""
        if self.model is None:
            return []
        
        try:
            # Run YOLO inference
            results = self.model(frame, conf=self.confidence_threshold, verbose=False)
            
            detections = []
            for result in results:
                if result.boxes is not None:
                    for box in result.boxes:
                        # Extract bounding box coordinates
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        
                        # Convert to our detection format
                        x, y, w, h = int(x1), int(y1), int(x2-x1), int(y2-y1)
                        area = w * h
                        
                        detections.append({
                            'bbox': (x, y, w, h),
                            'area': area,
                            'center': (x + w//2, y + h//2),
                            'confidence': float(confidence),
                            'source': 'YOLO'
                        })
            
            return detections
            
        except Exception as e:
            print(f"YOLO detection error: {e}")
            return []

class ConfidentBirdTracker:
    """Temporal confidence building to delay detections until confident they are real birds"""
    
    def __init__(self, confidence_frames=5, confidence_threshold=0.6, movement_threshold=5.0):
        self.confidence_frames = confidence_frames
        self.confidence_threshold = confidence_threshold
        self.movement_threshold = movement_threshold
        
        # Track detection candidates over time
        self.candidate_history = deque(maxlen=confidence_frames)
        
    def add_frame_detections(self, frame_number, detections):
        """Add current frame detections to confidence tracking"""
        detection_centers = []
        for detection in detections:
            x, y, w, h = detection['bbox']
            center_x, center_y = x + w//2, y + h//2
            detection_centers.append({
                'center': (center_x, center_y),
                'area': detection['area'],
                'detection': detection
            })
        
        self.candidate_history.append({
            'frame': frame_number,
            'detections': detection_centers
        })
    
    def get_confident_detections(self):
        """Get detections that have built sufficient confidence over time"""
        if len(self.candidate_history) < self.confidence_frames:
            return []
        
        current_frame = self.candidate_history[-1]
        current_detections = current_frame['detections']
        
        confident_detections = []
        
        for current_det in current_detections:
            current_center = current_det['center']
            
            confidence_score = 0
            movement_evidence = 0
            tracking_count = 0
            
            # Look for this detection in previous frames
            for i, hist_frame in enumerate(list(self.candidate_history)[:-1]):
                closest_distance = float('inf')
                closest_detection = None
                
                for hist_det in hist_frame['detections']:
                    hist_center = hist_det['center']
                    distance = np.sqrt((current_center[0] - hist_center[0])**2 + 
                                     (current_center[1] - hist_center[1])**2)
                    
                    if distance < closest_distance and distance < 25:
                        closest_distance = distance
                        closest_detection = hist_det
                
                if closest_detection is not None:
                    tracking_count += 1
                    confidence_score += 0.2
                    
                    if closest_distance > self.movement_threshold:
                        movement_evidence += 1
                        confidence_score += 0.3
                    
                    area_ratio = min(current_det['area'], closest_detection['area']) / max(current_det['area'], closest_detection['area'])
                    if area_ratio > 0.7:
                        confidence_score += 0.1
            
            if tracking_count > 0:
                final_confidence = confidence_score / tracking_count
                
                if movement_evidence >= 2:
                    final_confidence += 0.2
                
                tracking_ratio = tracking_count / (len(self.candidate_history) - 1)
                if tracking_ratio > 0.6:
                    final_confidence += 0.1
                
                if final_confidence >= self.confidence_threshold:
                    detection_with_confidence = current_det['detection'].copy()
                    detection_with_confidence['confidence'] = final_confidence
                    detection_with_confidence['tracking_count'] = tracking_count
                    detection_with_confidence['movement_evidence'] = movement_evidence
                    detection_with_confidence['source'] = 'Temporal'
                    confident_detections.append(detection_with_confidence)
        
        return confident_detections

class FrameAveragingDetector:
    """Background subtraction for fallback detection when YOLO fails"""
    
    def __init__(self, history_frames=10):
        self.history_frames = history_frames
        self.frame_buffer = deque(maxlen=history_frames)
        self.running_average = None
        self.min_area = 8
        self.max_area = 300
        self.threshold = 15
        
    def update_average(self, frame):
        """Update running average of frames"""
        gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) if len(frame.shape) == 3 else frame
        gray_frame = cv2.GaussianBlur(gray_frame, (3, 3), 0)
        
        self.frame_buffer.append(gray_frame.astype(np.float32))
        
        if len(self.frame_buffer) >= 5:
            frames_array = np.array(list(self.frame_buffer)[:-1])
            self.running_average = np.mean(frames_array, axis=0).astype(np.uint8)
            return True
        
        return False
    
    def detect_movement(self, current_frame):
        """Detect movement by subtracting average from current frame"""
        if self.running_average is None:
            return None, None
        
        gray_current = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY) if len(current_frame.shape) == 3 else current_frame
        gray_current = cv2.GaussianBlur(gray_current, (3, 3), 0)
        
        diff = cv2.absdiff(gray_current, self.running_average)
        _, binary_diff = cv2.threshold(diff, self.threshold, 255, cv2.THRESH_BINARY)
        
        kernel_small = np.ones((2, 2), np.uint8)
        kernel_medium = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        
        binary_diff = cv2.morphologyEx(binary_diff, cv2.MORPH_OPEN, kernel_small, iterations=1)
        binary_diff = cv2.morphologyEx(binary_diff, cv2.MORPH_CLOSE, kernel_medium, iterations=1)
        
        return diff, binary_diff
    
    def find_bird_candidates(self, binary_mask):
        """Find bird candidates from binary mask"""
        if binary_mask is None:
            return []
        
        contours, _ = cv2.findContours(binary_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        candidates = []
        for contour in contours:
            area = cv2.contourArea(contour)
            
            if self.min_area <= area <= self.max_area:
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = w / h if h > 0 else 0
                
                if 0.2 <= aspect_ratio <= 5.0:
                    perimeter = cv2.arcLength(contour, True)
                    if perimeter > 0:
                        compactness = 4 * np.pi * area / (perimeter * perimeter)
                        
                        if compactness > 0.1:
                            candidates.append({
                                'bbox': (x, y, w, h),
                                'area': area,
                                'center': (x + w//2, y + h//2),
                                'aspect_ratio': aspect_ratio,
                                'compactness': compactness,
                                'source': 'Background'
                            })
        
        return candidates

class ZoomAwareCameraMotionDetector:
    """Enhanced camera motion detector"""
    
    def __init__(self, motion_sensitivity=1.0, temporal_window=5):
        self.motion_sensitivity = motion_sensitivity
        self.temporal_window = temporal_window
        
        self.lk_params = dict(winSize=(15, 15), maxLevel=2,
                             criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 20, 0.01))
        
        self.base_zoom_threshold = 0.008
        self.base_pan_threshold = 1.5
        self.base_rotation_threshold = 0.015
        
        self.immediate_zoom_threshold = 0.02
        self.immediate_pan_threshold = 3.0
        self.immediate_rotation_threshold = 0.03
        
        self.current_zoom_level = 1.0
        self.zoom_history = deque(maxlen=10)
        
        self.motion_history = deque(maxlen=temporal_window)
        self.motion_confidence_threshold = 0.5
        
        self.prev_motion_state = False
        self.transition_frames = 0
        self.max_transition_frames = 5
        
        self.water_motion_threshold = 0.3
        self.high_detection_count_threshold = 30
        self.zoom_shake_threshold = 1.015
        
        self.frame_width = None
        self.frame_height = None
        
    def initialize(self, frame_shape):
        """Initialize camera motion detector"""
        self.frame_height, self.frame_width = frame_shape
        
    def update_zoom_level(self, zoom_factor):
        """Update current zoom level"""
        self.zoom_history.append(zoom_factor)
        if len(self.zoom_history) >= 5:
            self.current_zoom_level = np.median(list(self.zoom_history))
        else:
            self.current_zoom_level = zoom_factor
            
    def get_adaptive_thresholds(self):
        """Get motion thresholds adapted to current zoom level"""
        zoom_factor = max(1.0, self.current_zoom_level)
        sensitivity_multiplier = 1.0 / zoom_factor
        
        adapted_pan_threshold = self.base_pan_threshold * sensitivity_multiplier
        adapted_rotation_threshold = self.base_rotation_threshold * sensitivity_multiplier
        
        return {
            'zoom': self.base_zoom_threshold,
            'pan': max(0.8, adapted_pan_threshold),
            'rotation': max(0.008, adapted_rotation_threshold),
            'immediate_pan': self.immediate_pan_threshold * sensitivity_multiplier,
            'immediate_rotation': self.immediate_rotation_threshold * sensitivity_multiplier
        }
    
    def detect_water_motion(self, flow_vectors):
        """Detect water/tide-like motion patterns"""
        if len(flow_vectors) < 10:
            return False
            
        mean_flow = np.mean(flow_vectors, axis=0)
        flow_deviations = flow_vectors - mean_flow
        flow_magnitudes = np.linalg.norm(flow_deviations, axis=1)
        
        coherence = 1.0 - (np.std(flow_magnitudes) / (np.mean(flow_magnitudes) + 1e-6))
        horizontal_bias = abs(mean_flow[0]) / (abs(mean_flow[1]) + 1e-6)
        
        is_water_motion = (coherence > self.water_motion_threshold and 
                          horizontal_bias > 2.0 and 
                          np.linalg.norm(mean_flow) > 0.5)
        
        return is_water_motion

    def detect_camera_motion(self, old_gray, new_gray, bird_count=0):
        """Enhanced camera motion detection with conservative transitions"""
        sample_points = []
        step = 50
        border = 60

        for y in range(border, self.frame_height - border, step):
            for x in range(border, self.frame_width - border, step):
                sample_points.append([x, y])

        if len(sample_points) < 6:
            motion_detected = False
            motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0, "immediate": False}
        else:
            sample_points = np.array(sample_points, dtype=np.float32).reshape(-1, 1, 2)

            new_points, status, error = cv2.calcOpticalFlowPyrLK(
                old_gray, new_gray, sample_points, None, **self.lk_params)

            good_mask = (status == 1) & (error < 15)

            if np.sum(good_mask) < 6:
                motion_detected = False
                motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0, "immediate": False}
            else:
                old_pts = sample_points[good_mask.flatten()].reshape(-1, 2)
                new_pts = new_points[good_mask.flatten()].reshape(-1, 2)

                flow_vectors = new_pts - old_pts
                is_water_motion = self.detect_water_motion(flow_vectors)

                try:
                    transform_result = cv2.estimateAffinePartial2D(
                        old_pts, new_pts, method=cv2.RANSAC,
                        ransacReprojThreshold=1.5, maxIters=1000, confidence=0.95)

                    transform_matrix = transform_result[0]
                    inlier_mask = transform_result[1]

                    if transform_matrix is None or inlier_mask is None:
                        motion_detected = False
                        motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0, "immediate": False}
                    else:
                        confidence = np.sum(inlier_mask) / len(inlier_mask) if len(inlier_mask) > 0 else 0

                        a = transform_matrix[0, 0]
                        b = transform_matrix[0, 1]
                        tx = transform_matrix[0, 2]
                        ty = transform_matrix[1, 2]

                        scale = np.sqrt(a*a + b*b)
                        rotation = np.arctan2(b, a)
                        pan_x = tx * self.motion_sensitivity
                        pan_y = ty * self.motion_sensitivity

                        self.update_zoom_level(scale)
                        thresholds = self.get_adaptive_thresholds()

                        motion_params = {
                            "zoom": scale,
                            "pan_x": pan_x,
                            "pan_y": pan_y,
                            "rotation": rotation,
                            "confidence": confidence,
                            "immediate": False,
                            "zoom_level": self.current_zoom_level,
                            "water_motion": is_water_motion
                        }

                        zoom_change = abs(scale - 1.0)
                        pan_magnitude = np.sqrt(pan_x*pan_x + pan_y*pan_y)
                        rotation_magnitude = abs(rotation)

                        immediate_motion = (confidence > 0.6 and (
                            zoom_change > self.immediate_zoom_threshold or
                            pan_magnitude > thresholds['immediate_pan'] or
                            rotation_magnitude > thresholds['immediate_rotation']))

                        normal_motion = (confidence > 0.7 and (
                            zoom_change > thresholds['zoom'] or
                            pan_magnitude > thresholds['pan'] or
                            rotation_magnitude > thresholds['rotation']))

                        if is_water_motion and confidence > 0.5:
                            motion_detected = True
                            motion_params["immediate"] = False
                            motion_params["water_motion_detected"] = True
                        elif (self.current_zoom_level > self.zoom_shake_threshold and
                              bird_count > self.high_detection_count_threshold):
                            motion_detected = True
                            motion_params["immediate"] = True
                            motion_params["false_positive_suppression"] = True
                        else:
                            motion_detected = immediate_motion or normal_motion
                            motion_params["immediate"] = immediate_motion
                            motion_params["false_positive_suppression"] = False
                            motion_params["water_motion_detected"] = False

                except Exception as e:
                    motion_detected = False
                    motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0, "immediate": False}

        # Conservative transition handling
        if motion_detected != self.prev_motion_state:
            self.transition_frames = 0
            if motion_detected:
                if (motion_params.get("immediate", False) or
                    motion_params.get("water_motion_detected", False) or
                    motion_params.get("false_positive_suppression", False)):
                    final_motion_detected = True
                else:
                    self.motion_history.append(motion_detected)
                    motion_ratio = sum(self.motion_history) / len(self.motion_history)
                    final_motion_detected = motion_ratio >= 0.7
            else:
                self.motion_history.append(motion_detected)
                if len(self.motion_history) >= 3:
                    motion_ratio = sum(self.motion_history) / len(self.motion_history)
                    final_motion_detected = motion_ratio >= 0.3
                else:
                    final_motion_detected = self.prev_motion_state
        else:
            self.transition_frames += 1
            self.motion_history.append(motion_detected)

            if self.transition_frames < self.max_transition_frames:
                if motion_detected and (motion_params.get("immediate", False) or
                                      motion_params.get("water_motion_detected", False) or
                                      motion_params.get("false_positive_suppression", False)):
                    final_motion_detected = True
                else:
                    final_motion_detected = self.prev_motion_state
            else:
                motion_ratio = sum(self.motion_history) / len(self.motion_history)
                final_motion_detected = motion_ratio >= self.motion_confidence_threshold

        self.prev_motion_state = final_motion_detected

        motion_params["temporal_confidence"] = sum(self.motion_history) / len(self.motion_history) if self.motion_history else 0
        motion_params["transition_frames"] = self.transition_frames

        return final_motion_detected, motion_params

def detect_birds_hybrid(input_path, output_path, yolo_model_path="./best0313.pt", yolo_confidence=0.3, csv_path=None):
    """
    Hybrid bird detection: YOLO always active, fallback to temporal confidence when YOLO finds nothing and camera is stable.
    """

    cap = cv2.VideoCapture(input_path)

    if not cap.isOpened():
        print(f"Error: Could not open video file {input_path}")
        return False

    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    print(f"Processing video: {width}x{height} @ {fps}fps, {total_frames} frames")
    print(f"Hybrid detection: YOLO always active, temporal fallback only when camera stable and YOLO finds nothing")
    print(f"YOLO model: {yolo_model_path}")
    print(f"Movement start delay: 5 frames after movement begins")
    print(f"Movement end delay: 10 frames after movement ends")

    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

    # Initialize detectors
    yolo_detector = YOLOBirdDetector(yolo_model_path, yolo_confidence)
    motion_detector = ZoomAwareCameraMotionDetector(motion_sensitivity=1.0, temporal_window=5)
    motion_detector.initialize((height, width))

    frame_detector = FrameAveragingDetector(history_frames=10)
    confident_tracker = ConfidentBirdTracker(
        confidence_frames=5,
        confidence_threshold=0.6,
        movement_threshold=5.0
    )

    # Movement delay tracking
    movement_end_delay_frames = 10  # Wait 10 frames after movement ends
    movement_start_delay_frames = 5  # Wait 5 frames after movement starts
    frames_since_movement_end = 0
    frames_since_movement_start = 0

    # Prepare CSV writer
    csv_file = None
    csv_writer = None
    if csv_path:
        csv_file = open(csv_path, 'w', newline='')
        csv_writer = csv.writer(csv_file)
        csv_writer.writerow(['frame_number', 'timestamp', 'camera_moving', 'yolo_count', 'temporal_count', 'final_count', 'detection_source', 'detections', 'motion_info'])

    ret, prev_frame = cap.read()
    if not ret:
        print("Error: Could not read first frame")
        return False

    frame_detector.update_average(prev_frame)
    prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)

    frame_count = 0
    camera_moving_frames = 0
    yolo_detection_frames = 0
    temporal_detection_frames = 0
    hybrid_detection_frames = 0
    total_yolo_detections = 0
    total_temporal_detections = 0
    movement_delay_frames = 0
    movement_start_delay_frames_count = 0

    while True:
        ret, current_frame = cap.read()
        if not ret:
            break

        frame_count += 1
        timestamp = cap.get(cv2.CAP_PROP_POS_MSEC) / 1000.0

        current_gray = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY)

        # Update frame averaging for fallback detection
        avg_ready = frame_detector.update_average(current_frame)

        # Check for camera movement first
        is_camera_moving, motion_params = motion_detector.detect_camera_motion(
            prev_gray, current_gray, 0)

        # Track movement delays
        if is_camera_moving:
            frames_since_movement_end = 0  # Reset end delay counter
            frames_since_movement_start += 1  # Increment start delay counter
            camera_moving_frames += 1
        else:
            frames_since_movement_end += 1  # Increment end delay counter
            frames_since_movement_start = 0  # Reset start delay counter

        # Always try YOLO detection (YOLO works during camera movement)
        yolo_detections = yolo_detector.detect_birds(current_frame)

        final_detections = []
        detection_source = "NONE"
        temporal_detections = []

        if is_camera_moving:
            # Camera is moving - check if we're past the start delay
            if frames_since_movement_start >= movement_start_delay_frames:
                # Past start delay - use YOLO
                if len(yolo_detections) > 0:
                    final_detections = yolo_detections
                    detection_source = "YOLO_MOVING"
                    yolo_detection_frames += 1
                    total_yolo_detections += len(yolo_detections)
                else:
                    if motion_params.get("water_motion_detected", False):
                        detection_source = "WATER_MOTION"
                    elif motion_params.get("false_positive_suppression", False):
                        detection_source = "SHAKE_SUPPRESSION"
                    elif motion_params.get("immediate", False):
                        detection_source = "IMMEDIATE_MOTION"
                    else:
                        detection_source = "CAMERA_MOVING"
            else:
                # Still in start delay period
                detection_source = f"MOVEMENT_START_DELAY_{frames_since_movement_start}/{movement_start_delay_frames}"
                movement_start_delay_frames_count += 1
        else:
            # Camera is stable
            hybrid_detection_frames += 1

            if len(yolo_detections) > 0:
                # YOLO found birds - use YOLO results
                final_detections = yolo_detections
                detection_source = "YOLO_STABLE"
                yolo_detection_frames += 1
                total_yolo_detections += len(yolo_detections)
            elif frames_since_movement_end >= movement_end_delay_frames and avg_ready:
                # YOLO found nothing, camera stable for enough time - try temporal confidence
                diff_frame, binary_mask = frame_detector.detect_movement(current_frame)

                if binary_mask is not None:
                    candidate_detections = frame_detector.find_bird_candidates(binary_mask)

                    if len(candidate_detections) > 0:
                        # Add candidates to confidence tracker
                        confident_tracker.add_frame_detections(frame_count, candidate_detections)

                        # Get confident detections
                        temporal_detections = confident_tracker.get_confident_detections()

                        if len(temporal_detections) > 0:
                            final_detections = temporal_detections
                            detection_source = "TEMPORAL"
                            temporal_detection_frames += 1
                            total_temporal_detections += len(temporal_detections)
                        else:
                            detection_source = "BUILDING_CONFIDENCE"
                    else:
                        detection_source = "NO_MOVEMENT"
                else:
                    detection_source = "NO_DIFFERENCE"
            elif frames_since_movement_end < movement_end_delay_frames:
                # Still in delay period after movement end
                detection_source = f"MOVEMENT_DELAY_{frames_since_movement_end}/{movement_end_delay_frames}"
                movement_delay_frames += 1
            else:
                detection_source = "BUILDING_AVERAGE"

        # Create output frame
        output_frame = current_frame.copy()

        # Draw detection results
        if len(final_detections) > 0:
            for i, detection in enumerate(final_detections):
                x, y, w, h = detection['bbox']
                area = detection['area']
                center_x, center_y = detection['center']
                confidence = detection.get('confidence', 0.0)
                source = detection.get('source', 'Unknown')

                # Color code by source
                if source == 'YOLO':
                    color = (0, 255, 0)  # Green for YOLO
                    label_prefix = "Y"
                elif source == 'Temporal':
                    color = (255, 255, 0)  # Cyan for temporal
                    label_prefix = "T"
                else:
                    color = (0, 255, 255)  # Yellow for background
                    label_prefix = "B"

                # Draw detection
                cv2.rectangle(output_frame, (x, y), (x + w, y + h), color, 2)
                cv2.circle(output_frame, (center_x, center_y), 3, (255, 0, 255), -1)

                # Label with source and confidence
                label = f"{label_prefix}{i+1}:{area}px C:{confidence:.2f}"
                cv2.putText(output_frame, label, (x, y-5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

                # Show additional info for temporal detections
                if source == 'Temporal':
                    tracking_count = detection.get('tracking_count', 0)
                    movement_evidence = detection.get('movement_evidence', 0)
                    track_label = f"T:{tracking_count} M:{movement_evidence}"
                    cv2.putText(output_frame, track_label, (x, y+h+15),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)

        # Add status overlay
        status_color = (0, 0, 255) if is_camera_moving else (0, 255, 0)
        if motion_params.get("water_motion_detected", False):
            status_color = (255, 165, 0)
        elif motion_params.get("false_positive_suppression", False):
            status_color = (255, 0, 255)
        elif motion_params.get("immediate", False):
            status_color = (255, 255, 0)

        cv2.putText(output_frame, f"Status: {detection_source}",
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, status_color, 2)
        cv2.putText(output_frame, f"Frame: {frame_count}/{total_frames}",
                   (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(output_frame, f"YOLO: {len(yolo_detections)} | Temporal: {len(temporal_detections)} | Final: {len(final_detections)}",
                   (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(output_frame, f"Time: {timestamp:.1f}s | End: {frames_since_movement_end}/{movement_end_delay_frames} | Start: {frames_since_movement_start}/{movement_start_delay_frames}",
                   (10, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)

        # Show detection method info
        if detection_source in ["YOLO_MOVING", "YOLO_STABLE"]:
            cv2.putText(output_frame, f"YOLO Detection Active (conf>{yolo_confidence})",
                       (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        elif detection_source == "TEMPORAL":
            confidence_buffer = len(confident_tracker.candidate_history)
            cv2.putText(output_frame, f"Temporal Confidence Active ({confidence_buffer}/5)",
                       (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 2)
        elif detection_source == "BUILDING_CONFIDENCE":
            confidence_buffer = len(confident_tracker.candidate_history)
            cv2.putText(output_frame, f"Building Confidence ({confidence_buffer}/5)",
                       (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (128, 128, 255), 1)
        elif detection_source.startswith("MOVEMENT_DELAY"):
            cv2.putText(output_frame, f"Movement End Delay: {frames_since_movement_end}/{movement_end_delay_frames}",
                       (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 128, 0), 2)
        elif detection_source.startswith("MOVEMENT_START_DELAY"):
            cv2.putText(output_frame, f"Movement Start Delay: {frames_since_movement_start}/{movement_start_delay_frames}",
                       (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 128), 2)

        # Show motion parameters if camera is moving
        if is_camera_moving:
            conf = motion_params.get("confidence", 0)
            temp_conf = motion_params.get("temporal_confidence", 0)
            cv2.putText(output_frame, f"Motion Conf: {conf:.2f} TC: {temp_conf:.2f}",
                       (10, 180), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)

        # Record detection data to CSV
        if csv_writer:
            detection_data = []
            for i, detection in enumerate(final_detections):
                x, y, w, h = detection['bbox']
                area = detection['area']
                confidence = detection.get('confidence', 0.0)
                source = detection.get('source', 'Unknown')

                if source == 'Temporal':
                    tracking_count = detection.get('tracking_count', 0)
                    movement_evidence = detection.get('movement_evidence', 0)
                    detection_data.append(f"{source[0]}{i+1}:({x},{y},{w},{h},{area},{confidence:.3f},{tracking_count},{movement_evidence})")
                else:
                    detection_data.append(f"{source[0]}{i+1}:({x},{y},{w},{h},{area},{confidence:.3f})")

            motion_info = f"Z:{motion_params['zoom']:.3f},P:{motion_params['pan_x']:.1f},{motion_params['pan_y']:.1f},R:{motion_params['rotation']:.3f},C:{motion_params.get('confidence', 0):.2f},TC:{motion_params.get('temporal_confidence', 0):.2f},TF:{motion_params.get('transition_frames', 0)},WM:{motion_params.get('water_motion_detected', False)},FPS:{motion_params.get('false_positive_suppression', False)},CB:{len(confident_tracker.candidate_history)},FSME:{frames_since_movement_end},FSMS:{frames_since_movement_start},MDF:{movement_delay_frames},MSDF:{movement_start_delay_frames_count}"

            csv_writer.writerow([
                frame_count,
                f"{timestamp:.3f}",
                is_camera_moving,
                len(yolo_detections),
                len(temporal_detections),
                len(final_detections),
                detection_source,
                "|".join(detection_data) if detection_data else "None",
                motion_info
            ])

        out.write(output_frame)
        prev_gray = current_gray.copy()

        # Progress indicator
        if frame_count % 100 == 0 or frame_count == total_frames:
            progress = (frame_count / total_frames) * 100
            print(f"Progress: {progress:.1f}% ({frame_count}/{total_frames}) | "
                  f"YOLO: {len(yolo_detections)} | Temporal: {len(temporal_detections)} | Final: {len(final_detections)} | Source: {detection_source}")

    cap.release()
    out.release()
    if csv_file:
        csv_file.close()
    cv2.destroyAllWindows()

    print(f"Hybrid bird detection video saved to: {output_path}")
    print(f"Camera moving frames: {camera_moving_frames}/{frame_count} ({camera_moving_frames/frame_count*100:.1f}%)")
    print(f"Movement end delay frames: {movement_delay_frames}/{frame_count} ({movement_delay_frames/frame_count*100:.1f}%)")
    print(f"Movement start delay frames: {movement_start_delay_frames_count}/{frame_count} ({movement_start_delay_frames_count/frame_count*100:.1f}%)")
    print(f"YOLO detection frames: {yolo_detection_frames}/{frame_count} ({yolo_detection_frames/frame_count*100:.1f}%)")
    print(f"Temporal detection frames: {temporal_detection_frames}/{frame_count} ({temporal_detection_frames/frame_count*100:.1f}%)")
    print(f"Hybrid detection frames: {hybrid_detection_frames}/{frame_count} ({hybrid_detection_frames/frame_count*100:.1f}%)")
    print(f"Total YOLO detections: {total_yolo_detections}")
    print(f"Total temporal detections: {total_temporal_detections}")
    if csv_path:
        print(f"Detection data saved to: {csv_path}")
    return True

def main():
    parser = argparse.ArgumentParser(description='Hybrid bird detection: YOLO first, fallback to temporal confidence')
    parser.add_argument('input', help='Input video file path')
    parser.add_argument('output', help='Output video file path')
    parser.add_argument('--yolo-model', default='./best0313.pt',
                       help='Path to YOLO model file (default: ./best0313.pt)')
    parser.add_argument('--yolo-confidence', type=float, default=0.3,
                       help='YOLO confidence threshold (default: 0.3)')
    parser.add_argument('--csv', help='Optional path to save detection data as CSV')

    args = parser.parse_args()

    if not os.path.exists(args.input):
        print(f"Error: Input file '{args.input}' does not exist")
        return

    if not os.path.exists(args.yolo_model):
        print(f"Error: YOLO model file '{args.yolo_model}' does not exist")
        return

    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    if args.csv:
        csv_dir = os.path.dirname(args.csv) or '.'
        if not os.path.exists(csv_dir):
            os.makedirs(csv_dir)

    print(f"Input: {args.input}")
    print(f"Output: {args.output}")
    print(f"YOLO Model: {args.yolo_model}")
    print(f"YOLO Confidence: {args.yolo_confidence}")
    if args.csv:
        print(f"CSV Output: {args.csv}")

    success = detect_birds_hybrid(
        args.input, args.output, args.yolo_model, args.yolo_confidence, args.csv
    )

    if success:
        print("Hybrid bird detection completed successfully!")
    else:
        print("Hybrid bird detection failed!")

if __name__ == "__main__":
    main()
