import cv2
import numpy as np
import argparse
import os
import csv
from datetime import datetime

def process_video_motion_detection(input_path, output_path, threshold=30, blur_kernel=5, csv_path=None):
    """
    Process video to detect motion by computing frame differences and record dynamic pixels.
    
    Args:
        input_path (str): Path to input video file
        output_path (str): Path to output video file
        threshold (int): Threshold for motion detection (0-255)
        blur_kernel (int): Kernel size for Gaussian blur (odd number)
        csv_path (str): Optional path to save dynamic pixel data
    """
    
    # Open the input video
    cap = cv2.VideoCapture(input_path)
    
    if not cap.isOpened():
        print(f"Error: Could not open video file {input_path}")
        return False
    
    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"Processing video: {width}x{height} @ {fps}fps, {total_frames} frames")
    
    # Define the codec and create VideoWriter object
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    # Prepare CSV writer if needed
    csv_file = None
    csv_writer = None
    if csv_path:
        csv_file = open(csv_path, 'w', newline='')
        csv_writer = csv.writer(csv_file)
        csv_writer.writerow(['frame_number', 'timestamp', 'dynamic_pixels', 'motion_percentage'])
    
    # Read first frame
    ret, prev_frame = cap.read()
    if not ret:
        print("Error: Could not read first frame")
        return False
    
    # Convert to grayscale for motion detection
    prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)
    prev_gray = cv2.GaussianBlur(prev_gray, (blur_kernel, blur_kernel), 0)
    
    frame_count = 0
    
    while True:
        ret, current_frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        timestamp = cap.get(cv2.CAP_PROP_POS_MSEC) / 1000.0  # Current timestamp in seconds
        
        # Convert current frame to grayscale
        current_gray = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY)
        current_gray = cv2.GaussianBlur(current_gray, (blur_kernel, blur_kernel), 0)
        
        # Compute absolute difference between current and previous frame
        frame_diff = cv2.absdiff(prev_gray, current_gray)
        
        # Apply threshold to get binary image
        _, thresh = cv2.threshold(frame_diff, threshold, 255, cv2.THRESH_BINARY)
        
        # Calculate dynamic pixel statistics
        dynamic_pixels = np.count_nonzero(thresh)
        total_pixels = width * height
        motion_percentage = (dynamic_pixels / total_pixels) * 100
        
        # Record to CSV if enabled
        if csv_writer:
            csv_writer.writerow([
                frame_count, 
                f"{timestamp:.3f}", 
                dynamic_pixels, 
                f"{motion_percentage:.4f}"
            ])
        
        # Optional: Apply morphological operations to reduce noise
        kernel = np.ones((3, 3), np.uint8)
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        
        # Create visualization
        # Show difference as heatmap
        diff_colored = cv2.applyColorMap(frame_diff, cv2.COLORMAP_JET)
        
        # Overlay motion areas on original frame (optional)
        # motion_overlay = current_frame.copy()
        # motion_overlay[thresh > 0] = [0, 0, 255]  # Red for motion areas
        
        # Use the heatmap for output
        output_frame = diff_colored.copy()
        
        # Add text overlay with motion statistics
        cv2.putText(output_frame, f"Frame: {frame_count}/{total_frames}", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(output_frame, f"Motion: {dynamic_pixels} px ({motion_percentage:.2f}%)", 
                   (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(output_frame, f"Time: {timestamp:.1f}s", 
                   (10, 110), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # Write the frame
        out.write(output_frame)
        
        # Update previous frame
        prev_gray = current_gray.copy()
        
        # Progress indicator
        if frame_count % 100 == 0 or frame_count == total_frames:
            progress = (frame_count / total_frames) * 100
            print(f"Progress: {progress:.1f}% ({frame_count}/{total_frames}) | "
                  f"Motion: {dynamic_pixels} px ({motion_percentage:.2f}%)")
    
    # Release everything
    cap.release()
    out.release()
    if csv_file:
        csv_file.close()
    cv2.destroyAllWindows()
    
    print(f"Motion detection video saved to: {output_path}")
    if csv_path:
        print(f"Dynamic pixel data saved to: {csv_path}")
    return True

def process_video_simple_diff(input_path, output_path, enhance_factor=3.0, csv_path=None):
    """
    Simpler version that just shows enhanced frame differences and records dynamic pixels.
    
    Args:
        input_path (str): Path to input video file
        output_path (str): Path to output video file
        enhance_factor (float): Factor to enhance the difference visibility
        csv_path (str): Optional path to save dynamic pixel data
    """
    
    cap = cv2.VideoCapture(input_path)
    
    if not cap.isOpened():
        print(f"Error: Could not open video file {input_path}")
        return False
    
    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # Define the codec and create VideoWriter object
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    # Prepare CSV writer if needed
    csv_file = None
    csv_writer = None
    if csv_path:
        csv_file = open(csv_path, 'w', newline='')
        csv_writer = csv.writer(csv_file)
        csv_writer.writerow(['frame_number', 'timestamp', 'dynamic_pixels', 'motion_percentage'])
    
    # Read first frame
    ret, prev_frame = cap.read()
    if not ret:
        print("Error: Could not read first frame")
        return False
    
    # Convert to grayscale for better difference calculation
    prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)
    
    frame_count = 0
    
    while True:
        ret, current_frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        timestamp = cap.get(cv2.CAP_PROP_POS_MSEC) / 1000.0  # Current timestamp in seconds
        
        # Convert to grayscale
        current_gray = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY)
        
        # Compute absolute difference
        diff = cv2.absdiff(prev_gray, current_gray)
        
        # Calculate dynamic pixel statistics
        _, thresh = cv2.threshold(diff, 30, 255, cv2.THRESH_BINARY)
        dynamic_pixels = np.count_nonzero(thresh)
        total_pixels = width * height
        motion_percentage = (dynamic_pixels / total_pixels) * 100
        
        # Record to CSV if enabled
        if csv_writer:
            csv_writer.writerow([
                frame_count, 
                f"{timestamp:.3f}", 
                dynamic_pixels, 
                f"{motion_percentage:.4f}"
            ])
        
        # Enhance the difference
        diff_enhanced = cv2.multiply(diff, enhance_factor)
        diff_enhanced = np.clip(diff_enhanced, 0, 255).astype(np.uint8)
        
        # Convert back to color for output
        diff_colored = cv2.cvtColor(diff_enhanced, cv2.COLOR_GRAY2BGR)
        
        # Add text overlay with motion statistics
        cv2.putText(diff_colored, f"Frame: {frame_count}/{total_frames}", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(diff_colored, f"Motion: {dynamic_pixels} px ({motion_percentage:.2f}%)", 
                   (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(diff_colored, f"Time: {timestamp:.1f}s", 
                   (10, 110), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # Write the frame
        out.write(diff_colored)
        
        # Update previous frame
        prev_gray = current_gray.copy()
        
        # Progress indicator
        if frame_count % 100 == 0 or frame_count == total_frames:
            progress = (frame_count / total_frames) * 100
            print(f"Progress: {progress:.1f}% ({frame_count}/{total_frames}) | "
                  f"Motion: {dynamic_pixels} px ({motion_percentage:.2f}%)")
    
    # Release everything
    cap.release()
    out.release()
    if csv_file:
        csv_file.close()
    cv2.destroyAllWindows()
    
    print(f"Simple difference video saved to: {output_path}")
    if csv_path:
        print(f"Dynamic pixel data saved to: {csv_path}")
    return True

def main():
    parser = argparse.ArgumentParser(description='Detect motion in video by frame differencing and record dynamic pixels')
    parser.add_argument('input', help='Input video file path')
    parser.add_argument('output', help='Output video file path')
    parser.add_argument('--mode', choices=['advanced', 'simple'], default='advanced',
                       help='Processing mode (default: advanced)')
    parser.add_argument('--threshold', type=int, default=30,
                       help='Motion detection threshold (0-255, default: 30)')
    parser.add_argument('--blur', type=int, default=5,
                       help='Blur kernel size (odd number, default: 5)')
    parser.add_argument('--enhance', type=float, default=3.0,
                       help='Enhancement factor for simple mode (default: 3.0)')
    parser.add_argument('--csv', help='Optional path to save dynamic pixel data as CSV')
    
    args = parser.parse_args()
    
    # Check if input file exists
    if not os.path.exists(args.input):
        print(f"Error: Input file '{args.input}' does not exist")
        return
    
    # Create output directory if it doesn't exist
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Create CSV directory if specified
    if args.csv:
        csv_dir = os.path.dirname(args.csv) or '.'
        if not os.path.exists(csv_dir):
            os.makedirs(csv_dir)
    
    print(f"Input: {args.input}")
    print(f"Output: {args.output}")
    print(f"Mode: {args.mode}")
    if args.csv:
        print(f"CSV Output: {args.csv}")
    
    if args.mode == 'advanced':
        success = process_video_motion_detection(
            args.input, args.output, args.threshold, args.blur, args.csv
        )
    else:
        success = process_video_simple_diff(
            args.input, args.output, args.enhance, args.csv
        )
    
    if success:
        print("Processing completed successfully!")
    else:
        print("Processing failed!")

if __name__ == "__main__":
    main()
