import cv2
import numpy as np
import argparse
import os
import csv
from collections import deque
from datetime import datetime
from scipy import ndimage
from sklearn.cluster import KMeans

class ConvolutionalBirdDetector:
    """Advanced bird detection using convolutional kernels and color pattern matching"""
    
    def __init__(self, kernel_sizes=[3, 5, 7], color_clusters=5):
        self.kernel_sizes = kernel_sizes
        self.color_clusters = color_clusters
        
        # Create bird-like detection kernels
        self.bird_kernels = self._create_bird_kernels()
        
        # Color pattern analysis
        self.dominant_colors = None
        self.color_tolerance = 30
        
        # Response thresholds
        self.min_response_strength = 0.3
        self.consistency_threshold = 0.6
        
    def _create_bird_kernels(self):
        """Create convolutional kernels that match bird-like patterns"""
        kernels = []
        
        for size in self.kernel_sizes:
            # Circular/elliptical bird body kernel
            kernel_circular = np.zeros((size, size), dtype=np.float32)
            center = size // 2
            for i in range(size):
                for j in range(size):
                    dist = np.sqrt((i - center)**2 + (j - center)**2)
                    if dist <= center * 0.8:  # Bird body
                        kernel_circular[i, j] = 1.0
                    elif dist <= center:  # Edge transition
                        kernel_circular[i, j] = -0.5
            kernels.append(('circular', kernel_circular))
            
            # Elongated bird kernel (flying bird shape)
            kernel_elongated = np.zeros((size, size), dtype=np.float32)
            for i in range(size):
                for j in range(size):
                    # Elliptical shape (wider than tall)
                    ellipse_val = ((i - center) / (center * 0.6))**2 + ((j - center) / (center * 1.0))**2
                    if ellipse_val <= 1.0:
                        kernel_elongated[i, j] = 1.0
                    elif ellipse_val <= 1.5:
                        kernel_elongated[i, j] = -0.3
            kernels.append(('elongated', kernel_elongated))
            
            # Small dot kernel (distant bird)
            if size >= 5:
                kernel_dot = np.zeros((size, size), dtype=np.float32)
                kernel_dot[center-1:center+2, center-1:center+2] = 1.0
                kernel_dot[center, center] = 2.0  # Strong center
                # Negative surround
                for i in range(size):
                    for j in range(size):
                        if kernel_dot[i, j] == 0:
                            dist = np.sqrt((i - center)**2 + (j - center)**2)
                            if dist <= center:
                                kernel_dot[i, j] = -0.2
                kernels.append(('dot', kernel_dot))
        
        return kernels
    
    def analyze_color_patterns(self, frame):
        """Analyze dominant colors in the frame to identify bird-like colors"""
        # Convert to LAB color space for better color analysis
        lab_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2LAB)
        
        # Sample pixels for color analysis (avoid edges)
        h, w = lab_frame.shape[:2]
        sample_points = []
        step = 20
        for y in range(step, h-step, step):
            for x in range(step, w-step, step):
                sample_points.append(lab_frame[y, x])
        
        if len(sample_points) > self.color_clusters:
            # Cluster colors to find dominant patterns
            kmeans = KMeans(n_clusters=self.color_clusters, random_state=42, n_init=10)
            sample_points = np.array(sample_points)
            kmeans.fit(sample_points)
            self.dominant_colors = kmeans.cluster_centers_
        
        return self.dominant_colors is not None
    
    def create_color_mask(self, frame):
        """Create mask highlighting bird-like colors"""
        if self.dominant_colors is None:
            return None
        
        lab_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2LAB)
        color_mask = np.zeros(lab_frame.shape[:2], dtype=np.uint8)
        
        # For each dominant color, create a mask
        for color in self.dominant_colors:
            # Calculate distance to this color
            diff = np.sqrt(np.sum((lab_frame - color)**2, axis=2))
            
            # Create mask for pixels similar to this color
            mask = (diff < self.color_tolerance).astype(np.uint8) * 255
            
            # Only keep small connected components (bird-sized)
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            for contour in contours:
                area = cv2.contourArea(contour)
                if 10 <= area <= 200:  # Bird-sized areas
                    cv2.fillPoly(color_mask, [contour], 255)
        
        return color_mask
    
    def apply_convolution_detection(self, frame):
        """Apply convolutional kernels to detect bird-like patterns"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Normalize frame
        gray_norm = gray.astype(np.float32) / 255.0
        
        detection_responses = []
        
        for kernel_name, kernel in self.bird_kernels:
            # Apply convolution
            response = cv2.filter2D(gray_norm, -1, kernel)
            
            # Find strong responses
            response_abs = np.abs(response)
            
            # Threshold responses
            strong_responses = response_abs > self.min_response_strength
            
            # Find local maxima
            local_maxima = ndimage.maximum_filter(response_abs, size=5) == response_abs
            
            # Combine conditions
            detection_mask = strong_responses & local_maxima
            
            # Find detection points
            y_coords, x_coords = np.where(detection_mask)
            
            for y, x in zip(y_coords, x_coords):
                strength = response_abs[y, x]
                detection_responses.append({
                    'x': int(x),
                    'y': int(y),
                    'strength': float(strength),
                    'kernel': kernel_name,
                    'kernel_size': kernel.shape[0]
                })
        
        return detection_responses
    
    def filter_detections_by_color(self, detections, color_mask):
        """Filter detections based on color consistency"""
        if color_mask is None:
            return detections
        
        filtered_detections = []
        
        for detection in detections:
            x, y = detection['x'], detection['y']
            kernel_size = detection['kernel_size']
            
            # Check color consistency in the detection area
            half_size = kernel_size // 2
            y1, y2 = max(0, y - half_size), min(color_mask.shape[0], y + half_size + 1)
            x1, x2 = max(0, x - half_size), min(color_mask.shape[1], x + half_size + 1)
            
            region = color_mask[y1:y2, x1:x2]
            if region.size > 0:
                color_consistency = np.mean(region) / 255.0
                
                # Keep detections with good color consistency
                if color_consistency > self.consistency_threshold:
                    detection['color_consistency'] = color_consistency
                    filtered_detections.append(detection)
        
        return filtered_detections
    
    def merge_nearby_detections(self, detections, merge_distance=10):
        """Merge detections that are close to each other"""
        if not detections:
            return []
        
        merged = []
        used = set()
        
        for i, det1 in enumerate(detections):
            if i in used:
                continue
            
            # Find nearby detections
            group = [det1]
            used.add(i)
            
            for j, det2 in enumerate(detections):
                if j in used:
                    continue
                
                dist = np.sqrt((det1['x'] - det2['x'])**2 + (det1['y'] - det2['y'])**2)
                if dist < merge_distance:
                    group.append(det2)
                    used.add(j)
            
            # Merge group into single detection
            if group:
                avg_x = int(np.mean([d['x'] for d in group]))
                avg_y = int(np.mean([d['y'] for d in group]))
                max_strength = max([d['strength'] for d in group])
                avg_color = np.mean([d.get('color_consistency', 0) for d in group])
                
                merged.append({
                    'x': avg_x,
                    'y': avg_y,
                    'strength': max_strength,
                    'color_consistency': avg_color,
                    'group_size': len(group)
                })
        
        return merged

class ZoomAwareCameraMotionDetector:
    """Enhanced camera motion detector with zoom-aware thresholds and water motion filtering"""
    
    def __init__(self, motion_sensitivity=1.0, temporal_window=3):
        self.motion_sensitivity = motion_sensitivity
        self.temporal_window = temporal_window
        
        # Parameters for optical flow
        self.lk_params = dict(winSize=(15, 15),
                             maxLevel=2,
                             criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 20, 0.01))
        
        # Base motion thresholds
        self.base_zoom_threshold = 0.008
        self.base_pan_threshold = 1.5
        self.base_rotation_threshold = 0.015
        
        # Immediate response thresholds
        self.immediate_zoom_threshold = 0.02
        self.immediate_pan_threshold = 3.0
        self.immediate_rotation_threshold = 0.03
        
        # Zoom level tracking and adaptive thresholds
        self.current_zoom_level = 1.0
        self.zoom_history = deque(maxlen=10)
        self.zoom_change_threshold = 0.005
        
        # Temporal filtering
        self.motion_history = deque(maxlen=temporal_window)
        self.motion_confidence_threshold = 0.5
        
        # Transition detection
        self.prev_motion_state = False
        self.transition_frames = 0
        self.max_transition_frames = 3
        
        # Water/tide motion detection
        self.water_motion_threshold = 0.3
        self.water_detection_enabled = True
        
        # False positive detection for high zoom
        self.high_detection_count_threshold = 50
        self.zoom_shake_threshold = 1.015
        
        self.frame_width = None
        self.frame_height = None
        
    def initialize(self, frame_shape):
        """Initialize camera motion detector"""
        self.frame_height, self.frame_width = frame_shape
        
    def update_zoom_level(self, zoom_factor):
        """Update current zoom level and detect zoom changes"""
        self.zoom_history.append(zoom_factor)
        if len(self.zoom_history) >= 5:
            self.current_zoom_level = np.median(list(self.zoom_history))
        else:
            self.current_zoom_level = zoom_factor
            
    def get_adaptive_thresholds(self):
        """Get motion thresholds adapted to current zoom level"""
        zoom_factor = max(1.0, self.current_zoom_level)
        sensitivity_multiplier = 1.0 / zoom_factor
        
        adapted_pan_threshold = self.base_pan_threshold * sensitivity_multiplier
        adapted_rotation_threshold = self.base_rotation_threshold * sensitivity_multiplier
        
        return {
            'zoom': self.base_zoom_threshold,
            'pan': max(0.8, adapted_pan_threshold),
            'rotation': max(0.008, adapted_rotation_threshold),
            'immediate_pan': self.immediate_pan_threshold * sensitivity_multiplier,
            'immediate_rotation': self.immediate_rotation_threshold * sensitivity_multiplier
        }
    
    def detect_water_motion(self, flow_vectors):
        """Detect water/tide-like motion patterns"""
        if len(flow_vectors) < 10:
            return False
            
        # Calculate motion coherence (how similar the motion vectors are)
        mean_flow = np.mean(flow_vectors, axis=0)
        flow_deviations = flow_vectors - mean_flow
        flow_magnitudes = np.linalg.norm(flow_deviations, axis=1)
        
        # Water motion tends to be coherent (low deviation) and horizontal
        coherence = 1.0 - (np.std(flow_magnitudes) / (np.mean(flow_magnitudes) + 1e-6))
        
        # Check if motion is predominantly horizontal (water/tide characteristic)
        horizontal_bias = abs(mean_flow[0]) / (abs(mean_flow[1]) + 1e-6)
        
        # Water motion detection criteria
        is_water_motion = (coherence > self.water_motion_threshold and 
                          horizontal_bias > 2.0 and 
                          np.linalg.norm(mean_flow) > 0.5)
        
        return is_water_motion

    def detect_camera_motion(self, old_gray, new_gray, bird_count=0):
        """Enhanced camera motion detection with water motion filtering"""
        # Select stable sample points
        sample_points = []
        step = 50
        border = 60

        for y in range(border, self.frame_height - border, step):
            for x in range(border, self.frame_width - border, step):
                sample_points.append([x, y])

        if len(sample_points) < 6:
            motion_detected = False
            motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0, "immediate": False}
        else:
            sample_points = np.array(sample_points, dtype=np.float32).reshape(-1, 1, 2)

            # Calculate optical flow
            new_points, status, error = cv2.calcOpticalFlowPyrLK(
                old_gray, new_gray, sample_points, None, **self.lk_params)

            # Filter good points
            good_mask = (status == 1) & (error < 15)

            if np.sum(good_mask) < 6:
                motion_detected = False
                motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0, "immediate": False}
            else:
                old_pts = sample_points[good_mask.flatten()].reshape(-1, 2)
                new_pts = new_points[good_mask.flatten()].reshape(-1, 2)

                # Calculate flow vectors for water detection
                flow_vectors = new_pts - old_pts
                is_water_motion = self.detect_water_motion(flow_vectors) if self.water_detection_enabled else False

                # Calculate transformation with RANSAC
                try:
                    transform_result = cv2.estimateAffinePartial2D(
                        old_pts, new_pts, method=cv2.RANSAC,
                        ransacReprojThreshold=1.5, maxIters=1000, confidence=0.95)

                    transform_matrix = transform_result[0]
                    inlier_mask = transform_result[1]

                    if transform_matrix is None or inlier_mask is None:
                        motion_detected = False
                        motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0, "immediate": False}
                    else:
                        # Calculate confidence
                        confidence = np.sum(inlier_mask) / len(inlier_mask) if len(inlier_mask) > 0 else 0

                        # Extract motion parameters
                        a = transform_matrix[0, 0]
                        b = transform_matrix[0, 1]
                        tx = transform_matrix[0, 2]
                        ty = transform_matrix[1, 2]

                        scale = np.sqrt(a*a + b*b)
                        rotation = np.arctan2(b, a)
                        pan_x = tx * self.motion_sensitivity
                        pan_y = ty * self.motion_sensitivity

                        # Update zoom level tracking
                        self.update_zoom_level(scale)

                        # Get adaptive thresholds
                        thresholds = self.get_adaptive_thresholds()

                        motion_params = {
                            "zoom": scale,
                            "pan_x": pan_x,
                            "pan_y": pan_y,
                            "rotation": rotation,
                            "confidence": confidence,
                            "immediate": False,
                            "zoom_level": self.current_zoom_level,
                            "water_motion": is_water_motion
                        }

                        # Check for immediate response (strong motion)
                        zoom_change = abs(scale - 1.0)
                        pan_magnitude = np.sqrt(pan_x*pan_x + pan_y*pan_y)
                        rotation_magnitude = abs(rotation)

                        immediate_motion = (confidence > 0.6 and (
                            zoom_change > self.immediate_zoom_threshold or
                            pan_magnitude > thresholds['immediate_pan'] or
                            rotation_magnitude > thresholds['immediate_rotation']))

                        # Check for normal motion with adaptive thresholds
                        normal_motion = (confidence > 0.7 and (
                            zoom_change > thresholds['zoom'] or
                            pan_magnitude > thresholds['pan'] or
                            rotation_magnitude > thresholds['rotation']))

                        # Water motion override - if detected, treat as camera motion to pause bird detection
                        if is_water_motion and confidence > 0.5:
                            motion_detected = True
                            motion_params["immediate"] = False
                            motion_params["water_motion_detected"] = True
                        # High zoom + many detections = likely false positive from shake
                        elif (self.current_zoom_level > self.zoom_shake_threshold and
                              bird_count > self.high_detection_count_threshold):
                            motion_detected = True
                            motion_params["immediate"] = True
                            motion_params["false_positive_suppression"] = True
                        else:
                            motion_detected = immediate_motion or normal_motion
                            motion_params["immediate"] = immediate_motion
                            motion_params["false_positive_suppression"] = False
                            motion_params["water_motion_detected"] = False

                except Exception as e:
                    motion_detected = False
                    motion_params = {"zoom": 1.0, "pan_x": 0, "pan_y": 0, "rotation": 0, "confidence": 0, "immediate": False}

        # Handle transitions more responsively
        if motion_detected != self.prev_motion_state:
            self.transition_frames = 0
            if motion_detected:
                # Starting motion - immediate response if strong motion, water motion, or false positive suppression
                if (motion_params.get("immediate", False) or
                    motion_params.get("water_motion_detected", False) or
                    motion_params.get("false_positive_suppression", False)):
                    final_motion_detected = True
                else:
                    self.motion_history.append(motion_detected)
                    motion_ratio = sum(self.motion_history) / len(self.motion_history)
                    final_motion_detected = motion_ratio >= self.motion_confidence_threshold
            else:
                # Stopping motion - be more conservative
                self.motion_history.append(motion_detected)
                if len(self.motion_history) >= 2:
                    motion_ratio = sum(self.motion_history) / len(self.motion_history)
                    final_motion_detected = motion_ratio >= self.motion_confidence_threshold
                else:
                    final_motion_detected = self.prev_motion_state
        else:
            # Same state as before
            self.transition_frames += 1
            self.motion_history.append(motion_detected)

            if self.transition_frames < self.max_transition_frames:
                # Still in transition period
                if motion_detected and (motion_params.get("immediate", False) or
                                      motion_params.get("water_motion_detected", False) or
                                      motion_params.get("false_positive_suppression", False)):
                    final_motion_detected = True
                else:
                    final_motion_detected = self.prev_motion_state
            else:
                # Past transition period - use temporal filtering
                motion_ratio = sum(self.motion_history) / len(self.motion_history)
                final_motion_detected = motion_ratio >= self.motion_confidence_threshold

        # Update state
        self.prev_motion_state = final_motion_detected

        motion_params["temporal_confidence"] = sum(self.motion_history) / len(self.motion_history) if self.motion_history else 0
        motion_params["transition_frames"] = self.transition_frames

        return final_motion_detected, motion_params

def detect_birds_convolution(input_path, output_path, motion_sensitivity=1.0, csv_path=None):
    """
    Advanced bird detection using convolutional kernels and color pattern analysis.
    """

    # Open the input video
    cap = cv2.VideoCapture(input_path)

    if not cap.isOpened():
        print(f"Error: Could not open video file {input_path}")
        return False

    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    print(f"Processing video: {width}x{height} @ {fps}fps, {total_frames} frames")
    print(f"Convolutional bird detection: using color patterns and bird-like kernels")
    print(f"Advanced pattern matching with color consistency validation")

    # Define the codec and create VideoWriter object
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

    # Initialize detectors
    motion_detector = ZoomAwareCameraMotionDetector(motion_sensitivity, temporal_window=3)
    motion_detector.initialize((height, width))

    conv_detector = ConvolutionalBirdDetector(kernel_sizes=[3, 5, 7, 9], color_clusters=8)

    # Prepare CSV writer
    csv_file = None
    csv_writer = None
    if csv_path:
        csv_file = open(csv_path, 'w', newline='')
        csv_writer = csv.writer(csv_file)
        csv_writer.writerow(['frame_number', 'timestamp', 'camera_moving', 'bird_count', 'detections', 'motion_info'])

    # Read first frame
    ret, prev_frame = cap.read()
    if not ret:
        print("Error: Could not read first frame")
        return False

    # Initialize color analysis
    conv_detector.analyze_color_patterns(prev_frame)
    prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)

    frame_count = 0
    camera_moving_frames = 0
    bird_detection_frames = 0
    water_motion_frames = 0

    while True:
        ret, current_frame = cap.read()
        if not ret:
            break

        frame_count += 1
        timestamp = cap.get(cv2.CAP_PROP_POS_MSEC) / 1000.0

        # Convert current frame to grayscale
        current_gray = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY)

        # Preliminary bird detection for motion detector feedback
        preliminary_detections = conv_detector.apply_convolution_detection(current_frame)
        preliminary_bird_count = len(preliminary_detections)

        # Check for camera movement
        is_camera_moving, motion_params = motion_detector.detect_camera_motion(
            prev_gray, current_gray, preliminary_bird_count)

        bird_detections = []

        if is_camera_moving:
            camera_moving_frames += 1
            if motion_params.get("water_motion_detected", False):
                detection_status = "WATER_MOTION"
                water_motion_frames += 1
            elif motion_params.get("false_positive_suppression", False):
                detection_status = "SHAKE_SUPPRESSION"
            elif motion_params.get("immediate", False):
                detection_status = "IMMEDIATE_MOTION"
            else:
                detection_status = "CAMERA_MOVING"
        else:
            bird_detection_frames += 1
            detection_status = "CONV_DETECTING"

            # Apply convolutional detection
            conv_detections = conv_detector.apply_convolution_detection(current_frame)

            # Create color mask for filtering
            color_mask = conv_detector.create_color_mask(current_frame)

            # Filter detections by color consistency
            filtered_detections = conv_detector.filter_detections_by_color(conv_detections, color_mask)

            # Merge nearby detections
            bird_detections = conv_detector.merge_nearby_detections(filtered_detections, merge_distance=15)

        # Create output frame
        output_frame = current_frame.copy()

        # Draw detection results
        if detection_status == "CONV_DETECTING":
            for i, detection in enumerate(bird_detections):
                x, y = detection['x'], detection['y']
                strength = detection['strength']
                color_consistency = detection.get('color_consistency', 0)
                group_size = detection.get('group_size', 1)

                # Color code by strength and consistency
                if strength > 0.7 and color_consistency > 0.8:
                    color = (0, 255, 0)  # Green for high confidence
                elif strength > 0.5 and color_consistency > 0.6:
                    color = (0, 255, 255)  # Yellow for medium confidence
                else:
                    color = (0, 0, 255)  # Red for low confidence

                # Draw detection
                cv2.circle(output_frame, (x, y), 8, color, 2)
                cv2.circle(output_frame, (x, y), 2, (255, 0, 255), -1)

                # Label with strength and consistency
                label = f"B{i+1}:S{strength:.2f}C{color_consistency:.2f}"
                if group_size > 1:
                    label += f"G{group_size}"
                cv2.putText(output_frame, label, (x-20, y-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

        # Add status overlay
        status_color = (0, 0, 255) if is_camera_moving else (0, 255, 0)
        if motion_params.get("water_motion_detected", False):
            status_color = (255, 165, 0)  # Orange for water motion
        elif motion_params.get("false_positive_suppression", False):
            status_color = (255, 0, 255)  # Magenta for shake suppression
        elif motion_params.get("immediate", False):
            status_color = (255, 255, 0)  # Cyan for immediate motion

        cv2.putText(output_frame, f"Status: {detection_status}",
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, status_color, 2)
        cv2.putText(output_frame, f"Frame: {frame_count}/{total_frames}",
                   (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(output_frame, f"Conv Birds: {len(bird_detections)}",
                   (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(output_frame, f"Time: {timestamp:.1f}s",
                   (10, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        # Show convolution info
        if detection_status == "CONV_DETECTING":
            cv2.putText(output_frame, f"Kernels: {len(conv_detector.bird_kernels)} types",
                       (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            cv2.putText(output_frame, f"Colors: {conv_detector.color_clusters} clusters",
                       (10, 180), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        # Show motion parameters
        if is_camera_moving:
            conf = motion_params.get("confidence", 0)
            temp_conf = motion_params.get("temporal_confidence", 0)
            zoom_change = abs(motion_params["zoom"] - 1.0) * 100
            pan_mag = np.sqrt(motion_params["pan_x"]**2 + motion_params["pan_y"]**2)
            rot_deg = abs(np.degrees(motion_params["rotation"]))

            cv2.putText(output_frame, f"Motion: Z:{zoom_change:.1f}% P:{pan_mag:.1f}px R:{rot_deg:.1f}°",
                       (10, 210), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
            cv2.putText(output_frame, f"Conf: {conf:.2f} TC: {temp_conf:.2f}",
                       (10, 240), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)

            if motion_params.get("water_motion_detected", False):
                cv2.putText(output_frame, "WATER/TIDE MOTION DETECTED",
                           (10, 270), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 165, 0), 2)
            elif motion_params.get("false_positive_suppression", False):
                cv2.putText(output_frame, "SHAKE SUPPRESSION ACTIVE",
                           (10, 270), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 255), 2)

        # Record detection data to CSV
        if csv_writer:
            detection_data = []
            for i, detection in enumerate(bird_detections):
                x, y = detection['x'], detection['y']
                strength = detection['strength']
                color_consistency = detection.get('color_consistency', 0)
                group_size = detection.get('group_size', 1)
                detection_data.append(f"B{i+1}:({x},{y},{strength:.3f},{color_consistency:.3f},{group_size})")

            motion_info = f"Z:{motion_params['zoom']:.3f},P:{motion_params['pan_x']:.1f},{motion_params['pan_y']:.1f},R:{motion_params['rotation']:.3f},C:{motion_params.get('confidence', 0):.2f},TC:{motion_params.get('temporal_confidence', 0):.2f},WM:{motion_params.get('water_motion_detected', False)},FPS:{motion_params.get('false_positive_suppression', False)}"

            csv_writer.writerow([
                frame_count,
                f"{timestamp:.3f}",
                is_camera_moving,
                len(bird_detections),
                "|".join(detection_data) if detection_data else "None",
                motion_info
            ])

        # Write the frame
        out.write(output_frame)

        # Update previous frame
        prev_gray = current_gray.copy()

        # Progress indicator
        if frame_count % 100 == 0 or frame_count == total_frames:
            progress = (frame_count / total_frames) * 100
            print(f"Progress: {progress:.1f}% ({frame_count}/{total_frames}) | "
                  f"Conv Birds: {len(bird_detections)} | Status: {detection_status}")

    # Release everything
    cap.release()
    out.release()
    if csv_file:
        csv_file.close()
    cv2.destroyAllWindows()

    print(f"Convolutional bird detection video saved to: {output_path}")
    print(f"Camera moving frames: {camera_moving_frames}/{frame_count} ({camera_moving_frames/frame_count*100:.1f}%)")
    print(f"Water motion frames: {water_motion_frames}/{frame_count} ({water_motion_frames/frame_count*100:.1f}%)")
    print(f"Bird detection frames: {bird_detection_frames}/{frame_count} ({bird_detection_frames/frame_count*100:.1f}%)")
    if csv_path:
        print(f"Detection data saved to: {csv_path}")
    return True

def main():
    parser = argparse.ArgumentParser(description='Convolutional bird detection with color pattern analysis')
    parser.add_argument('input', help='Input video file path')
    parser.add_argument('output', help='Output video file path')
    parser.add_argument('--motion-sensitivity', type=float, default=1.0,
                       help='Camera motion sensitivity (default: 1.0)')
    parser.add_argument('--csv', help='Optional path to save detection data as CSV')

    args = parser.parse_args()

    # Check if input file exists
    if not os.path.exists(args.input):
        print(f"Error: Input file '{args.input}' does not exist")
        return

    # Create output directory if it doesn't exist
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Create CSV directory if specified
    if args.csv:
        csv_dir = os.path.dirname(args.csv) or '.'
        if not os.path.exists(csv_dir):
            os.makedirs(csv_dir)

    print(f"Input: {args.input}")
    print(f"Output: {args.output}")
    if args.csv:
        print(f"CSV Output: {args.csv}")

    success = detect_birds_convolution(
        args.input, args.output, args.motion_sensitivity, args.csv
    )

    if success:
        print("Convolutional bird detection completed successfully!")
    else:
        print("Convolutional bird detection failed!")

if __name__ == "__main__":
    main()
