import cv2
import numpy as np
import argparse
import os
import csv
from datetime import datetime

def detect_small_birds_optimized(input_path, output_path, threshold=20, blur_kernel=3, 
                                min_area=25, max_area=400, csv_path=None):
    """
    Optimized detection for small moving birds (around 10x10 pixels) using frame subtraction.
    
    Args:
        input_path (str): Path to input video file
        output_path (str): Path to output video file
        threshold (int): Threshold for motion detection (0-255) - lower for more sensitivity
        blur_kernel (int): Kernel size for Gaussian blur (odd number) - smaller for small objects
        min_area (int): Minimum contour area to consider as bird (pixels) - smaller for 10x10 birds
        max_area (int): Maximum contour area to consider as bird (pixels)
        csv_path (str): Optional path to save detection data
    """
    
    # Open the input video
    cap = cv2.VideoCapture(input_path)
    
    if not cap.isOpened():
        print(f"Error: Could not open video file {input_path}")
        return False
    
    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"Processing video: {width}x{height} @ {fps}fps, {total_frames} frames")
    print(f"Optimized for small birds: area {min_area}-{max_area} pixels, threshold {threshold}")
    
    # Define the codec and create VideoWriter object
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    # Prepare CSV writer if needed
    csv_file = None
    csv_writer = None
    if csv_path:
        csv_file = open(csv_path, 'w', newline='')
        csv_writer = csv.writer(csv_file)
        csv_writer.writerow(['frame_number', 'timestamp', 'bird_count', 'detections'])
    
    # Read first frame
    ret, prev_frame = cap.read()
    if not ret:
        print("Error: Could not read first frame")
        return False
    
    # Convert to grayscale for motion detection
    prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)
    prev_gray = cv2.GaussianBlur(prev_gray, (blur_kernel, blur_kernel), 0)
    
    frame_count = 0
    
    while True:
        ret, current_frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        timestamp = cap.get(cv2.CAP_PROP_POS_MSEC) / 1000.0
        
        # Convert current frame to grayscale
        current_gray = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY)
        current_gray = cv2.GaussianBlur(current_gray, (blur_kernel, blur_kernel), 0)
        
        # Compute absolute difference between current and previous frame
        frame_diff = cv2.absdiff(prev_gray, current_gray)
        
        # Apply threshold to get binary image (lower threshold for small objects)
        _, thresh = cv2.threshold(frame_diff, threshold, 255, cv2.THRESH_BINARY)
        
        # Apply morphological operations optimized for small objects
        # Use smaller kernel for small birds
        kernel_small = np.ones((2, 2), np.uint8)
        kernel_medium = np.ones((3, 3), np.uint8)
        
        # Remove noise but preserve small objects
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel_small)
        # Fill small gaps
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel_medium)
        
        # Find contours
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Filter contours based on area and shape for small birds
        bird_detections = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if min_area <= area <= max_area:
                # Get bounding rectangle
                x, y, w, h = cv2.boundingRect(contour)
                
                # Additional filtering for bird-like objects
                aspect_ratio = w / h if h > 0 else 0
                
                # More lenient aspect ratio for small objects (birds can appear in various orientations)
                if 0.2 <= aspect_ratio <= 5.0:
                    # Calculate compactness (circularity measure)
                    perimeter = cv2.arcLength(contour, True)
                    if perimeter > 0:
                        compactness = 4 * np.pi * area / (perimeter * perimeter)
                        
                        # Birds should be reasonably compact (not too elongated or fragmented)
                        if compactness > 0.1:  # More lenient for small noisy objects
                            bird_detections.append({
                                'bbox': (x, y, w, h),
                                'area': area,
                                'center': (x + w//2, y + h//2),
                                'compactness': compactness
                            })
        
        # Create output frame (start with original frame)
        output_frame = current_frame.copy()
        
        # Draw bounding boxes and labels for detected birds
        for i, detection in enumerate(bird_detections):
            x, y, w, h = detection['bbox']
            area = detection['area']
            center_x, center_y = detection['center']
            compactness = detection['compactness']
            
            # Color code by size: green for small (likely birds), yellow for medium, red for large
            if area <= 100:
                color = (0, 255, 0)  # Green for small objects
            elif area <= 200:
                color = (0, 255, 255)  # Yellow for medium objects
            else:
                color = (0, 0, 255)  # Red for large objects
            
            # Draw bounding box
            cv2.rectangle(output_frame, (x, y), (x + w, y + h), color, 2)
            
            # Draw center point
            cv2.circle(output_frame, (center_x, center_y), 2, (255, 0, 255), -1)
            
            # Add label with bird ID, area, and compactness
            label = f"B{i+1}:{area}px"
            cv2.putText(output_frame, label, (x, y-5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
        
        # Record detection data to CSV if enabled
        if csv_writer:
            detection_data = []
            for i, detection in enumerate(bird_detections):
                x, y, w, h = detection['bbox']
                area = detection['area']
                comp = detection['compactness']
                detection_data.append(f"B{i+1}:({x},{y},{w},{h},{area},{comp:.2f})")
            
            csv_writer.writerow([
                frame_count,
                f"{timestamp:.3f}",
                len(bird_detections),
                "|".join(detection_data) if detection_data else "None"
            ])
        
        # Add frame information overlay
        info_color = (255, 255, 255)
        cv2.putText(output_frame, f"Frame: {frame_count}/{total_frames}", 
                   (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, info_color, 2)
        cv2.putText(output_frame, f"Birds: {len(bird_detections)}", 
                   (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.6, info_color, 2)
        cv2.putText(output_frame, f"Time: {timestamp:.1f}s", 
                   (10, 75), cv2.FONT_HERSHEY_SIMPLEX, 0.6, info_color, 2)
        cv2.putText(output_frame, f"Threshold: {threshold}", 
                   (10, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.6, info_color, 2)
        
        # Write the frame
        out.write(output_frame)
        
        # Update previous frame
        prev_gray = current_gray.copy()
        
        # Progress indicator
        if frame_count % 100 == 0 or frame_count == total_frames:
            progress = (frame_count / total_frames) * 100
            print(f"Progress: {progress:.1f}% ({frame_count}/{total_frames}) | "
                  f"Birds detected: {len(bird_detections)}")
    
    # Release everything
    cap.release()
    out.release()
    if csv_file:
        csv_file.close()
    cv2.destroyAllWindows()
    
    print(f"Optimized bird detection video saved to: {output_path}")
    if csv_path:
        print(f"Detection data saved to: {csv_path}")
    return True

def main():
    parser = argparse.ArgumentParser(description='Optimized detection for small moving birds (10x10 pixels)')
    parser.add_argument('input', help='Input video file path')
    parser.add_argument('output', help='Output video file path')
    parser.add_argument('--threshold', type=int, default=20,
                       help='Motion detection threshold (0-255, default: 20 - lower for more sensitivity)')
    parser.add_argument('--blur', type=int, default=3,
                       help='Blur kernel size (odd number, default: 3 - smaller for small objects)')
    parser.add_argument('--min-area', type=int, default=25,
                       help='Minimum bird area in pixels (default: 25 - for ~10x10 birds)')
    parser.add_argument('--max-area', type=int, default=400,
                       help='Maximum bird area in pixels (default: 400)')
    parser.add_argument('--csv', help='Optional path to save detection data as CSV')
    
    args = parser.parse_args()
    
    # Check if input file exists
    if not os.path.exists(args.input):
        print(f"Error: Input file '{args.input}' does not exist")
        return
    
    # Create output directory if it doesn't exist
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Create CSV directory if specified
    if args.csv:
        csv_dir = os.path.dirname(args.csv) or '.'
        if not os.path.exists(csv_dir):
            os.makedirs(csv_dir)
    
    print(f"Input: {args.input}")
    print(f"Output: {args.output}")
    if args.csv:
        print(f"CSV Output: {args.csv}")
    
    success = detect_small_birds_optimized(
        args.input, args.output, args.threshold, args.blur, 
        args.min_area, args.max_area, args.csv
    )
    
    if success:
        print("Optimized bird detection completed successfully!")
    else:
        print("Optimized bird detection failed!")

if __name__ == "__main__":
    main()
