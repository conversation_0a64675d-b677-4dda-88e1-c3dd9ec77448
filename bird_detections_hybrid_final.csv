frame_number,timestamp,camera_moving,yolo_count,temporal_count,final_count,detection_source,detections,motion_info
1,0.040,False,0,0,0,MOVEMENT_DELAY_1/10,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:1,WM:False,FPS:False,CB:0,FSME:1,FSMS:0,MDF:1,MSDF:0"
2,0.080,False,0,0,0,MOVEMENT_DELAY_2/10,None,"Z:1.000,P:0.0,-0.2,R:-0.000,C:1.00,TC:0.00,TF:2,WM:False,FPS:False,CB:0,FSME:2,FSMS:0,MDF:2,MSDF:0"
3,0.120,False,0,0,0,MOVEMENT_DELAY_3/10,None,"Z:1.000,P:-0.0,-0.1,R:-0.000,C:1.00,TC:0.00,TF:3,WM:False,FPS:False,CB:0,FSME:3,FSMS:0,MDF:3,MSDF:0"
4,0.159,False,0,0,0,MOVEMENT_DELAY_4/10,None,"Z:1.000,P:0.1,0.2,R:0.000,C:1.00,TC:0.00,TF:4,WM:False,FPS:False,CB:0,FSME:4,FSMS:0,MDF:4,MSDF:0"
5,0.200,False,0,0,0,MOVEMENT_DELAY_5/10,None,"Z:1.000,P:0.1,0.2,R:-0.000,C:1.00,TC:0.00,TF:5,WM:False,FPS:False,CB:0,FSME:5,FSMS:0,MDF:5,MSDF:0"
6,0.239,False,0,0,0,MOVEMENT_DELAY_6/10,None,"Z:1.000,P:-0.0,-0.1,R:0.000,C:1.00,TC:0.00,TF:6,WM:False,FPS:False,CB:0,FSME:6,FSMS:0,MDF:6,MSDF:0"
7,0.279,False,0,0,0,MOVEMENT_DELAY_7/10,None,"Z:1.000,P:0.1,-0.1,R:-0.000,C:1.00,TC:0.00,TF:7,WM:False,FPS:False,CB:0,FSME:7,FSMS:0,MDF:7,MSDF:0"
8,0.319,False,0,0,0,MOVEMENT_DELAY_8/10,None,"Z:1.000,P:-0.1,-0.3,R:-0.000,C:1.00,TC:0.00,TF:8,WM:False,FPS:False,CB:0,FSME:8,FSMS:0,MDF:8,MSDF:0"
9,0.360,False,0,0,0,MOVEMENT_DELAY_9/10,None,"Z:1.000,P:0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:9,WM:False,FPS:False,CB:0,FSME:9,FSMS:0,MDF:9,MSDF:0"
10,0.399,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.1,R:-0.000,C:1.00,TC:0.00,TF:10,WM:False,FPS:False,CB:1,FSME:10,FSMS:0,MDF:9,MSDF:0"
11,0.440,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.2,R:-0.000,C:1.00,TC:0.00,TF:11,WM:False,FPS:False,CB:2,FSME:11,FSMS:0,MDF:9,MSDF:0"
12,0.479,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,0.0,R:0.000,C:1.00,TC:0.00,TF:12,WM:False,FPS:False,CB:3,FSME:12,FSMS:0,MDF:9,MSDF:0"
13,0.519,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.0,-0.0,R:0.000,C:1.00,TC:0.00,TF:13,WM:False,FPS:False,CB:4,FSME:13,FSMS:0,MDF:9,MSDF:0"
14,0.559,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:-0.1,-0.2,R:-0.000,C:0.99,TC:0.00,TF:14,WM:False,FPS:False,CB:4,FSME:14,FSMS:0,MDF:9,MSDF:0"
15,0.599,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:15,WM:False,FPS:False,CB:5,FSME:15,FSMS:0,MDF:9,MSDF:0"
16,0.639,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.2,R:-0.000,C:1.00,TC:0.00,TF:16,WM:False,FPS:False,CB:5,FSME:16,FSMS:0,MDF:9,MSDF:0"
17,0.679,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:17,WM:False,FPS:False,CB:5,FSME:17,FSMS:0,MDF:9,MSDF:0"
18,0.719,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,0.0,R:0.000,C:1.00,TC:0.00,TF:18,WM:False,FPS:False,CB:5,FSME:18,FSMS:0,MDF:9,MSDF:0"
19,0.759,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:19,WM:False,FPS:False,CB:5,FSME:19,FSMS:0,MDF:9,MSDF:0"
20,0.799,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.1,R:-0.000,C:1.00,TC:0.00,TF:20,WM:False,FPS:False,CB:5,FSME:20,FSMS:0,MDF:9,MSDF:0"
21,0.839,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.1,0.1,R:0.000,C:1.00,TC:0.00,TF:21,WM:False,FPS:False,CB:5,FSME:21,FSMS:0,MDF:9,MSDF:0"
22,0.879,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:22,WM:False,FPS:False,CB:5,FSME:22,FSMS:0,MDF:9,MSDF:0"
23,0.919,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,0.0,R:0.000,C:1.00,TC:0.00,TF:23,WM:False,FPS:False,CB:5,FSME:23,FSMS:0,MDF:9,MSDF:0"
24,0.959,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:24,WM:False,FPS:False,CB:5,FSME:24,FSMS:0,MDF:9,MSDF:0"
25,0.999,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:25,WM:False,FPS:False,CB:5,FSME:25,FSMS:0,MDF:9,MSDF:0"
26,1.039,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,-0.1,R:-0.000,C:1.00,TC:0.00,TF:26,WM:False,FPS:False,CB:5,FSME:26,FSMS:0,MDF:9,MSDF:0"
27,1.079,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:27,WM:False,FPS:False,CB:5,FSME:27,FSMS:0,MDF:9,MSDF:0"
28,1.119,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.1,R:-0.000,C:1.00,TC:0.00,TF:28,WM:False,FPS:False,CB:5,FSME:28,FSMS:0,MDF:9,MSDF:0"
29,1.159,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.2,R:-0.000,C:1.00,TC:0.00,TF:29,WM:False,FPS:False,CB:5,FSME:29,FSMS:0,MDF:9,MSDF:0"
30,1.199,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:30,WM:False,FPS:False,CB:5,FSME:30,FSMS:0,MDF:9,MSDF:0"
31,1.239,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,-0.1,R:-0.000,C:1.00,TC:0.00,TF:31,WM:False,FPS:False,CB:5,FSME:31,FSMS:0,MDF:9,MSDF:0"
32,1.279,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.0,-0.1,R:-0.000,C:1.00,TC:0.00,TF:32,WM:False,FPS:False,CB:5,FSME:32,FSMS:0,MDF:9,MSDF:0"
33,1.319,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.0,0.1,R:0.000,C:1.00,TC:0.00,TF:33,WM:False,FPS:False,CB:5,FSME:33,FSMS:0,MDF:9,MSDF:0"
34,1.359,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:34,WM:False,FPS:False,CB:5,FSME:34,FSMS:0,MDF:9,MSDF:0"
35,1.399,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.1,R:-0.000,C:1.00,TC:0.00,TF:35,WM:False,FPS:False,CB:5,FSME:35,FSMS:0,MDF:9,MSDF:0"
36,1.439,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:36,WM:False,FPS:False,CB:5,FSME:36,FSMS:0,MDF:9,MSDF:0"
37,1.478,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:37,WM:False,FPS:False,CB:5,FSME:37,FSMS:0,MDF:9,MSDF:0"
38,1.518,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:38,WM:False,FPS:False,CB:5,FSME:38,FSMS:0,MDF:9,MSDF:0"
39,1.558,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:39,WM:False,FPS:False,CB:5,FSME:39,FSMS:0,MDF:9,MSDF:0"
40,1.598,False,0,1,1,TEMPORAL,"T1:(347,226,10,10,30.5,0.650,4,2)","Z:1.000,P:0.1,0.1,R:-0.000,C:1.00,TC:0.00,TF:40,WM:False,FPS:False,CB:5,FSME:40,FSMS:0,MDF:9,MSDF:0"
41,1.638,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,0.0,R:0.000,C:1.00,TC:0.00,TF:41,WM:False,FPS:False,CB:5,FSME:41,FSMS:0,MDF:9,MSDF:0"
42,1.678,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.1,R:-0.000,C:0.99,TC:0.00,TF:42,WM:False,FPS:False,CB:5,FSME:42,FSMS:0,MDF:9,MSDF:0"
43,1.718,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,-0.1,R:-0.000,C:1.00,TC:0.00,TF:43,WM:False,FPS:False,CB:5,FSME:43,FSMS:0,MDF:9,MSDF:0"
44,1.758,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.0,0.1,R:0.000,C:1.00,TC:0.00,TF:44,WM:False,FPS:False,CB:5,FSME:44,FSMS:0,MDF:9,MSDF:0"
45,1.798,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:45,WM:False,FPS:False,CB:5,FSME:45,FSMS:0,MDF:9,MSDF:0"
46,1.838,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.2,R:0.000,C:1.00,TC:0.00,TF:46,WM:False,FPS:False,CB:5,FSME:46,FSMS:0,MDF:9,MSDF:0"
47,1.878,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:47,WM:False,FPS:False,CB:5,FSME:47,FSMS:0,MDF:9,MSDF:0"
48,1.918,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:48,WM:False,FPS:False,CB:5,FSME:48,FSMS:0,MDF:9,MSDF:0"
49,1.958,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.1,-0.1,R:0.000,C:1.00,TC:0.00,TF:49,WM:False,FPS:False,CB:5,FSME:49,FSMS:0,MDF:9,MSDF:0"
50,1.998,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:0.95,TC:0.00,TF:50,WM:False,FPS:False,CB:5,FSME:50,FSMS:0,MDF:9,MSDF:0"
51,2.038,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,0.0,R:0.000,C:1.00,TC:0.00,TF:51,WM:False,FPS:False,CB:5,FSME:51,FSMS:0,MDF:9,MSDF:0"
52,2.078,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.1,R:-0.000,C:1.00,TC:0.00,TF:52,WM:False,FPS:False,CB:5,FSME:52,FSMS:0,MDF:9,MSDF:0"
53,2.118,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.0,R:0.000,C:1.00,TC:0.00,TF:53,WM:False,FPS:False,CB:5,FSME:53,FSMS:0,MDF:9,MSDF:0"
54,2.158,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,-0.1,R:-0.000,C:1.00,TC:0.00,TF:54,WM:False,FPS:False,CB:5,FSME:54,FSMS:0,MDF:9,MSDF:0"
55,2.198,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:55,WM:False,FPS:False,CB:5,FSME:55,FSMS:0,MDF:9,MSDF:0"
56,2.238,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,0.1,R:0.000,C:1.00,TC:0.00,TF:56,WM:False,FPS:False,CB:5,FSME:56,FSMS:0,MDF:9,MSDF:0"
57,2.278,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.0,0.0,R:0.000,C:1.00,TC:0.00,TF:57,WM:False,FPS:False,CB:5,FSME:57,FSMS:0,MDF:9,MSDF:0"
58,2.318,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:58,WM:False,FPS:False,CB:5,FSME:58,FSMS:0,MDF:9,MSDF:0"
59,2.358,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:59,WM:False,FPS:False,CB:5,FSME:59,FSMS:0,MDF:9,MSDF:0"
60,2.398,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:60,WM:False,FPS:False,CB:5,FSME:60,FSMS:0,MDF:9,MSDF:0"
61,2.438,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:61,WM:False,FPS:False,CB:5,FSME:61,FSMS:0,MDF:9,MSDF:0"
62,2.478,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:62,WM:False,FPS:False,CB:5,FSME:62,FSMS:0,MDF:9,MSDF:0"
63,2.518,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:63,WM:False,FPS:False,CB:5,FSME:63,FSMS:0,MDF:9,MSDF:0"
64,2.558,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:64,WM:False,FPS:False,CB:5,FSME:64,FSMS:0,MDF:9,MSDF:0"
65,2.598,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.0,0.1,R:0.000,C:1.00,TC:0.00,TF:65,WM:False,FPS:False,CB:5,FSME:65,FSMS:0,MDF:9,MSDF:0"
66,2.638,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:66,WM:False,FPS:False,CB:5,FSME:66,FSMS:0,MDF:9,MSDF:0"
67,2.678,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:67,WM:False,FPS:False,CB:5,FSME:67,FSMS:0,MDF:9,MSDF:0"
68,2.718,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:-0.0,-0.1,R:-0.000,C:1.00,TC:0.00,TF:68,WM:False,FPS:False,CB:5,FSME:68,FSMS:0,MDF:9,MSDF:0"
69,2.758,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.0,0.0,R:0.000,C:1.00,TC:0.00,TF:69,WM:False,FPS:False,CB:5,FSME:69,FSMS:0,MDF:9,MSDF:0"
70,2.798,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:70,WM:False,FPS:False,CB:5,FSME:70,FSMS:0,MDF:9,MSDF:0"
71,2.837,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.2,R:-0.000,C:1.00,TC:0.00,TF:71,WM:False,FPS:False,CB:5,FSME:71,FSMS:0,MDF:9,MSDF:0"
72,2.877,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:72,WM:False,FPS:False,CB:5,FSME:72,FSMS:0,MDF:9,MSDF:0"
73,2.917,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:73,WM:False,FPS:False,CB:5,FSME:73,FSMS:0,MDF:9,MSDF:0"
74,2.958,False,0,1,1,TEMPORAL,"T1:(373,270,4,6,10.5,0.600,1,1)","Z:1.000,P:-0.0,-0.1,R:0.000,C:1.00,TC:0.00,TF:74,WM:False,FPS:False,CB:5,FSME:74,FSMS:0,MDF:9,MSDF:0"
75,2.997,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:75,WM:False,FPS:False,CB:5,FSME:75,FSMS:0,MDF:9,MSDF:0"
76,3.037,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:76,WM:False,FPS:False,CB:5,FSME:76,FSMS:0,MDF:9,MSDF:0"
77,3.077,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.0,0.1,R:0.000,C:1.00,TC:0.00,TF:77,WM:False,FPS:False,CB:5,FSME:77,FSMS:0,MDF:9,MSDF:0"
78,3.117,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:78,WM:False,FPS:False,CB:5,FSME:78,FSMS:0,MDF:9,MSDF:0"
79,3.157,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.0,R:0.000,C:1.00,TC:0.00,TF:79,WM:False,FPS:False,CB:5,FSME:79,FSMS:0,MDF:9,MSDF:0"
80,3.197,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:80,WM:False,FPS:False,CB:5,FSME:80,FSMS:0,MDF:9,MSDF:0"
81,3.237,False,0,1,1,TEMPORAL,"T1:(367,274,4,6,8.5,0.800,2,2)","Z:1.000,P:-0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:81,WM:False,FPS:False,CB:5,FSME:81,FSMS:0,MDF:9,MSDF:0"
82,3.277,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:82,WM:False,FPS:False,CB:5,FSME:82,FSMS:0,MDF:9,MSDF:0"
83,3.317,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.0,0.1,R:0.000,C:1.00,TC:0.00,TF:83,WM:False,FPS:False,CB:5,FSME:83,FSMS:0,MDF:9,MSDF:0"
84,3.357,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.1,R:-0.000,C:1.00,TC:0.00,TF:84,WM:False,FPS:False,CB:5,FSME:84,FSMS:0,MDF:9,MSDF:0"
85,3.397,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:85,WM:False,FPS:False,CB:5,FSME:85,FSMS:0,MDF:9,MSDF:0"
86,3.437,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.1,R:-0.000,C:1.00,TC:0.00,TF:86,WM:False,FPS:False,CB:5,FSME:86,FSMS:0,MDF:9,MSDF:0"
87,3.477,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:87,WM:False,FPS:False,CB:5,FSME:87,FSMS:0,MDF:9,MSDF:0"
88,3.517,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:88,WM:False,FPS:False,CB:5,FSME:88,FSMS:0,MDF:9,MSDF:0"
89,3.557,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,0.1,R:0.000,C:1.00,TC:0.00,TF:89,WM:False,FPS:False,CB:5,FSME:89,FSMS:0,MDF:9,MSDF:0"
90,3.597,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:90,WM:False,FPS:False,CB:5,FSME:90,FSMS:0,MDF:9,MSDF:0"
91,3.637,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:91,WM:False,FPS:False,CB:5,FSME:91,FSMS:0,MDF:9,MSDF:0"
92,3.677,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:92,WM:False,FPS:False,CB:5,FSME:92,FSMS:0,MDF:9,MSDF:0"
93,3.717,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:-0.0,0.1,R:0.000,C:1.00,TC:0.00,TF:93,WM:False,FPS:False,CB:5,FSME:93,FSMS:0,MDF:9,MSDF:0"
94,3.757,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:94,WM:False,FPS:False,CB:5,FSME:94,FSMS:0,MDF:9,MSDF:0"
95,3.797,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.0,-0.1,R:-0.000,C:1.00,TC:0.00,TF:95,WM:False,FPS:False,CB:5,FSME:95,FSMS:0,MDF:9,MSDF:0"
96,3.837,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:96,WM:False,FPS:False,CB:5,FSME:96,FSMS:0,MDF:9,MSDF:0"
97,3.877,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.1,R:-0.000,C:1.00,TC:0.00,TF:97,WM:False,FPS:False,CB:5,FSME:97,FSMS:0,MDF:9,MSDF:0"
98,3.917,False,0,1,1,TEMPORAL,"T1:(352,282,10,7,20.5,0.700,3,2)","Z:1.000,P:0.1,0.0,R:-0.000,C:0.99,TC:0.00,TF:98,WM:False,FPS:False,CB:5,FSME:98,FSMS:0,MDF:9,MSDF:0"
99,3.957,False,0,1,1,TEMPORAL,"T1:(351,282,11,6,17.5,0.675,4,2)","Z:1.000,P:0.0,0.0,R:0.000,C:1.00,TC:0.00,TF:99,WM:False,FPS:False,CB:5,FSME:99,FSMS:0,MDF:9,MSDF:0"
100,3.997,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.3,0.0,R:-0.000,C:0.97,TC:0.00,TF:100,WM:False,FPS:False,CB:5,FSME:100,FSMS:0,MDF:9,MSDF:0"
101,4.037,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:-0.0,-0.1,R:-0.000,C:1.00,TC:0.00,TF:101,WM:False,FPS:False,CB:5,FSME:101,FSMS:0,MDF:9,MSDF:0"
102,4.077,False,0,1,1,TEMPORAL,"T1:(350,280,4,7,10.0,0.775,4,3)","Z:1.000,P:-0.1,-0.1,R:-0.000,C:1.00,TC:0.00,TF:102,WM:False,FPS:False,CB:5,FSME:102,FSMS:0,MDF:9,MSDF:0"
103,4.117,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.1,R:-0.000,C:1.00,TC:0.00,TF:103,WM:False,FPS:False,CB:5,FSME:103,FSMS:0,MDF:9,MSDF:0"
104,4.156,False,0,1,1,TEMPORAL,"T1:(346,284,11,6,21.0,0.775,4,3)","Z:1.000,P:0.2,0.1,R:-0.000,C:1.00,TC:0.00,TF:104,WM:False,FPS:False,CB:5,FSME:104,FSMS:0,MDF:9,MSDF:0"
105,4.196,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,-0.0,R:0.000,C:1.00,TC:0.00,TF:105,WM:False,FPS:False,CB:5,FSME:105,FSMS:0,MDF:9,MSDF:0"
106,4.236,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,-0.0,R:-0.000,C:0.99,TC:0.00,TF:106,WM:False,FPS:False,CB:5,FSME:106,FSMS:0,MDF:9,MSDF:0"
107,4.277,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.1,-0.0,R:0.000,C:0.98,TC:0.00,TF:107,WM:False,FPS:False,CB:5,FSME:107,FSMS:0,MDF:9,MSDF:0"
108,4.316,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:108,WM:False,FPS:False,CB:5,FSME:108,FSMS:0,MDF:9,MSDF:0"
109,4.356,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:109,WM:False,FPS:False,CB:5,FSME:109,FSMS:0,MDF:9,MSDF:0"
110,4.396,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:110,WM:False,FPS:False,CB:5,FSME:110,FSMS:0,MDF:9,MSDF:0"
111,4.436,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.0,R:0.000,C:1.00,TC:0.00,TF:111,WM:False,FPS:False,CB:5,FSME:111,FSMS:0,MDF:9,MSDF:0"
112,4.476,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:112,WM:False,FPS:False,CB:5,FSME:112,FSMS:0,MDF:9,MSDF:0"
113,4.516,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.0,R:-0.000,C:0.99,TC:0.00,TF:113,WM:False,FPS:False,CB:5,FSME:113,FSMS:0,MDF:9,MSDF:0"
114,4.556,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:114,WM:False,FPS:False,CB:5,FSME:114,FSMS:0,MDF:9,MSDF:0"
115,4.596,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:-0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:115,WM:False,FPS:False,CB:5,FSME:115,FSMS:0,MDF:9,MSDF:0"
116,4.636,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.1,R:0.000,C:1.00,TC:0.00,TF:116,WM:False,FPS:False,CB:5,FSME:116,FSMS:0,MDF:9,MSDF:0"
117,4.676,False,0,1,1,TEMPORAL,"T1:(339,286,5,6,8.0,0.733,3,2)","Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:117,WM:False,FPS:False,CB:5,FSME:117,FSMS:0,MDF:9,MSDF:0"
118,4.716,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:118,WM:False,FPS:False,CB:5,FSME:118,FSMS:0,MDF:9,MSDF:0"
119,4.756,False,0,1,1,TEMPORAL,"T1:(338,288,9,8,21.5,0.725,4,2)","Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:119,WM:False,FPS:False,CB:5,FSME:119,FSMS:0,MDF:9,MSDF:0"
120,4.796,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:120,WM:False,FPS:False,CB:5,FSME:120,FSMS:0,MDF:9,MSDF:0"
121,4.836,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:121,WM:False,FPS:False,CB:5,FSME:121,FSMS:0,MDF:9,MSDF:0"
122,4.876,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:-0.1,0.0,R:0.000,C:1.00,TC:0.00,TF:122,WM:False,FPS:False,CB:5,FSME:122,FSMS:0,MDF:9,MSDF:0"
123,4.916,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.1,R:-0.000,C:1.00,TC:0.00,TF:123,WM:False,FPS:False,CB:5,FSME:123,FSMS:0,MDF:9,MSDF:0"
124,4.956,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,-0.1,R:-0.000,C:1.00,TC:0.00,TF:124,WM:False,FPS:False,CB:5,FSME:124,FSMS:0,MDF:9,MSDF:0"
125,4.996,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.0,R:0.000,C:1.00,TC:0.00,TF:125,WM:False,FPS:False,CB:5,FSME:125,FSMS:0,MDF:9,MSDF:0"
126,5.036,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:126,WM:False,FPS:False,CB:5,FSME:126,FSMS:0,MDF:9,MSDF:0"
127,5.076,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:127,WM:False,FPS:False,CB:5,FSME:127,FSMS:0,MDF:9,MSDF:0"
128,5.116,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.1,0.1,R:0.000,C:0.99,TC:0.00,TF:128,WM:False,FPS:False,CB:5,FSME:128,FSMS:0,MDF:9,MSDF:0"
129,5.156,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:129,WM:False,FPS:False,CB:5,FSME:129,FSMS:0,MDF:9,MSDF:0"
130,5.196,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:130,WM:False,FPS:False,CB:5,FSME:130,FSMS:0,MDF:9,MSDF:0"
131,5.236,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:131,WM:False,FPS:False,CB:5,FSME:131,FSMS:0,MDF:9,MSDF:0"
132,5.276,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:132,WM:False,FPS:False,CB:5,FSME:132,FSMS:0,MDF:9,MSDF:0"
133,5.316,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:133,WM:False,FPS:False,CB:5,FSME:133,FSMS:0,MDF:9,MSDF:0"
134,5.356,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:134,WM:False,FPS:False,CB:5,FSME:134,FSMS:0,MDF:9,MSDF:0"
135,5.396,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,0.1,R:0.000,C:1.00,TC:0.00,TF:135,WM:False,FPS:False,CB:5,FSME:135,FSMS:0,MDF:9,MSDF:0"
136,5.436,False,0,2,2,TEMPORAL,"T1:(385,32,6,6,13.5,0.800,4,4)|T2:(395,27,16,12,73.5,0.800,4,4)","Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:136,WM:False,FPS:False,CB:5,FSME:136,FSMS:0,MDF:9,MSDF:0"
137,5.476,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:137,WM:False,FPS:False,CB:5,FSME:137,FSMS:0,MDF:9,MSDF:0"
138,5.515,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.0,0.0,R:0.000,C:1.00,TC:0.00,TF:138,WM:False,FPS:False,CB:5,FSME:138,FSMS:0,MDF:9,MSDF:0"
139,5.556,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:0.99,TC:0.00,TF:139,WM:False,FPS:False,CB:5,FSME:139,FSMS:0,MDF:9,MSDF:0"
140,5.595,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:-0.0,-0.0,R:0.000,C:1.00,TC:0.00,TF:140,WM:False,FPS:False,CB:5,FSME:140,FSMS:0,MDF:9,MSDF:0"
141,5.635,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:141,WM:False,FPS:False,CB:5,FSME:141,FSMS:0,MDF:9,MSDF:0"
142,5.675,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:142,WM:False,FPS:False,CB:5,FSME:142,FSMS:0,MDF:9,MSDF:0"
143,5.715,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:143,WM:False,FPS:False,CB:5,FSME:143,FSMS:0,MDF:9,MSDF:0"
144,5.755,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:144,WM:False,FPS:False,CB:5,FSME:144,FSMS:0,MDF:9,MSDF:0"
145,5.795,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:145,WM:False,FPS:False,CB:5,FSME:145,FSMS:0,MDF:9,MSDF:0"
146,5.835,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:-0.1,-0.0,R:0.000,C:1.00,TC:0.00,TF:146,WM:False,FPS:False,CB:5,FSME:146,FSMS:0,MDF:9,MSDF:0"
147,5.875,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.1,R:0.000,C:1.00,TC:0.00,TF:147,WM:False,FPS:False,CB:5,FSME:147,FSMS:0,MDF:9,MSDF:0"
148,5.915,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.0,0.0,R:-0.000,C:0.99,TC:0.00,TF:148,WM:False,FPS:False,CB:5,FSME:148,FSMS:0,MDF:9,MSDF:0"
149,5.955,False,0,1,1,TEMPORAL,"T1:(324,299,9,6,18.0,0.767,3,2)","Z:1.000,P:0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:149,WM:False,FPS:False,CB:5,FSME:149,FSMS:0,MDF:9,MSDF:0"
150,5.995,False,0,1,1,TEMPORAL,"T1:(324,299,8,9,23.0,0.675,4,2)","Z:1.000,P:0.0,0.0,R:0.000,C:0.97,TC:0.00,TF:150,WM:False,FPS:False,CB:5,FSME:150,FSMS:0,MDF:9,MSDF:0"
151,6.035,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:151,WM:False,FPS:False,CB:5,FSME:151,FSMS:0,MDF:9,MSDF:0"
152,6.075,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,-0.1,R:-0.000,C:1.00,TC:0.00,TF:152,WM:False,FPS:False,CB:5,FSME:152,FSMS:0,MDF:9,MSDF:0"
153,6.115,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.1,0.1,R:0.000,C:1.00,TC:0.00,TF:153,WM:False,FPS:False,CB:5,FSME:153,FSMS:0,MDF:9,MSDF:0"
154,6.155,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.1,R:-0.000,C:1.00,TC:0.00,TF:154,WM:False,FPS:False,CB:5,FSME:154,FSMS:0,MDF:9,MSDF:0"
155,6.195,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.0,0.1,R:0.000,C:1.00,TC:0.00,TF:155,WM:False,FPS:False,CB:5,FSME:155,FSMS:0,MDF:9,MSDF:0"
156,6.235,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:156,WM:False,FPS:False,CB:5,FSME:156,FSMS:0,MDF:9,MSDF:0"
157,6.275,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,-0.1,R:-0.000,C:0.99,TC:0.00,TF:157,WM:False,FPS:False,CB:5,FSME:157,FSMS:0,MDF:9,MSDF:0"
158,6.315,False,0,1,1,TEMPORAL,"T1:(288,312,5,6,14.0,0.850,4,4)","Z:1.000,P:0.0,-0.1,R:-0.000,C:1.00,TC:0.00,TF:158,WM:False,FPS:False,CB:5,FSME:158,FSMS:0,MDF:9,MSDF:0"
159,6.355,False,0,1,1,TEMPORAL,"T1:(287,312,5,6,13.5,0.800,4,3)","Z:1.000,P:-0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:159,WM:False,FPS:False,CB:5,FSME:159,FSMS:0,MDF:9,MSDF:0"
160,6.395,False,0,1,1,TEMPORAL,"T1:(285,312,5,6,10.0,0.725,4,2)","Z:1.000,P:0.0,0.1,R:0.000,C:1.00,TC:0.00,TF:160,WM:False,FPS:False,CB:5,FSME:160,FSMS:0,MDF:9,MSDF:0"
161,6.435,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.1,R:-0.000,C:1.00,TC:0.00,TF:161,WM:False,FPS:False,CB:5,FSME:161,FSMS:0,MDF:9,MSDF:0"
162,6.475,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:162,WM:False,FPS:False,CB:5,FSME:162,FSMS:0,MDF:9,MSDF:0"
163,6.515,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,-0.1,R:-0.000,C:0.99,TC:0.00,TF:163,WM:False,FPS:False,CB:5,FSME:163,FSMS:0,MDF:9,MSDF:0"
164,6.555,False,0,1,1,TEMPORAL,"T1:(279,309,5,7,14.5,0.750,4,3)","Z:1.000,P:-0.1,-0.1,R:0.000,C:1.00,TC:0.00,TF:164,WM:False,FPS:False,CB:5,FSME:164,FSMS:0,MDF:9,MSDF:0"
165,6.595,False,0,1,1,TEMPORAL,"T1:(277,309,7,7,14.0,0.700,4,2)","Z:1.000,P:0.1,0.1,R:-0.000,C:0.99,TC:0.00,TF:165,WM:False,FPS:False,CB:5,FSME:165,FSMS:0,MDF:9,MSDF:0"
166,6.635,False,0,1,1,TEMPORAL,"T1:(276,311,3,6,8.0,0.675,4,2)","Z:1.000,P:0.1,0.2,R:-0.000,C:1.00,TC:0.00,TF:166,WM:False,FPS:False,CB:5,FSME:166,FSMS:0,MDF:9,MSDF:0"
167,6.675,False,0,1,1,TEMPORAL,"T1:(273,313,4,6,10.0,0.800,4,3)","Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:167,WM:False,FPS:False,CB:5,FSME:167,FSMS:0,MDF:9,MSDF:0"
168,6.715,False,0,1,1,TEMPORAL,"T1:(272,312,3,6,9.5,0.700,4,2)","Z:1.000,P:0.0,-0.1,R:0.000,C:1.00,TC:0.00,TF:168,WM:False,FPS:False,CB:5,FSME:168,FSMS:0,MDF:9,MSDF:0"
169,6.755,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:-0.0,-0.1,R:-0.000,C:1.00,TC:0.00,TF:169,WM:False,FPS:False,CB:5,FSME:169,FSMS:0,MDF:9,MSDF:0"
170,6.795,False,0,1,1,TEMPORAL,"T1:(269,308,4,6,11.5,0.800,4,3)","Z:1.000,P:-0.1,-0.1,R:-0.000,C:0.99,TC:0.00,TF:170,WM:False,FPS:False,CB:5,FSME:170,FSMS:0,MDF:9,MSDF:0"
171,6.834,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.0,R:-0.000,C:0.98,TC:0.00,TF:171,WM:False,FPS:False,CB:5,FSME:171,FSMS:0,MDF:9,MSDF:0"
172,6.874,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.1,R:-0.000,C:0.98,TC:0.00,TF:172,WM:False,FPS:False,CB:5,FSME:172,FSMS:0,MDF:9,MSDF:0"
173,6.914,False,0,1,1,TEMPORAL,"T1:(263,311,3,7,12.0,0.875,4,4)","Z:1.000,P:0.0,0.0,R:-0.000,C:0.99,TC:0.00,TF:173,WM:False,FPS:False,CB:5,FSME:173,FSMS:0,MDF:9,MSDF:0"
174,6.954,False,0,1,1,TEMPORAL,"T1:(261,310,3,6,8.0,0.775,4,3)","Z:1.000,P:0.0,-0.0,R:0.000,C:0.99,TC:0.00,TF:174,WM:False,FPS:False,CB:5,FSME:174,FSMS:0,MDF:9,MSDF:0"
175,6.994,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:175,WM:False,FPS:False,CB:5,FSME:175,FSMS:0,MDF:9,MSDF:0"
176,7.034,False,0,1,1,TEMPORAL,"T1:(257,308,5,5,11.0,0.800,3,2)","Z:1.000,P:-0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:176,WM:False,FPS:False,CB:5,FSME:176,FSMS:0,MDF:9,MSDF:0"
177,7.074,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.0,0.0,R:-0.000,C:0.99,TC:0.00,TF:177,WM:False,FPS:False,CB:5,FSME:177,FSMS:0,MDF:9,MSDF:0"
178,7.114,False,0,1,1,TEMPORAL,"T1:(295,311,5,5,8.5,0.600,1,1)","Z:1.000,P:-0.0,0.0,R:0.000,C:0.99,TC:0.00,TF:178,WM:False,FPS:False,CB:5,FSME:178,FSMS:0,MDF:9,MSDF:0"
179,7.154,False,0,1,1,TEMPORAL,"T1:(251,311,4,5,10.0,0.800,2,2)","Z:1.000,P:0.0,0.0,R:0.000,C:1.00,TC:0.00,TF:179,WM:False,FPS:False,CB:5,FSME:179,FSMS:0,MDF:9,MSDF:0"
180,7.194,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,0.0,R:0.000,C:0.99,TC:0.00,TF:180,WM:False,FPS:False,CB:5,FSME:180,FSMS:0,MDF:9,MSDF:0"
181,7.234,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:181,WM:False,FPS:False,CB:5,FSME:181,FSMS:0,MDF:9,MSDF:0"
182,7.274,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:182,WM:False,FPS:False,CB:5,FSME:182,FSMS:0,MDF:9,MSDF:0"
183,7.314,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:183,WM:False,FPS:False,CB:5,FSME:183,FSMS:0,MDF:9,MSDF:0"
184,7.354,False,0,2,2,TEMPORAL,"T1:(400,31,15,10,78.0,0.800,4,4)|T2:(401,21,14,8,68.0,0.650,4,2)","Z:1.000,P:-0.0,0.0,R:0.000,C:1.00,TC:0.00,TF:184,WM:False,FPS:False,CB:5,FSME:184,FSMS:0,MDF:9,MSDF:0"
185,7.394,False,0,2,2,TEMPORAL,"T1:(240,310,5,4,9.5,0.600,1,1)|T2:(400,31,15,10,62.5,0.750,4,3)","Z:1.000,P:0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:185,WM:False,FPS:False,CB:5,FSME:185,FSMS:0,MDF:9,MSDF:0"
186,7.434,False,0,1,1,TEMPORAL,"T1:(401,31,14,10,59.0,0.700,4,2)","Z:1.000,P:0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:186,WM:False,FPS:False,CB:5,FSME:186,FSMS:0,MDF:9,MSDF:0"
187,7.474,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:187,WM:False,FPS:False,CB:5,FSME:187,FSMS:0,MDF:9,MSDF:0"
188,7.514,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:188,WM:False,FPS:False,CB:5,FSME:188,FSMS:0,MDF:9,MSDF:0"
189,7.554,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:-0.0,0.0,R:0.000,C:1.00,TC:0.00,TF:189,WM:False,FPS:False,CB:5,FSME:189,FSMS:0,MDF:9,MSDF:0"
190,7.594,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:190,WM:False,FPS:False,CB:5,FSME:190,FSMS:0,MDF:9,MSDF:0"
191,7.634,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:191,WM:False,FPS:False,CB:5,FSME:191,FSMS:0,MDF:9,MSDF:0"
192,7.674,False,0,1,1,TEMPORAL,"T1:(226,309,5,4,9.5,0.800,2,2)","Z:1.000,P:0.1,-0.1,R:-0.000,C:1.00,TC:0.00,TF:192,WM:False,FPS:False,CB:5,FSME:192,FSMS:0,MDF:9,MSDF:0"
193,7.714,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.0,-0.0,R:0.000,C:1.00,TC:0.00,TF:193,WM:False,FPS:False,CB:5,FSME:193,FSMS:0,MDF:9,MSDF:0"
194,7.754,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,0.1,R:0.000,C:1.00,TC:0.00,TF:194,WM:False,FPS:False,CB:5,FSME:194,FSMS:0,MDF:9,MSDF:0"
195,7.794,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.0,0.0,R:0.000,C:1.00,TC:0.00,TF:195,WM:False,FPS:False,CB:5,FSME:195,FSMS:0,MDF:9,MSDF:0"
196,7.834,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:196,WM:False,FPS:False,CB:5,FSME:196,FSMS:0,MDF:9,MSDF:0"
197,7.874,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:197,WM:False,FPS:False,CB:5,FSME:197,FSMS:0,MDF:9,MSDF:0"
198,7.914,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:0.99,TC:0.00,TF:198,WM:False,FPS:False,CB:5,FSME:198,FSMS:0,MDF:9,MSDF:0"
199,7.954,False,0,1,1,TEMPORAL,"T1:(214,307,4,5,10.0,0.600,1,1)","Z:1.000,P:0.0,0.0,R:0.000,C:0.99,TC:0.00,TF:199,WM:False,FPS:False,CB:5,FSME:199,FSMS:0,MDF:9,MSDF:0"
200,7.994,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.3,0.2,R:-0.000,C:0.96,TC:0.00,TF:200,WM:False,FPS:False,CB:5,FSME:200,FSMS:0,MDF:9,MSDF:0"
201,8.034,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.1,-0.1,R:-0.000,C:1.00,TC:0.00,TF:201,WM:False,FPS:False,CB:5,FSME:201,FSMS:0,MDF:9,MSDF:0"
202,8.074,False,0,1,1,TEMPORAL,"T1:(266,312,4,5,8.5,0.825,4,4)","Z:1.000,P:-0.2,-0.1,R:0.000,C:1.00,TC:0.00,TF:202,WM:False,FPS:False,CB:5,FSME:202,FSMS:0,MDF:9,MSDF:0"
203,8.114,False,0,1,1,TEMPORAL,"T1:(265,311,5,7,14.5,0.750,4,3)","Z:1.000,P:0.1,-0.0,R:-0.000,C:0.99,TC:0.00,TF:203,WM:False,FPS:False,CB:5,FSME:203,FSMS:0,MDF:9,MSDF:0"
204,8.153,False,0,1,1,TEMPORAL,"T1:(264,311,5,6,12.5,0.725,4,2)","Z:1.000,P:0.0,-0.0,R:-0.000,C:0.99,TC:0.00,TF:204,WM:False,FPS:False,CB:5,FSME:204,FSMS:0,MDF:9,MSDF:0"
205,8.193,False,0,1,1,TEMPORAL,"T1:(203,305,5,5,11.0,0.600,1,1)","Z:1.000,P:0.1,0.0,R:-0.000,C:0.99,TC:0.00,TF:205,WM:False,FPS:False,CB:5,FSME:205,FSMS:0,MDF:9,MSDF:0"
206,8.233,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.2,R:-0.000,C:0.99,TC:0.00,TF:206,WM:False,FPS:False,CB:5,FSME:206,FSMS:0,MDF:9,MSDF:0"
207,8.273,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.1,-0.2,R:0.000,C:0.99,TC:0.00,TF:207,WM:False,FPS:False,CB:5,FSME:207,FSMS:0,MDF:9,MSDF:0"
208,8.313,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:208,WM:False,FPS:False,CB:5,FSME:208,FSMS:0,MDF:9,MSDF:0"
209,8.353,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:209,WM:False,FPS:False,CB:5,FSME:209,FSMS:0,MDF:9,MSDF:0"
210,8.393,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:210,WM:False,FPS:False,CB:5,FSME:210,FSMS:0,MDF:9,MSDF:0"
211,8.434,False,0,1,1,TEMPORAL,"T1:(193,303,6,4,9.0,0.767,3,2)","Z:1.000,P:0.0,0.0,R:0.000,C:0.99,TC:0.00,TF:211,WM:False,FPS:False,CB:5,FSME:211,FSMS:0,MDF:9,MSDF:0"
212,8.473,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:212,WM:False,FPS:False,CB:5,FSME:212,FSMS:0,MDF:9,MSDF:0"
213,8.513,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.0,0.0,R:0.000,C:1.00,TC:0.00,TF:213,WM:False,FPS:False,CB:5,FSME:213,FSMS:0,MDF:9,MSDF:0"
214,8.553,False,0,1,1,TEMPORAL,"T1:(188,300,6,6,16.0,0.733,3,2)","Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:214,WM:False,FPS:False,CB:5,FSME:214,FSMS:0,MDF:9,MSDF:0"
215,8.593,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,0.0,R:0.000,C:1.00,TC:0.00,TF:215,WM:False,FPS:False,CB:5,FSME:215,FSMS:0,MDF:9,MSDF:0"
216,8.633,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.0,R:-0.000,C:1.00,TC:0.00,TF:216,WM:False,FPS:False,CB:5,FSME:216,FSMS:0,MDF:9,MSDF:0"
217,8.673,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:217,WM:False,FPS:False,CB:5,FSME:217,FSMS:0,MDF:9,MSDF:0"
218,8.713,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:-0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:218,WM:False,FPS:False,CB:5,FSME:218,FSMS:0,MDF:9,MSDF:0"
219,8.753,False,0,1,1,TEMPORAL,"T1:(180,300,9,6,16.0,0.725,4,2)","Z:1.000,P:-0.1,0.0,R:0.000,C:1.00,TC:0.00,TF:219,WM:False,FPS:False,CB:5,FSME:219,FSMS:0,MDF:9,MSDF:0"
220,8.793,False,0,1,1,TEMPORAL,"T1:(178,300,5,6,13.5,0.800,4,3)","Z:1.000,P:0.0,0.1,R:-0.000,C:1.00,TC:0.00,TF:220,WM:False,FPS:False,CB:5,FSME:220,FSMS:0,MDF:9,MSDF:0"
221,8.833,False,0,1,1,TEMPORAL,"T1:(177,299,7,6,18.5,0.750,4,2)","Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:221,WM:False,FPS:False,CB:5,FSME:221,FSMS:0,MDF:9,MSDF:0"
222,8.873,False,0,1,1,TEMPORAL,"T1:(177,297,3,7,9.0,0.650,4,2)","Z:1.000,P:0.1,-0.1,R:-0.000,C:1.00,TC:0.00,TF:222,WM:False,FPS:False,CB:5,FSME:222,FSMS:0,MDF:9,MSDF:0"
223,8.913,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.0,-0.0,R:-0.000,C:1.00,TC:0.00,TF:223,WM:False,FPS:False,CB:5,FSME:223,FSMS:0,MDF:9,MSDF:0"
224,8.953,False,0,1,1,TEMPORAL,"T1:(173,299,5,5,11.0,0.775,4,3)","Z:1.000,P:-0.0,0.1,R:-0.000,C:1.00,TC:0.00,TF:224,WM:False,FPS:False,CB:5,FSME:224,FSMS:0,MDF:9,MSDF:0"
225,8.993,False,0,1,1,TEMPORAL,"T1:(171,298,5,5,12.0,0.725,4,2)","Z:1.000,P:0.0,0.0,R:-0.000,C:1.00,TC:0.00,TF:225,WM:False,FPS:False,CB:5,FSME:225,FSMS:0,MDF:9,MSDF:0"
226,9.033,False,0,1,1,TEMPORAL,"T1:(170,298,4,6,11.0,0.725,4,2)","Z:1.000,P:0.9,-0.2,R:-0.001,C:0.95,TC:0.00,TF:226,WM:False,FPS:False,CB:5,FSME:226,FSMS:0,MDF:9,MSDF:0"
227,9.073,False,0,13,13,TEMPORAL,"T1:(154,309,8,3,8.0,0.875,4,4)|T2:(186,295,8,8,17.5,0.800,4,4)|T3:(167,286,10,7,15.5,0.875,4,4)|T4:(592,217,6,3,9.0,0.600,1,1)|T5:(512,207,6,5,9.5,0.600,1,1)|T6:(560,200,5,5,9.5,0.600,1,1)|T7:(583,195,3,8,13.0,0.600,1,1)|T8:(639,140,7,5,11.0,0.600,1,1)|T9:(622,94,7,8,14.5,0.600,1,1)|T10:(442,52,9,4,12.0,0.600,1,1)|T11:(520,46,5,8,11.0,0.600,1,1)|T12:(446,40,4,5,8.5,0.600,1,1)|T13:(545,16,7,4,14.0,0.600,1,1)","Z:1.000,P:2.4,0.2,R:0.001,C:0.97,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:227,FSMS:0,MDF:9,MSDF:0"
228,9.113,False,0,18,18,TEMPORAL,"T1:(387,541,11,13,44.0,0.600,1,1)|T2:(700,518,4,11,13.5,0.600,1,1)|T3:(299,509,10,7,16.0,0.600,1,1)|T4:(329,480,4,6,12.5,0.600,1,1)|T5:(187,444,7,6,13.0,0.600,1,1)|T6:(341,437,9,5,11.0,0.600,1,1)|T7:(437,346,16,13,50.5,0.600,1,1)|T8:(167,286,9,7,14.5,0.825,4,3)|T9:(513,253,6,6,17.5,0.750,2,2)|T10:(533,231,7,5,13.0,0.750,2,2)|T11:(456,143,11,14,26.0,0.600,1,1)|T12:(570,124,26,16,36.0,0.600,1,1)|T13:(375,102,20,21,92.5,0.600,1,1)|T14:(654,55,4,6,10.5,0.750,2,2)|T15:(456,39,5,7,11.5,0.750,2,2)|T16:(659,27,24,28,85.5,0.750,2,2)|T17:(452,24,7,10,23.5,0.750,2,2)|T18:(419,20,5,10,11.5,0.600,1,1)","Z:0.999,P:0.3,0.2,R:0.000,C:0.99,TC:0.20,TF:1,WM:False,FPS:False,CB:5,FSME:228,FSMS:0,MDF:9,MSDF:0"
229,9.153,True,0,0,0,MOVEMENT_START_DELAY_1/5,None,"Z:1.000,P:0.6,-0.2,R:-0.000,C:1.00,TC:0.20,TF:0,WM:True,FPS:False,CB:5,FSME:0,FSMS:1,MDF:9,MSDF:1"
230,9.193,True,0,0,0,MOVEMENT_START_DELAY_2/5,None,"Z:1.000,P:0.7,0.0,R:-0.000,C:1.00,TC:0.40,TF:1,WM:True,FPS:False,CB:5,FSME:0,FSMS:2,MDF:9,MSDF:2"
231,9.233,True,0,0,0,MOVEMENT_START_DELAY_3/5,None,"Z:1.000,P:1.1,0.1,R:-0.000,C:1.00,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:3,MDF:9,MSDF:3"
232,9.273,True,0,0,0,MOVEMENT_START_DELAY_4/5,None,"Z:1.000,P:1.3,-0.2,R:-0.000,C:0.99,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:4,MDF:9,MSDF:4"
233,9.313,True,0,0,0,CAMERA_MOVING,None,"Z:1.000,P:1.6,-0.1,R:-0.000,C:0.99,TC:0.40,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:5,MDF:9,MSDF:4"
234,9.353,True,1,0,1,YOLO_MOVING,"Y1:(458,313,124,63,7812,0.339)","Z:1.000,P:1.3,-0.1,R:0.000,C:0.97,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:6,MDF:9,MSDF:4"
235,9.393,True,1,0,1,YOLO_MOVING,"Y1:(457,314,116,61,7076,0.413)","Z:1.000,P:2.3,-0.4,R:-0.001,C:0.95,TC:0.40,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:7,MDF:9,MSDF:4"
236,9.433,True,0,0,0,IMMEDIATE_MOTION,None,"Z:0.998,P:8.2,-0.6,R:-0.004,C:0.65,TC:0.60,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:8,MDF:9,MSDF:4"
237,9.473,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.001,P:12.6,-0.9,R:-0.002,C:0.91,TC:0.80,TF:3,WM:False,FPS:False,CB:5,FSME:0,FSMS:9,MDF:9,MSDF:4"
238,9.512,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.000,P:14.8,-0.3,R:-0.001,C:0.91,TC:0.80,TF:4,WM:False,FPS:False,CB:5,FSME:0,FSMS:10,MDF:9,MSDF:4"
239,9.552,True,1,0,1,YOLO_MOVING,"Y1:(507,312,121,65,7865,0.582)","Z:1.000,P:15.2,-0.1,R:-0.001,C:0.89,TC:1.00,TF:5,WM:False,FPS:False,CB:5,FSME:0,FSMS:11,MDF:9,MSDF:4"
240,9.593,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.000,P:15.4,-0.3,R:-0.001,C:0.94,TC:1.00,TF:6,WM:False,FPS:False,CB:5,FSME:0,FSMS:12,MDF:9,MSDF:4"
241,9.632,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.000,P:15.3,0.1,R:-0.001,C:0.90,TC:1.00,TF:7,WM:False,FPS:False,CB:5,FSME:0,FSMS:13,MDF:9,MSDF:4"
242,9.672,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.000,P:15.3,-0.1,R:-0.001,C:0.90,TC:1.00,TF:8,WM:False,FPS:False,CB:5,FSME:0,FSMS:14,MDF:9,MSDF:4"
243,9.712,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.000,P:15.3,-0.0,R:-0.000,C:0.92,TC:1.00,TF:9,WM:False,FPS:False,CB:5,FSME:0,FSMS:15,MDF:9,MSDF:4"
244,9.752,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.000,P:15.3,-0.1,R:-0.001,C:0.92,TC:1.00,TF:10,WM:False,FPS:False,CB:5,FSME:0,FSMS:16,MDF:9,MSDF:4"
245,9.792,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.000,P:15.4,-0.2,R:-0.001,C:0.89,TC:1.00,TF:11,WM:False,FPS:False,CB:5,FSME:0,FSMS:17,MDF:9,MSDF:4"
246,9.832,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.000,P:15.6,0.0,R:-0.001,C:0.90,TC:1.00,TF:12,WM:False,FPS:False,CB:5,FSME:0,FSMS:18,MDF:9,MSDF:4"
247,9.872,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.000,P:15.5,-0.0,R:-0.001,C:0.92,TC:1.00,TF:13,WM:False,FPS:False,CB:5,FSME:0,FSMS:19,MDF:9,MSDF:4"
248,9.912,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.000,P:15.4,0.2,R:-0.001,C:0.90,TC:1.00,TF:14,WM:False,FPS:False,CB:5,FSME:0,FSMS:20,MDF:9,MSDF:4"
249,9.952,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.000,P:15.1,-0.3,R:-0.001,C:0.89,TC:1.00,TF:15,WM:False,FPS:False,CB:5,FSME:0,FSMS:21,MDF:9,MSDF:4"
250,9.992,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.001,P:14.5,-0.5,R:-0.001,C:0.73,TC:1.00,TF:16,WM:False,FPS:False,CB:5,FSME:0,FSMS:22,MDF:9,MSDF:4"
251,10.032,True,0,0,0,IMMEDIATE_MOTION,None,"Z:0.999,P:15.3,0.2,R:-0.001,C:0.91,TC:1.00,TF:17,WM:False,FPS:False,CB:5,FSME:0,FSMS:23,MDF:9,MSDF:4"
252,10.072,True,0,0,0,CAMERA_MOVING,None,"Z:0.998,P:9.0,3.7,R:0.007,C:0.33,TC:0.80,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:24,MDF:9,MSDF:4"
253,10.112,True,0,0,0,CAMERA_MOVING,None,"Z:0.998,P:0.4,2.3,R:0.003,C:0.75,TC:0.80,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:25,MDF:9,MSDF:4"
254,10.152,True,0,0,0,CAMERA_MOVING,None,"Z:1.000,P:-0.1,-0.3,R:-0.000,C:0.98,TC:0.60,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:26,MDF:9,MSDF:4"
255,10.192,True,0,0,0,CAMERA_MOVING,None,"Z:1.000,P:0.0,-0.1,R:0.000,C:0.94,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:27,MDF:9,MSDF:4"
256,10.232,False,0,0,0,MOVEMENT_DELAY_1/10,None,"Z:1.000,P:0.0,0.1,R:-0.000,C:1.00,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:1,FSMS:0,MDF:10,MSDF:4"
257,10.272,False,0,0,0,MOVEMENT_DELAY_2/10,None,"Z:1.000,P:0.1,0.0,R:0.000,C:1.00,TC:0.20,TF:1,WM:False,FPS:False,CB:5,FSME:2,FSMS:0,MDF:11,MSDF:4"
258,10.312,False,0,0,0,MOVEMENT_DELAY_3/10,None,"Z:1.000,P:0.2,0.2,R:-0.000,C:1.00,TC:0.00,TF:2,WM:False,FPS:False,CB:5,FSME:3,FSMS:0,MDF:12,MSDF:4"
259,10.352,False,0,0,0,MOVEMENT_DELAY_4/10,None,"Z:1.000,P:0.1,0.1,R:0.000,C:0.98,TC:0.00,TF:3,WM:False,FPS:False,CB:5,FSME:4,FSMS:0,MDF:13,MSDF:4"
260,10.392,False,0,0,0,MOVEMENT_DELAY_5/10,None,"Z:1.000,P:0.3,0.1,R:-0.000,C:0.99,TC:0.00,TF:4,WM:False,FPS:False,CB:5,FSME:5,FSMS:0,MDF:14,MSDF:4"
261,10.432,False,0,0,0,MOVEMENT_DELAY_6/10,None,"Z:1.000,P:-0.0,-0.1,R:0.000,C:1.00,TC:0.00,TF:5,WM:False,FPS:False,CB:5,FSME:6,FSMS:0,MDF:15,MSDF:4"
262,10.472,False,0,0,0,MOVEMENT_DELAY_7/10,None,"Z:1.000,P:0.1,-0.1,R:-0.000,C:1.00,TC:0.00,TF:6,WM:False,FPS:False,CB:5,FSME:7,FSMS:0,MDF:16,MSDF:4"
263,10.512,False,0,0,0,MOVEMENT_DELAY_8/10,None,"Z:1.000,P:0.1,0.1,R:0.000,C:0.99,TC:0.00,TF:7,WM:False,FPS:False,CB:5,FSME:8,FSMS:0,MDF:17,MSDF:4"
264,10.552,False,0,0,0,MOVEMENT_DELAY_9/10,None,"Z:1.000,P:0.3,0.1,R:-0.000,C:1.00,TC:0.00,TF:8,WM:False,FPS:False,CB:5,FSME:9,FSMS:0,MDF:18,MSDF:4"
265,10.592,False,0,1,1,TEMPORAL,"T1:(481,307,6,5,11.0,0.750,2,2)","Z:0.999,P:0.3,0.4,R:0.000,C:1.00,TC:0.00,TF:9,WM:False,FPS:False,CB:5,FSME:10,FSMS:0,MDF:18,MSDF:4"
266,10.632,False,0,1,1,TEMPORAL,"T1:(479,304,4,7,13.5,0.733,3,2)","Z:1.000,P:0.2,0.1,R:-0.000,C:1.00,TC:0.00,TF:10,WM:False,FPS:False,CB:5,FSME:11,FSMS:0,MDF:18,MSDF:4"
267,10.672,False,0,1,1,TEMPORAL,"T1:(477,302,5,8,14.0,0.775,4,3)","Z:1.000,P:0.2,-0.3,R:-0.000,C:0.99,TC:0.00,TF:11,WM:False,FPS:False,CB:5,FSME:12,FSMS:0,MDF:18,MSDF:4"
268,10.712,False,0,1,1,TEMPORAL,"T1:(475,305,4,6,11.0,0.725,4,2)","Z:1.000,P:-0.0,0.1,R:0.000,C:1.00,TC:0.00,TF:12,WM:False,FPS:False,CB:5,FSME:13,FSMS:0,MDF:18,MSDF:4"
269,10.752,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.1,R:-0.000,C:0.99,TC:0.00,TF:13,WM:False,FPS:False,CB:5,FSME:14,FSMS:0,MDF:18,MSDF:4"
270,10.792,False,0,1,1,TEMPORAL,"T1:(471,308,4,8,18.5,0.850,4,4)","Z:1.000,P:0.3,0.2,R:0.000,C:0.99,TC:0.00,TF:14,WM:False,FPS:False,CB:5,FSME:15,FSMS:0,MDF:18,MSDF:4"
271,10.831,False,0,1,1,TEMPORAL,"T1:(469,308,4,7,14.5,0.825,4,3)","Z:1.000,P:0.2,0.3,R:-0.000,C:1.00,TC:0.00,TF:15,WM:False,FPS:False,CB:5,FSME:16,FSMS:0,MDF:18,MSDF:4"
272,10.871,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.0,R:0.000,C:0.99,TC:0.00,TF:16,WM:False,FPS:False,CB:5,FSME:17,FSMS:0,MDF:18,MSDF:4"
273,10.911,False,0,1,1,TEMPORAL,"T1:(465,305,5,6,13.0,0.825,4,3)","Z:1.000,P:-0.1,-0.0,R:0.000,C:0.98,TC:0.00,TF:17,WM:False,FPS:False,CB:5,FSME:18,FSMS:0,MDF:18,MSDF:4"
274,10.951,False,0,1,1,TEMPORAL,"T1:(464,304,4,7,13.5,0.825,4,3)","Z:1.000,P:0.1,-0.2,R:-0.000,C:0.97,TC:0.00,TF:18,WM:False,FPS:False,CB:5,FSME:19,FSMS:0,MDF:18,MSDF:4"
275,10.991,False,0,1,1,TEMPORAL,"T1:(461,305,5,7,13.0,0.750,4,2)","Z:0.999,P:0.3,0.1,R:-0.000,C:0.99,TC:0.00,TF:19,WM:False,FPS:False,CB:5,FSME:20,FSMS:0,MDF:18,MSDF:4"
276,11.031,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.2,R:0.000,C:1.00,TC:0.00,TF:20,WM:False,FPS:False,CB:5,FSME:21,FSMS:0,MDF:18,MSDF:4"
277,11.071,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.2,R:-0.000,C:0.99,TC:0.00,TF:21,WM:False,FPS:False,CB:5,FSME:22,FSMS:0,MDF:18,MSDF:4"
278,11.111,False,0,1,1,TEMPORAL,"T1:(455,309,4,8,17.5,0.900,4,4)","Z:1.000,P:0.2,0.0,R:-0.000,C:0.98,TC:0.00,TF:22,WM:False,FPS:False,CB:5,FSME:23,FSMS:0,MDF:18,MSDF:4"
279,11.151,False,0,1,1,TEMPORAL,"T1:(453,308,4,6,12.5,0.825,4,3)","Z:1.000,P:0.2,-0.0,R:0.000,C:0.99,TC:0.00,TF:23,WM:False,FPS:False,CB:5,FSME:24,FSMS:0,MDF:18,MSDF:4"
280,11.191,False,0,1,1,TEMPORAL,"T1:(452,308,6,5,11.0,0.725,4,2)","Z:1.000,P:0.0,0.0,R:0.000,C:1.00,TC:0.00,TF:24,WM:False,FPS:False,CB:5,FSME:25,FSMS:0,MDF:18,MSDF:4"
281,11.231,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,-0.0,R:-0.000,C:1.00,TC:0.00,TF:25,WM:False,FPS:False,CB:5,FSME:26,FSMS:0,MDF:18,MSDF:4"
282,11.271,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.2,R:0.000,C:1.00,TC:0.00,TF:26,WM:False,FPS:False,CB:5,FSME:27,FSMS:0,MDF:18,MSDF:4"
283,11.311,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:0.999,P:-0.0,0.1,R:-0.000,C:1.00,TC:0.00,TF:27,WM:False,FPS:False,CB:5,FSME:28,FSMS:0,MDF:18,MSDF:4"
284,11.351,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:28,WM:False,FPS:False,CB:5,FSME:29,FSMS:0,MDF:18,MSDF:4"
285,11.392,False,0,1,1,TEMPORAL,"T1:(440,310,6,6,18.0,0.800,2,2)","Z:1.000,P:0.2,0.0,R:-0.000,C:1.00,TC:0.00,TF:29,WM:False,FPS:False,CB:5,FSME:30,FSMS:0,MDF:18,MSDF:4"
286,11.431,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.1,R:0.000,C:0.99,TC:0.00,TF:30,WM:False,FPS:False,CB:5,FSME:31,FSMS:0,MDF:18,MSDF:4"
287,11.471,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.0,R:-0.000,C:0.99,TC:0.00,TF:31,WM:False,FPS:False,CB:5,FSME:32,FSMS:0,MDF:18,MSDF:4"
288,11.511,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.3,0.1,R:-0.000,C:1.00,TC:0.00,TF:32,WM:False,FPS:False,CB:5,FSME:33,FSMS:0,MDF:18,MSDF:4"
289,11.551,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.1,R:-0.000,C:1.00,TC:0.00,TF:33,WM:False,FPS:False,CB:5,FSME:34,FSMS:0,MDF:18,MSDF:4"
290,11.591,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.1,R:0.000,C:1.00,TC:0.00,TF:34,WM:False,FPS:False,CB:5,FSME:35,FSMS:0,MDF:18,MSDF:4"
291,11.631,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:35,WM:False,FPS:False,CB:5,FSME:36,FSMS:0,MDF:18,MSDF:4"
292,11.671,False,0,1,1,TEMPORAL,"T1:(426,309,7,5,18.0,0.833,3,3)","Z:1.000,P:0.2,-0.1,R:-0.000,C:1.00,TC:0.00,TF:36,WM:False,FPS:False,CB:5,FSME:37,FSMS:0,MDF:18,MSDF:4"
293,11.711,False,0,1,1,TEMPORAL,"T1:(425,308,5,5,12.5,0.733,3,2)","Z:1.000,P:0.3,0.1,R:-0.000,C:1.00,TC:0.00,TF:37,WM:False,FPS:False,CB:5,FSME:38,FSMS:0,MDF:18,MSDF:4"
294,11.751,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.3,0.1,R:-0.000,C:1.00,TC:0.00,TF:38,WM:False,FPS:False,CB:5,FSME:39,FSMS:0,MDF:18,MSDF:4"
295,11.791,False,0,1,1,TEMPORAL,"T1:(424,306,6,7,19.5,0.700,4,2)","Z:1.000,P:0.1,0.1,R:0.000,C:1.00,TC:0.00,TF:39,WM:False,FPS:False,CB:5,FSME:40,FSMS:0,MDF:18,MSDF:4"
296,11.831,False,0,1,1,TEMPORAL,"T1:(422,304,6,7,18.5,0.725,4,2)","Z:1.000,P:0.1,0.0,R:0.000,C:1.00,TC:0.00,TF:40,WM:False,FPS:False,CB:5,FSME:41,FSMS:0,MDF:18,MSDF:4"
297,11.871,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,-0.0,R:-0.000,C:1.00,TC:0.00,TF:41,WM:False,FPS:False,CB:5,FSME:42,FSMS:0,MDF:18,MSDF:4"
298,11.911,False,0,1,1,TEMPORAL,"T1:(417,308,6,3,9.0,0.750,4,3)","Z:1.000,P:0.2,0.1,R:-0.000,C:1.00,TC:0.00,TF:42,WM:False,FPS:False,CB:5,FSME:43,FSMS:0,MDF:18,MSDF:4"
299,11.951,False,0,1,1,TEMPORAL,"T1:(414,307,7,6,18.0,0.800,4,3)","Z:1.000,P:0.3,0.1,R:-0.000,C:1.00,TC:0.00,TF:43,WM:False,FPS:False,CB:5,FSME:44,FSMS:0,MDF:18,MSDF:4"
300,11.991,False,0,1,1,TEMPORAL,"T1:(413,307,6,5,14.0,0.725,4,2)","Z:1.000,P:0.2,0.2,R:0.000,C:0.76,TC:0.00,TF:44,WM:False,FPS:False,CB:5,FSME:45,FSMS:0,MDF:18,MSDF:4"
301,12.031,False,0,1,1,TEMPORAL,"T1:(412,306,6,4,11.0,0.725,4,2)","Z:1.000,P:0.2,0.1,R:0.000,C:1.00,TC:0.00,TF:45,WM:False,FPS:False,CB:5,FSME:46,FSMS:0,MDF:18,MSDF:4"
302,12.071,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.0,R:-0.000,C:0.99,TC:0.00,TF:46,WM:False,FPS:False,CB:5,FSME:47,FSMS:0,MDF:18,MSDF:4"
303,12.111,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.1,R:0.000,C:0.99,TC:0.00,TF:47,WM:False,FPS:False,CB:5,FSME:48,FSMS:0,MDF:18,MSDF:4"
304,12.151,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.1,R:-0.000,C:0.99,TC:0.00,TF:48,WM:False,FPS:False,CB:5,FSME:49,FSMS:0,MDF:18,MSDF:4"
305,12.190,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.1,R:-0.000,C:1.00,TC:0.00,TF:49,WM:False,FPS:False,CB:5,FSME:50,FSMS:0,MDF:18,MSDF:4"
306,12.230,False,0,1,1,TEMPORAL,"T1:(403,305,7,5,14.0,0.900,4,4)","Z:1.000,P:0.2,0.2,R:0.000,C:1.00,TC:0.00,TF:50,WM:False,FPS:False,CB:5,FSME:51,FSMS:0,MDF:18,MSDF:4"
307,12.270,False,0,1,1,TEMPORAL,"T1:(401,305,7,5,19.0,0.825,4,3)","Z:1.000,P:0.3,0.1,R:-0.000,C:0.99,TC:0.00,TF:51,WM:False,FPS:False,CB:5,FSME:52,FSMS:0,MDF:18,MSDF:4"
308,12.310,False,0,1,1,TEMPORAL,"T1:(401,304,5,5,11.5,0.725,4,2)","Z:1.000,P:0.2,0.1,R:0.000,C:0.99,TC:0.00,TF:52,WM:False,FPS:False,CB:5,FSME:53,FSMS:0,MDF:18,MSDF:4"
309,12.350,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.0,R:-0.000,C:1.00,TC:0.00,TF:53,WM:False,FPS:False,CB:5,FSME:54,FSMS:0,MDF:18,MSDF:4"
310,12.390,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.0,R:-0.000,C:0.99,TC:0.00,TF:54,WM:False,FPS:False,CB:5,FSME:55,FSMS:0,MDF:18,MSDF:4"
311,12.430,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.1,R:0.000,C:1.00,TC:0.00,TF:55,WM:False,FPS:False,CB:5,FSME:56,FSMS:0,MDF:18,MSDF:4"
312,12.470,False,0,2,2,TEMPORAL,"T1:(395,302,6,5,11.5,0.750,4,2)|T2:(412,22,3,6,8.5,0.800,4,4)","Z:1.000,P:0.2,0.0,R:-0.000,C:1.00,TC:0.00,TF:56,WM:False,FPS:False,CB:5,FSME:57,FSMS:0,MDF:18,MSDF:4"
313,12.510,False,0,1,1,TEMPORAL,"T1:(392,302,11,7,18.5,0.675,4,2)","Z:1.000,P:0.1,0.0,R:-0.000,C:0.99,TC:0.00,TF:57,WM:False,FPS:False,CB:5,FSME:58,FSMS:0,MDF:18,MSDF:4"
314,12.550,False,0,1,1,TEMPORAL,"T1:(391,303,6,6,11.5,0.725,4,2)","Z:1.000,P:0.1,0.0,R:-0.000,C:0.99,TC:0.00,TF:58,WM:False,FPS:False,CB:5,FSME:59,FSMS:0,MDF:18,MSDF:4"
315,12.590,False,0,1,1,TEMPORAL,"T1:(390,302,5,6,11.5,0.725,4,2)","Z:1.000,P:0.2,0.1,R:0.000,C:1.00,TC:0.00,TF:59,WM:False,FPS:False,CB:5,FSME:60,FSMS:0,MDF:18,MSDF:4"
316,12.630,False,0,1,1,TEMPORAL,"T1:(389,300,5,5,11.0,0.725,4,2)","Z:1.000,P:0.2,0.1,R:-0.000,C:1.00,TC:0.00,TF:60,WM:False,FPS:False,CB:5,FSME:61,FSMS:0,MDF:18,MSDF:4"
317,12.670,False,0,1,1,TEMPORAL,"T1:(388,299,5,6,14.5,0.750,4,2)","Z:1.000,P:0.3,0.1,R:-0.000,C:0.99,TC:0.00,TF:61,WM:False,FPS:False,CB:5,FSME:62,FSMS:0,MDF:18,MSDF:4"
318,12.710,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.1,R:0.000,C:1.00,TC:0.00,TF:62,WM:False,FPS:False,CB:5,FSME:63,FSMS:0,MDF:18,MSDF:4"
319,12.750,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.1,R:-0.000,C:1.00,TC:0.00,TF:63,WM:False,FPS:False,CB:5,FSME:64,FSMS:0,MDF:18,MSDF:4"
320,12.790,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.0,R:0.000,C:1.00,TC:0.00,TF:64,WM:False,FPS:False,CB:5,FSME:65,FSMS:0,MDF:18,MSDF:4"
321,12.830,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.0,R:-0.000,C:1.00,TC:0.00,TF:65,WM:False,FPS:False,CB:5,FSME:66,FSMS:0,MDF:18,MSDF:4"
322,12.870,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.0,R:0.000,C:1.00,TC:0.00,TF:66,WM:False,FPS:False,CB:5,FSME:67,FSMS:0,MDF:18,MSDF:4"
323,12.910,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.1,R:0.000,C:1.00,TC:0.00,TF:67,WM:False,FPS:False,CB:5,FSME:68,FSMS:0,MDF:18,MSDF:4"
324,12.950,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.2,R:0.000,C:1.00,TC:0.00,TF:68,WM:False,FPS:False,CB:5,FSME:69,FSMS:0,MDF:18,MSDF:4"
325,12.990,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.1,R:0.000,C:0.99,TC:0.00,TF:69,WM:False,FPS:False,CB:5,FSME:70,FSMS:0,MDF:18,MSDF:4"
326,13.030,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.1,R:0.000,C:0.99,TC:0.00,TF:70,WM:False,FPS:False,CB:5,FSME:71,FSMS:0,MDF:18,MSDF:4"
327,13.070,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.3,-0.0,R:-0.000,C:1.00,TC:0.00,TF:71,WM:False,FPS:False,CB:5,FSME:72,FSMS:0,MDF:18,MSDF:4"
328,13.110,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:72,WM:False,FPS:False,CB:5,FSME:73,FSMS:0,MDF:18,MSDF:4"
329,13.150,False,0,1,1,TEMPORAL,"T1:(369,298,17,7,48.0,0.750,4,2)","Z:1.000,P:0.2,0.1,R:-0.000,C:1.00,TC:0.00,TF:73,WM:False,FPS:False,CB:5,FSME:74,FSMS:0,MDF:18,MSDF:4"
330,13.190,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.3,0.1,R:-0.000,C:1.00,TC:0.00,TF:74,WM:False,FPS:False,CB:5,FSME:75,FSMS:0,MDF:18,MSDF:4"
331,13.230,False,0,1,1,TEMPORAL,"T1:(367,299,5,5,9.0,0.800,4,4)","Z:1.000,P:0.0,0.1,R:0.000,C:1.00,TC:0.00,TF:75,WM:False,FPS:False,CB:5,FSME:76,FSMS:0,MDF:18,MSDF:4"
332,13.270,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.1,R:-0.000,C:0.99,TC:0.00,TF:76,WM:False,FPS:False,CB:5,FSME:77,FSMS:0,MDF:18,MSDF:4"
333,13.310,False,0,1,1,TEMPORAL,"T1:(366,297,8,6,13.0,0.650,4,2)","Z:1.000,P:0.1,0.0,R:0.000,C:0.99,TC:0.00,TF:77,WM:False,FPS:False,CB:5,FSME:78,FSMS:0,MDF:18,MSDF:4"
334,13.350,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,-0.0,R:-0.000,C:0.99,TC:0.00,TF:78,WM:False,FPS:False,CB:5,FSME:79,FSMS:0,MDF:18,MSDF:4"
335,13.390,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.0,R:0.000,C:1.00,TC:0.00,TF:79,WM:False,FPS:False,CB:5,FSME:80,FSMS:0,MDF:18,MSDF:4"
336,13.430,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.1,R:0.000,C:1.00,TC:0.00,TF:80,WM:False,FPS:False,CB:5,FSME:81,FSMS:0,MDF:18,MSDF:4"
337,13.470,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.1,R:0.000,C:1.00,TC:0.00,TF:81,WM:False,FPS:False,CB:5,FSME:82,FSMS:0,MDF:18,MSDF:4"
338,13.510,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.2,R:0.000,C:1.00,TC:0.00,TF:82,WM:False,FPS:False,CB:5,FSME:83,FSMS:0,MDF:18,MSDF:4"
339,13.549,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,-0.1,R:-0.000,C:0.99,TC:0.00,TF:83,WM:False,FPS:False,CB:5,FSME:84,FSMS:0,MDF:18,MSDF:4"
340,13.589,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.1,R:0.000,C:1.00,TC:0.00,TF:84,WM:False,FPS:False,CB:5,FSME:85,FSMS:0,MDF:18,MSDF:4"
341,13.630,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.1,R:-0.000,C:1.00,TC:0.00,TF:85,WM:False,FPS:False,CB:5,FSME:86,FSMS:0,MDF:18,MSDF:4"
342,13.669,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.3,0.2,R:0.000,C:1.00,TC:0.00,TF:86,WM:False,FPS:False,CB:5,FSME:87,FSMS:0,MDF:18,MSDF:4"
343,13.709,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.2,R:0.000,C:1.00,TC:0.00,TF:87,WM:False,FPS:False,CB:5,FSME:88,FSMS:0,MDF:18,MSDF:4"
344,13.749,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.0,R:0.000,C:1.00,TC:0.00,TF:88,WM:False,FPS:False,CB:5,FSME:89,FSMS:0,MDF:18,MSDF:4"
345,13.789,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:89,WM:False,FPS:False,CB:5,FSME:90,FSMS:0,MDF:18,MSDF:4"
346,13.829,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:90,WM:False,FPS:False,CB:5,FSME:91,FSMS:0,MDF:18,MSDF:4"
347,13.869,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.0,R:-0.000,C:1.00,TC:0.00,TF:91,WM:False,FPS:False,CB:5,FSME:92,FSMS:0,MDF:18,MSDF:4"
348,13.909,False,0,1,1,TEMPORAL,"T1:(345,293,4,7,10.0,0.750,2,2)","Z:1.000,P:0.2,0.1,R:-0.000,C:0.99,TC:0.00,TF:92,WM:False,FPS:False,CB:5,FSME:93,FSMS:0,MDF:18,MSDF:4"
349,13.949,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.1,R:0.000,C:1.00,TC:0.00,TF:93,WM:False,FPS:False,CB:5,FSME:94,FSMS:0,MDF:18,MSDF:4"
350,13.989,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.4,0.1,R:-0.000,C:0.82,TC:0.00,TF:94,WM:False,FPS:False,CB:5,FSME:95,FSMS:0,MDF:18,MSDF:4"
351,14.029,False,0,1,1,TEMPORAL,"T1:(342,296,3,8,10.0,0.800,3,2)","Z:1.000,P:0.2,-0.0,R:-0.000,C:0.99,TC:0.00,TF:95,WM:False,FPS:False,CB:5,FSME:96,FSMS:0,MDF:18,MSDF:4"
352,14.069,False,0,1,1,TEMPORAL,"T1:(341,296,3,8,8.5,0.750,4,2)","Z:1.000,P:0.1,0.0,R:0.000,C:1.00,TC:0.00,TF:96,WM:False,FPS:False,CB:5,FSME:97,FSMS:0,MDF:18,MSDF:4"
353,14.109,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.0,R:-0.000,C:1.00,TC:0.00,TF:97,WM:False,FPS:False,CB:5,FSME:98,FSMS:0,MDF:18,MSDF:4"
354,14.149,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.3,0.1,R:-0.000,C:1.00,TC:0.00,TF:98,WM:False,FPS:False,CB:5,FSME:99,FSMS:0,MDF:18,MSDF:4"
355,14.189,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.1,R:0.000,C:1.00,TC:0.00,TF:99,WM:False,FPS:False,CB:5,FSME:100,FSMS:0,MDF:18,MSDF:4"
356,14.229,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.0,R:-0.000,C:1.00,TC:0.00,TF:100,WM:False,FPS:False,CB:5,FSME:101,FSMS:0,MDF:18,MSDF:4"
357,14.269,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,-0.0,R:-0.000,C:0.99,TC:0.00,TF:101,WM:False,FPS:False,CB:5,FSME:102,FSMS:0,MDF:18,MSDF:4"
358,14.309,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.0,R:-0.000,C:0.99,TC:0.00,TF:102,WM:False,FPS:False,CB:5,FSME:103,FSMS:0,MDF:18,MSDF:4"
359,14.349,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.1,R:0.000,C:0.99,TC:0.00,TF:103,WM:False,FPS:False,CB:5,FSME:104,FSMS:0,MDF:18,MSDF:4"
360,14.389,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.1,R:0.000,C:1.00,TC:0.00,TF:104,WM:False,FPS:False,CB:5,FSME:105,FSMS:0,MDF:18,MSDF:4"
361,14.429,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.3,0.0,R:-0.000,C:1.00,TC:0.00,TF:105,WM:False,FPS:False,CB:5,FSME:106,FSMS:0,MDF:18,MSDF:4"
362,14.469,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.0,R:-0.000,C:1.00,TC:0.00,TF:106,WM:False,FPS:False,CB:5,FSME:107,FSMS:0,MDF:18,MSDF:4"
363,14.509,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,-0.0,R:-0.000,C:1.00,TC:0.00,TF:107,WM:False,FPS:False,CB:5,FSME:108,FSMS:0,MDF:18,MSDF:4"
364,14.549,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:108,WM:False,FPS:False,CB:5,FSME:109,FSMS:0,MDF:18,MSDF:4"
365,14.589,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.1,R:-0.000,C:0.99,TC:0.00,TF:109,WM:False,FPS:False,CB:5,FSME:110,FSMS:0,MDF:18,MSDF:4"
366,14.629,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.1,R:-0.000,C:1.00,TC:0.00,TF:110,WM:False,FPS:False,CB:5,FSME:111,FSMS:0,MDF:18,MSDF:4"
367,14.669,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.1,R:-0.000,C:0.99,TC:0.00,TF:111,WM:False,FPS:False,CB:5,FSME:112,FSMS:0,MDF:18,MSDF:4"
368,14.709,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:112,WM:False,FPS:False,CB:5,FSME:113,FSMS:0,MDF:18,MSDF:4"
369,14.749,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.3,-0.0,R:-0.000,C:1.00,TC:0.00,TF:113,WM:False,FPS:False,CB:5,FSME:114,FSMS:0,MDF:18,MSDF:4"
370,14.789,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.1,R:0.000,C:1.00,TC:0.00,TF:114,WM:False,FPS:False,CB:5,FSME:115,FSMS:0,MDF:18,MSDF:4"
371,14.829,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.1,R:0.000,C:1.00,TC:0.00,TF:115,WM:False,FPS:False,CB:5,FSME:116,FSMS:0,MDF:18,MSDF:4"
372,14.868,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.0,R:-0.000,C:0.99,TC:0.00,TF:116,WM:False,FPS:False,CB:5,FSME:117,FSMS:0,MDF:18,MSDF:4"
373,14.908,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.0,R:-0.000,C:1.00,TC:0.00,TF:117,WM:False,FPS:False,CB:5,FSME:118,FSMS:0,MDF:18,MSDF:4"
374,14.949,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,-0.1,R:-0.000,C:1.00,TC:0.00,TF:118,WM:False,FPS:False,CB:5,FSME:119,FSMS:0,MDF:18,MSDF:4"
375,14.988,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.1,R:-0.000,C:1.00,TC:0.00,TF:119,WM:False,FPS:False,CB:5,FSME:120,FSMS:0,MDF:18,MSDF:4"
376,15.028,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.1,R:-0.000,C:1.00,TC:0.00,TF:120,WM:False,FPS:False,CB:5,FSME:121,FSMS:0,MDF:18,MSDF:4"
377,15.068,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.1,R:-0.000,C:1.00,TC:0.00,TF:121,WM:False,FPS:False,CB:5,FSME:122,FSMS:0,MDF:18,MSDF:4"
378,15.108,False,0,1,1,TEMPORAL,"T1:(320,300,7,5,9.5,0.600,1,1)","Z:1.000,P:0.2,0.0,R:-0.000,C:1.00,TC:0.00,TF:122,WM:False,FPS:False,CB:5,FSME:123,FSMS:0,MDF:18,MSDF:4"
379,15.148,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,-0.0,R:-0.000,C:1.00,TC:0.00,TF:123,WM:False,FPS:False,CB:5,FSME:124,FSMS:0,MDF:18,MSDF:4"
380,15.188,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,-0.0,R:-0.000,C:0.99,TC:0.00,TF:124,WM:False,FPS:False,CB:5,FSME:125,FSMS:0,MDF:18,MSDF:4"
381,15.228,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.0,R:-0.000,C:1.00,TC:0.00,TF:125,WM:False,FPS:False,CB:5,FSME:126,FSMS:0,MDF:18,MSDF:4"
382,15.269,False,0,1,1,TEMPORAL,"T1:(409,21,6,4,9.5,0.700,2,2)","Z:1.000,P:0.2,0.0,R:-0.000,C:0.99,TC:0.00,TF:126,WM:False,FPS:False,CB:5,FSME:127,FSMS:0,MDF:18,MSDF:4"
383,15.308,False,0,1,1,TEMPORAL,"T1:(385,20,26,22,243.5,0.700,2,2)","Z:1.000,P:0.2,0.2,R:-0.000,C:0.99,TC:0.00,TF:127,WM:False,FPS:False,CB:5,FSME:128,FSMS:0,MDF:18,MSDF:4"
384,15.348,False,0,1,1,TEMPORAL,"T1:(401,21,6,4,9.0,0.800,2,2)","Z:1.000,P:0.2,0.1,R:-0.000,C:1.00,TC:0.00,TF:128,WM:False,FPS:False,CB:5,FSME:129,FSMS:0,MDF:18,MSDF:4"
385,15.389,False,0,1,1,TEMPORAL,"T1:(401,21,6,4,9.0,0.800,3,2)","Z:1.000,P:0.2,0.0,R:-0.000,C:1.00,TC:0.00,TF:129,WM:False,FPS:False,CB:5,FSME:130,FSMS:0,MDF:18,MSDF:4"
386,15.428,False,0,1,1,TEMPORAL,"T1:(385,21,14,12,45.5,0.800,4,4)","Z:1.000,P:0.3,-0.0,R:-0.000,C:1.00,TC:0.00,TF:130,WM:False,FPS:False,CB:5,FSME:131,FSMS:0,MDF:18,MSDF:4"
387,15.468,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.0,R:-0.000,C:0.99,TC:0.00,TF:131,WM:False,FPS:False,CB:5,FSME:132,FSMS:0,MDF:18,MSDF:4"
388,15.508,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.3,0.1,R:-0.000,C:1.00,TC:0.00,TF:132,WM:False,FPS:False,CB:5,FSME:133,FSMS:0,MDF:18,MSDF:4"
389,15.548,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.0,R:-0.000,C:1.00,TC:0.00,TF:133,WM:False,FPS:False,CB:5,FSME:134,FSMS:0,MDF:18,MSDF:4"
390,15.588,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.1,R:-0.000,C:1.00,TC:0.00,TF:134,WM:False,FPS:False,CB:5,FSME:135,FSMS:0,MDF:18,MSDF:4"
391,15.628,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.1,R:-0.000,C:0.99,TC:0.00,TF:135,WM:False,FPS:False,CB:5,FSME:136,FSMS:0,MDF:18,MSDF:4"
392,15.668,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:136,WM:False,FPS:False,CB:5,FSME:137,FSMS:0,MDF:18,MSDF:4"
393,15.708,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.1,R:-0.000,C:1.00,TC:0.00,TF:137,WM:False,FPS:False,CB:5,FSME:138,FSMS:0,MDF:18,MSDF:4"
394,15.748,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.3,0.0,R:-0.000,C:1.00,TC:0.00,TF:138,WM:False,FPS:False,CB:5,FSME:139,FSMS:0,MDF:18,MSDF:4"
395,15.788,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.1,R:-0.000,C:1.00,TC:0.00,TF:139,WM:False,FPS:False,CB:5,FSME:140,FSMS:0,MDF:18,MSDF:4"
396,15.828,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.0,R:-0.000,C:1.00,TC:0.00,TF:140,WM:False,FPS:False,CB:5,FSME:141,FSMS:0,MDF:18,MSDF:4"
397,15.868,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,-0.1,R:-0.000,C:0.99,TC:0.00,TF:141,WM:False,FPS:False,CB:5,FSME:142,FSMS:0,MDF:18,MSDF:4"
398,15.908,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.1,R:-0.000,C:1.00,TC:0.00,TF:142,WM:False,FPS:False,CB:5,FSME:143,FSMS:0,MDF:18,MSDF:4"
399,15.948,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.1,0.0,R:0.000,C:0.99,TC:0.00,TF:143,WM:False,FPS:False,CB:5,FSME:144,FSMS:0,MDF:18,MSDF:4"
400,15.988,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.3,-0.1,R:-0.000,C:0.86,TC:0.00,TF:144,WM:False,FPS:False,CB:5,FSME:145,FSMS:0,MDF:18,MSDF:4"
401,16.028,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.1,R:0.000,C:1.00,TC:0.00,TF:145,WM:False,FPS:False,CB:5,FSME:146,FSMS:0,MDF:18,MSDF:4"
402,16.068,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,0.1,R:-0.000,C:1.00,TC:0.00,TF:146,WM:False,FPS:False,CB:5,FSME:147,FSMS:0,MDF:18,MSDF:4"
403,16.108,False,0,0,0,NO_MOVEMENT,None,"Z:1.000,P:0.2,-0.0,R:-0.000,C:1.00,TC:0.00,TF:147,WM:False,FPS:False,CB:5,FSME:148,FSMS:0,MDF:18,MSDF:4"
404,16.148,False,0,3,3,TEMPORAL,"T1:(334,315,6,5,13.5,0.600,1,1)|T2:(418,27,16,6,28.5,0.700,2,2)|T3:(405,12,6,6,13.0,0.800,4,4)","Z:1.001,P:1.0,-1.8,R:-0.000,C:0.77,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:149,FSMS:0,MDF:18,MSDF:4"
405,16.187,True,0,0,0,MOVEMENT_START_DELAY_1/5,None,"Z:0.999,P:3.4,-0.7,R:-0.001,C:0.96,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:1,MDF:18,MSDF:5"
406,16.227,True,0,0,0,MOVEMENT_START_DELAY_2/5,None,"Z:1.000,P:6.0,-1.2,R:-0.002,C:0.91,TC:0.40,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:2,MDF:18,MSDF:6"
407,16.267,True,0,0,0,MOVEMENT_START_DELAY_3/5,None,"Z:0.999,P:7.1,-0.4,R:0.000,C:0.95,TC:0.60,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:3,MDF:18,MSDF:7"
408,16.307,True,0,0,0,MOVEMENT_START_DELAY_4/5,None,"Z:1.001,P:4.1,-0.6,R:0.002,C:0.88,TC:0.80,TF:3,WM:False,FPS:False,CB:5,FSME:0,FSMS:4,MDF:18,MSDF:8"
409,16.347,True,0,0,0,CAMERA_MOVING,None,"Z:1.001,P:0.1,-0.8,R:0.001,C:0.95,TC:0.80,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:5,MDF:18,MSDF:8"
410,16.387,True,0,0,0,CAMERA_MOVING,None,"Z:1.000,P:0.4,-1.4,R:-0.000,C:0.98,TC:0.60,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:6,MDF:18,MSDF:8"
411,16.427,True,0,0,0,CAMERA_MOVING,None,"Z:1.001,P:0.9,-2.1,R:-0.001,C:0.95,TC:0.60,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:7,MDF:18,MSDF:8"
412,16.467,True,0,0,0,CAMERA_MOVING,None,"Z:1.002,P:0.6,-2.9,R:0.000,C:0.88,TC:0.60,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:8,MDF:18,MSDF:8"
413,16.507,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.004,P:-0.9,-4.9,R:0.001,C:0.73,TC:0.60,TF:3,WM:False,FPS:False,CB:5,FSME:0,FSMS:9,MDF:18,MSDF:8"
414,16.547,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.005,P:-0.9,-7.9,R:-0.000,C:0.88,TC:0.80,TF:4,WM:False,FPS:False,CB:5,FSME:0,FSMS:10,MDF:18,MSDF:8"
415,16.587,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.006,P:-1.6,-7.4,R:-0.000,C:0.93,TC:1.00,TF:5,WM:False,FPS:False,CB:5,FSME:0,FSMS:11,MDF:18,MSDF:8"
416,16.627,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.008,P:-2.4,-5.0,R:0.000,C:0.87,TC:1.00,TF:6,WM:False,FPS:False,CB:5,FSME:0,FSMS:12,MDF:18,MSDF:8"
417,16.667,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.012,P:-3.8,-3.8,R:-0.000,C:0.80,TC:1.00,TF:7,WM:False,FPS:False,CB:5,FSME:0,FSMS:13,MDF:18,MSDF:8"
418,16.707,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.014,P:-4.5,-3.7,R:0.000,C:0.83,TC:1.00,TF:8,WM:False,FPS:False,CB:5,FSME:0,FSMS:14,MDF:18,MSDF:8"
419,16.747,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.016,P:-5.5,-4.4,R:-0.000,C:0.92,TC:1.00,TF:9,WM:False,FPS:False,CB:5,FSME:0,FSMS:15,MDF:18,MSDF:8"
420,16.787,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.018,P:-6.2,-5.4,R:-0.000,C:0.91,TC:1.00,TF:10,WM:False,FPS:False,CB:5,FSME:0,FSMS:16,MDF:18,MSDF:8"
421,16.827,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.018,P:-6.1,-4.7,R:-0.000,C:0.90,TC:1.00,TF:11,WM:False,FPS:False,CB:5,FSME:0,FSMS:17,MDF:18,MSDF:8"
422,16.867,True,0,0,0,WATER_MOTION,None,"Z:1.019,P:-6.2,-5.7,R:-0.000,C:0.90,TC:1.00,TF:12,WM:True,FPS:False,CB:5,FSME:0,FSMS:18,MDF:18,MSDF:8"
423,16.907,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.019,P:-6.6,-5.0,R:0.000,C:0.91,TC:1.00,TF:13,WM:False,FPS:False,CB:5,FSME:0,FSMS:19,MDF:18,MSDF:8"
424,16.947,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.020,P:-6.8,-5.9,R:-0.000,C:0.90,TC:1.00,TF:14,WM:False,FPS:False,CB:5,FSME:0,FSMS:20,MDF:18,MSDF:8"
425,16.987,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.020,P:-6.6,-5.6,R:-0.000,C:0.89,TC:1.00,TF:15,WM:False,FPS:False,CB:5,FSME:0,FSMS:21,MDF:18,MSDF:8"
426,17.027,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.020,P:-6.7,-5.9,R:-0.001,C:0.86,TC:1.00,TF:16,WM:False,FPS:False,CB:5,FSME:0,FSMS:22,MDF:18,MSDF:8"
427,17.067,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.020,P:-6.8,-6.3,R:-0.000,C:0.87,TC:1.00,TF:17,WM:False,FPS:False,CB:5,FSME:0,FSMS:23,MDF:18,MSDF:8"
428,17.107,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.021,P:-6.9,-5.7,R:-0.000,C:0.86,TC:1.00,TF:18,WM:False,FPS:False,CB:5,FSME:0,FSMS:24,MDF:18,MSDF:8"
429,17.147,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.021,P:-7.1,-6.0,R:-0.000,C:0.81,TC:1.00,TF:19,WM:False,FPS:False,CB:5,FSME:0,FSMS:25,MDF:18,MSDF:8"
430,17.187,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.023,P:-7.6,-6.6,R:-0.001,C:0.87,TC:1.00,TF:20,WM:False,FPS:False,CB:5,FSME:0,FSMS:26,MDF:18,MSDF:8"
431,17.227,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.022,P:-7.6,-6.5,R:0.000,C:0.85,TC:1.00,TF:21,WM:False,FPS:False,CB:5,FSME:0,FSMS:27,MDF:18,MSDF:8"
432,17.267,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.022,P:-7.3,-6.7,R:-0.000,C:0.86,TC:1.00,TF:22,WM:False,FPS:False,CB:5,FSME:0,FSMS:28,MDF:18,MSDF:8"
433,17.307,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.023,P:-8.0,-6.8,R:-0.000,C:0.83,TC:1.00,TF:23,WM:False,FPS:False,CB:5,FSME:0,FSMS:29,MDF:18,MSDF:8"
434,17.347,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.023,P:-7.8,-6.8,R:-0.000,C:0.86,TC:1.00,TF:24,WM:False,FPS:False,CB:5,FSME:0,FSMS:30,MDF:18,MSDF:8"
435,17.387,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.024,P:-8.0,-6.8,R:0.000,C:0.88,TC:1.00,TF:25,WM:False,FPS:False,CB:5,FSME:0,FSMS:31,MDF:18,MSDF:8"
436,17.427,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.023,P:-8.0,-6.6,R:-0.001,C:0.83,TC:1.00,TF:26,WM:False,FPS:False,CB:5,FSME:0,FSMS:32,MDF:18,MSDF:8"
437,17.467,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.024,P:-8.3,-7.7,R:-0.000,C:0.78,TC:1.00,TF:27,WM:False,FPS:False,CB:5,FSME:0,FSMS:33,MDF:18,MSDF:8"
438,17.506,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.024,P:-8.4,-7.2,R:-0.000,C:0.79,TC:1.00,TF:28,WM:False,FPS:False,CB:5,FSME:0,FSMS:34,MDF:18,MSDF:8"
439,17.546,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.026,P:-8.9,-7.9,R:-0.000,C:0.79,TC:1.00,TF:29,WM:False,FPS:False,CB:5,FSME:0,FSMS:35,MDF:18,MSDF:8"
440,17.587,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.024,P:-8.1,-7.1,R:-0.000,C:0.77,TC:1.00,TF:30,WM:False,FPS:False,CB:5,FSME:0,FSMS:36,MDF:18,MSDF:8"
441,17.626,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.026,P:-8.9,-7.7,R:-0.000,C:0.88,TC:1.00,TF:31,WM:False,FPS:False,CB:5,FSME:0,FSMS:37,MDF:18,MSDF:8"
442,17.666,True,1,0,1,YOLO_MOVING,"Y1:(394,511,13,23,299,0.320)","Z:1.026,P:-8.9,-8.0,R:-0.000,C:0.80,TC:1.00,TF:32,WM:False,FPS:False,CB:5,FSME:0,FSMS:38,MDF:18,MSDF:8"
443,17.707,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.025,P:-8.7,-7.6,R:-0.001,C:0.62,TC:1.00,TF:33,WM:False,FPS:False,CB:5,FSME:0,FSMS:39,MDF:18,MSDF:8"
444,17.746,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.027,P:-9.2,-8.7,R:-0.001,C:0.84,TC:1.00,TF:34,WM:False,FPS:False,CB:5,FSME:0,FSMS:40,MDF:18,MSDF:8"
445,17.786,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.027,P:-8.9,-8.2,R:-0.000,C:0.79,TC:1.00,TF:35,WM:False,FPS:False,CB:5,FSME:0,FSMS:41,MDF:18,MSDF:8"
446,17.826,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.027,P:-9.3,-8.1,R:-0.000,C:0.80,TC:1.00,TF:36,WM:False,FPS:False,CB:5,FSME:0,FSMS:42,MDF:18,MSDF:8"
447,17.866,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.028,P:-9.5,-7.7,R:0.000,C:0.70,TC:1.00,TF:37,WM:False,FPS:False,CB:5,FSME:0,FSMS:43,MDF:18,MSDF:8"
448,17.906,True,0,0,0,WATER_MOTION,None,"Z:1.028,P:-9.6,-7.9,R:0.000,C:0.80,TC:1.00,TF:38,WM:True,FPS:False,CB:5,FSME:0,FSMS:44,MDF:18,MSDF:8"
449,17.946,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.029,P:-10.1,-9.1,R:-0.000,C:0.83,TC:1.00,TF:39,WM:False,FPS:False,CB:5,FSME:0,FSMS:45,MDF:18,MSDF:8"
450,17.986,True,1,0,1,YOLO_MOVING,"Y1:(154,432,14,14,196,0.585)","Z:1.029,P:-10.6,-8.7,R:0.001,C:0.57,TC:0.80,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:46,MDF:18,MSDF:8"
451,18.026,True,1,0,1,YOLO_MOVING,"Y1:(148,435,15,14,210,0.506)","Z:1.029,P:-10.0,-9.1,R:-0.000,C:0.92,TC:0.80,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:47,MDF:18,MSDF:8"
452,18.066,True,1,0,1,YOLO_MOVING,"Y1:(142,440,15,14,210,0.572)","Z:1.030,P:-9.9,-8.8,R:-0.000,C:0.91,TC:0.80,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:48,MDF:18,MSDF:8"
453,18.106,True,1,0,1,YOLO_MOVING,"Y1:(135,444,17,16,272,0.526)","Z:1.031,P:-10.5,-8.8,R:-0.000,C:0.88,TC:0.80,TF:3,WM:False,FPS:False,CB:5,FSME:0,FSMS:49,MDF:18,MSDF:8"
454,18.146,True,1,0,1,YOLO_MOVING,"Y1:(129,450,16,15,240,0.514)","Z:1.032,P:-10.8,-9.7,R:-0.000,C:0.62,TC:0.80,TF:4,WM:False,FPS:False,CB:5,FSME:0,FSMS:50,MDF:18,MSDF:8"
455,18.186,True,1,0,1,YOLO_MOVING,"Y1:(122,454,17,15,255,0.492)","Z:1.031,P:-10.8,-9.3,R:-0.000,C:0.83,TC:1.00,TF:5,WM:False,FPS:False,CB:5,FSME:0,FSMS:51,MDF:18,MSDF:8"
456,18.226,True,1,0,1,YOLO_MOVING,"Y1:(114,458,18,17,306,0.448)","Z:1.032,P:-11.1,-9.8,R:0.000,C:0.87,TC:1.00,TF:6,WM:False,FPS:False,CB:5,FSME:0,FSMS:52,MDF:18,MSDF:8"
457,18.266,True,1,0,1,YOLO_MOVING,"Y1:(107,463,18,18,324,0.350)","Z:1.031,P:-10.7,-9.7,R:-0.000,C:0.90,TC:1.00,TF:7,WM:False,FPS:False,CB:5,FSME:0,FSMS:53,MDF:18,MSDF:8"
458,18.306,True,1,0,1,YOLO_MOVING,"Y1:(99,467,19,19,361,0.448)","Z:1.031,P:-10.8,-9.3,R:-0.000,C:0.81,TC:1.00,TF:8,WM:False,FPS:False,CB:5,FSME:0,FSMS:54,MDF:18,MSDF:8"
459,18.346,True,1,0,1,YOLO_MOVING,"Y1:(92,473,19,19,361,0.487)","Z:1.033,P:-10.7,-9.9,R:-0.001,C:0.84,TC:1.00,TF:9,WM:False,FPS:False,CB:5,FSME:0,FSMS:55,MDF:18,MSDF:8"
460,18.386,True,1,0,1,YOLO_MOVING,"Y1:(84,480,19,18,342,0.357)","Z:1.034,P:-11.8,-10.3,R:0.000,C:0.75,TC:1.00,TF:10,WM:False,FPS:False,CB:5,FSME:0,FSMS:56,MDF:18,MSDF:8"
461,18.426,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.035,P:-12.5,-11.1,R:-0.001,C:0.66,TC:1.00,TF:11,WM:False,FPS:False,CB:5,FSME:0,FSMS:57,MDF:18,MSDF:8"
462,18.466,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.034,P:-12.0,-10.9,R:-0.000,C:0.73,TC:1.00,TF:12,WM:False,FPS:False,CB:5,FSME:0,FSMS:58,MDF:18,MSDF:8"
463,18.506,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.034,P:-12.0,-9.8,R:-0.000,C:0.74,TC:1.00,TF:13,WM:False,FPS:False,CB:5,FSME:0,FSMS:59,MDF:18,MSDF:8"
464,18.546,True,0,0,0,WATER_MOTION,None,"Z:1.033,P:-10.7,-9.6,R:-0.000,C:0.76,TC:1.00,TF:14,WM:True,FPS:False,CB:5,FSME:0,FSMS:60,MDF:18,MSDF:8"
465,18.586,True,1,0,1,YOLO_MOVING,"Y1:(376,281,36,31,1116,0.451)","Z:1.034,P:-10.9,-10.4,R:-0.001,C:0.66,TC:1.00,TF:15,WM:False,FPS:False,CB:5,FSME:0,FSMS:61,MDF:18,MSDF:8"
466,18.626,True,1,0,1,YOLO_MOVING,"Y1:(375,245,42,55,2310,0.442)","Z:1.034,P:-11.7,-9.6,R:0.001,C:0.58,TC:0.80,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:62,MDF:18,MSDF:8"
467,18.666,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.034,P:-11.9,-10.5,R:-0.000,C:0.74,TC:0.80,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:63,MDF:18,MSDF:8"
468,18.706,True,1,0,1,YOLO_MOVING,"Y1:(2,535,27,23,621,0.529)","Z:1.032,P:-11.7,-10.2,R:0.000,C:0.75,TC:0.80,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:64,MDF:18,MSDF:8"
469,18.746,True,1,0,1,YOLO_MOVING,"Y1:(0,541,20,23,460,0.452)","Z:1.028,P:-9.6,-8.9,R:-0.000,C:0.81,TC:0.80,TF:3,WM:False,FPS:False,CB:5,FSME:0,FSMS:65,MDF:18,MSDF:8"
470,18.786,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.025,P:-8.6,-7.0,R:0.000,C:0.77,TC:0.80,TF:4,WM:False,FPS:False,CB:5,FSME:0,FSMS:66,MDF:18,MSDF:8"
471,18.826,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.019,P:-5.6,-5.8,R:-0.001,C:0.65,TC:1.00,TF:5,WM:False,FPS:False,CB:5,FSME:0,FSMS:67,MDF:18,MSDF:8"
472,18.865,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.014,P:-5.1,-3.6,R:0.001,C:0.63,TC:1.00,TF:6,WM:False,FPS:False,CB:5,FSME:0,FSMS:68,MDF:18,MSDF:8"
473,18.905,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.010,P:-3.6,-2.7,R:0.000,C:0.74,TC:1.00,TF:7,WM:False,FPS:False,CB:5,FSME:0,FSMS:69,MDF:18,MSDF:8"
474,18.945,True,0,0,0,IMMEDIATE_MOTION,None,"Z:1.006,P:-2.1,-2.1,R:0.000,C:0.76,TC:1.00,TF:8,WM:False,FPS:False,CB:5,FSME:0,FSMS:70,MDF:18,MSDF:8"
475,18.985,True,0,0,0,CAMERA_MOVING,None,"Z:1.003,P:-1.2,-1.3,R:-0.000,C:0.90,TC:1.00,TF:9,WM:False,FPS:False,CB:5,FSME:0,FSMS:71,MDF:18,MSDF:8"
476,19.025,True,0,0,0,CAMERA_MOVING,None,"Z:1.002,P:-0.5,-0.7,R:0.000,C:0.97,TC:0.80,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:72,MDF:18,MSDF:8"
477,19.065,True,1,0,1,YOLO_MOVING,"Y1:(358,246,34,33,1122,0.339)","Z:1.001,P:-0.3,-0.1,R:-0.000,C:0.94,TC:0.60,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:73,MDF:18,MSDF:8"
478,19.105,True,2,0,2,YOLO_MOVING,"Y1:(396,263,29,29,841,0.516)|Y2:(365,236,25,26,650,0.505)","Z:1.001,P:-0.2,-0.2,R:-0.000,C:0.98,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:74,MDF:18,MSDF:8"
479,19.146,False,3,0,3,YOLO_STABLE,"Y1:(366,216,17,31,527,0.535)|Y2:(397,263,28,29,812,0.482)|Y3:(402,261,24,28,672,0.353)","Z:1.000,P:-0.2,-0.0,R:0.000,C:0.97,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:1,FSMS:0,MDF:18,MSDF:8"
480,19.185,False,2,0,2,YOLO_STABLE,"Y1:(354,226,28,33,924,0.771)|Y2:(396,261,30,30,900,0.605)","Z:1.001,P:-0.1,-0.6,R:-0.000,C:0.98,TC:0.00,TF:1,WM:False,FPS:False,CB:5,FSME:2,FSMS:0,MDF:18,MSDF:8"
481,19.225,False,0,0,0,MOVEMENT_DELAY_3/10,None,"Z:1.000,P:-0.2,-0.3,R:0.000,C:0.98,TC:0.00,TF:2,WM:False,FPS:False,CB:5,FSME:3,FSMS:0,MDF:19,MSDF:8"
482,19.265,False,3,0,3,YOLO_STABLE,"Y1:(403,260,21,23,483,0.409)|Y2:(397,262,25,22,550,0.347)|Y3:(338,238,33,26,858,0.304)","Z:1.001,P:-0.2,0.1,R:-0.000,C:0.97,TC:0.00,TF:3,WM:False,FPS:False,CB:5,FSME:4,FSMS:0,MDF:19,MSDF:8"
483,19.305,False,1,0,1,YOLO_STABLE,"Y1:(403,258,21,25,525,0.345)","Z:1.001,P:-0.0,-0.1,R:-0.000,C:0.96,TC:0.00,TF:4,WM:False,FPS:False,CB:5,FSME:5,FSMS:0,MDF:19,MSDF:8"
484,19.345,False,3,0,3,YOLO_STABLE,"Y1:(329,214,31,41,1271,0.765)|Y2:(397,257,27,31,837,0.556)|Y3:(485,284,13,21,273,0.305)","Z:1.001,P:-0.1,0.1,R:0.000,C:0.97,TC:0.00,TF:5,WM:False,FPS:False,CB:5,FSME:6,FSMS:0,MDF:19,MSDF:8"
485,19.385,False,0,0,0,MOVEMENT_DELAY_7/10,None,"Z:1.001,P:-0.3,-0.5,R:-0.000,C:0.97,TC:0.00,TF:6,WM:False,FPS:False,CB:5,FSME:7,FSMS:0,MDF:20,MSDF:8"
486,19.425,False,2,0,2,YOLO_STABLE,"Y1:(317,231,30,16,480,0.607)|Y2:(399,255,23,26,598,0.385)","Z:1.001,P:-0.2,-0.7,R:-0.000,C:0.97,TC:0.00,TF:7,WM:False,FPS:False,CB:5,FSME:8,FSMS:0,MDF:20,MSDF:8"
487,19.465,False,1,0,1,YOLO_STABLE,"Y1:(401,255,21,27,567,0.338)","Z:1.001,P:-0.3,-0.2,R:-0.000,C:1.00,TC:0.00,TF:8,WM:False,FPS:False,CB:5,FSME:9,FSMS:0,MDF:20,MSDF:8"
488,19.506,False,1,0,1,YOLO_STABLE,"Y1:(400,254,20,25,500,0.326)","Z:1.001,P:-0.1,-0.2,R:-0.000,C:0.42,TC:0.00,TF:9,WM:False,FPS:False,CB:5,FSME:10,FSMS:0,MDF:20,MSDF:8"
489,19.545,False,3,0,3,YOLO_STABLE,"Y1:(305,205,32,40,1280,0.687)|Y2:(400,254,21,32,672,0.624)|Y3:(484,284,12,21,252,0.302)","Z:1.001,P:-0.1,-0.1,R:-0.000,C:1.00,TC:0.00,TF:10,WM:False,FPS:False,CB:5,FSME:11,FSMS:0,MDF:20,MSDF:8"
490,19.585,False,1,0,1,YOLO_STABLE,"Y1:(400,252,21,30,630,0.558)","Z:1.001,P:-0.1,-0.2,R:-0.000,C:1.00,TC:0.00,TF:11,WM:False,FPS:False,CB:5,FSME:12,FSMS:0,MDF:20,MSDF:8"
491,19.625,False,2,0,2,YOLO_STABLE,"Y1:(285,224,31,21,651,0.690)|Y2:(401,253,21,27,567,0.492)","Z:1.000,P:-0.2,-0.1,R:-0.000,C:0.97,TC:0.00,TF:12,WM:False,FPS:False,CB:5,FSME:13,FSMS:0,MDF:20,MSDF:8"
492,19.665,False,1,0,1,YOLO_STABLE,"Y1:(281,219,29,25,725,0.606)","Z:1.001,P:-0.3,-0.4,R:-0.000,C:0.98,TC:0.00,TF:13,WM:False,FPS:False,CB:5,FSME:14,FSMS:0,MDF:20,MSDF:8"
493,19.705,False,1,0,1,YOLO_STABLE,"Y1:(284,208,22,30,660,0.609)","Z:1.001,P:-0.1,-0.4,R:-0.000,C:0.98,TC:0.00,TF:14,WM:False,FPS:False,CB:5,FSME:15,FSMS:0,MDF:20,MSDF:8"
494,19.745,False,1,0,1,YOLO_STABLE,"Y1:(275,202,26,35,910,0.770)","Z:1.001,P:-0.1,-0.4,R:-0.000,C:0.97,TC:0.00,TF:15,WM:False,FPS:False,CB:5,FSME:16,FSMS:0,MDF:20,MSDF:8"
495,19.785,False,0,14,14,TEMPORAL,"T1:(660,551,4,5,8.5,0.600,1,1)|T2:(652,538,10,4,14.0,0.600,1,1)|T3:(546,516,5,5,9.5,0.600,1,1)|T4:(549,434,7,4,8.0,0.600,1,1)|T5:(492,234,7,7,13.5,0.600,1,1)|T6:(647,231,5,4,8.0,0.600,1,1)|T7:(665,227,6,9,13.5,0.600,1,1)|T8:(362,201,10,3,10.5,0.600,1,1)|T9:(293,189,9,6,19.0,0.600,1,1)|T10:(575,165,7,7,20.5,0.600,1,1)|T11:(459,143,6,5,10.0,0.600,1,1)|T12:(613,108,6,4,8.5,0.600,1,1)|T13:(562,59,16,6,48.5,0.600,1,1)|T14:(454,50,6,3,8.5,0.600,1,1)","Z:1.001,P:-0.1,-0.2,R:0.000,C:0.97,TC:0.00,TF:16,WM:False,FPS:False,CB:5,FSME:17,FSMS:0,MDF:20,MSDF:8"
496,19.825,False,1,0,1,YOLO_STABLE,"Y1:(252,214,32,22,704,0.624)","Z:1.001,P:-0.2,-0.1,R:-0.000,C:0.99,TC:0.00,TF:17,WM:False,FPS:False,CB:5,FSME:18,FSMS:0,MDF:20,MSDF:8"
497,19.865,False,1,0,1,YOLO_STABLE,"Y1:(251,209,28,25,700,0.676)","Z:1.001,P:-0.0,-0.2,R:-0.000,C:0.97,TC:0.00,TF:18,WM:False,FPS:False,CB:5,FSME:19,FSMS:0,MDF:20,MSDF:8"
498,19.905,False,2,0,2,YOLO_STABLE,"Y1:(255,195,20,35,700,0.764)|Y2:(401,254,16,23,368,0.534)","Z:1.001,P:-0.2,-0.4,R:-0.000,C:0.99,TC:0.00,TF:19,WM:False,FPS:False,CB:5,FSME:20,FSMS:0,MDF:20,MSDF:8"
499,19.945,False,1,0,1,YOLO_STABLE,"Y1:(242,192,27,33,891,0.790)","Z:1.001,P:-0.3,-0.4,R:-0.000,C:0.98,TC:0.00,TF:20,WM:False,FPS:False,CB:5,FSME:21,FSMS:0,MDF:20,MSDF:8"
500,19.985,False,2,0,2,YOLO_STABLE,"Y1:(400,254,15,21,315,0.418)|Y2:(369,454,13,13,169,0.390)","Z:1.001,P:-0.3,-0.2,R:0.000,C:0.86,TC:0.00,TF:21,WM:False,FPS:False,CB:5,FSME:22,FSMS:0,MDF:20,MSDF:8"
501,20.025,False,3,0,3,YOLO_STABLE,"Y1:(400,254,15,20,300,0.597)|Y2:(369,453,13,13,169,0.398)|Y3:(218,204,33,24,792,0.356)","Z:1.001,P:-0.1,-0.2,R:-0.000,C:0.99,TC:0.00,TF:22,WM:False,FPS:False,CB:5,FSME:23,FSMS:0,MDF:20,MSDF:8"
502,20.065,False,3,0,3,YOLO_STABLE,"Y1:(215,200,25,26,650,0.827)|Y2:(401,254,15,22,330,0.684)|Y3:(369,454,13,13,169,0.413)","Z:1.001,P:-0.2,-0.3,R:-0.000,C:0.97,TC:0.00,TF:23,WM:False,FPS:False,CB:5,FSME:24,FSMS:0,MDF:20,MSDF:8"
503,20.105,False,3,0,3,YOLO_STABLE,"Y1:(401,254,15,21,315,0.738)|Y2:(218,188,22,27,594,0.633)|Y3:(369,454,13,13,169,0.437)","Z:1.001,P:-0.3,-0.3,R:-0.000,C:0.96,TC:0.00,TF:24,WM:False,FPS:False,CB:5,FSME:25,FSMS:0,MDF:20,MSDF:8"
504,20.145,False,3,0,3,YOLO_STABLE,"Y1:(203,180,32,33,1056,0.785)|Y2:(400,254,16,24,384,0.744)|Y3:(370,454,13,13,169,0.449)","Z:1.001,P:-0.1,-0.3,R:-0.000,C:0.97,TC:0.00,TF:25,WM:False,FPS:False,CB:5,FSME:26,FSMS:0,MDF:20,MSDF:8"
505,20.184,False,3,0,3,YOLO_STABLE,"Y1:(400,253,16,24,384,0.708)|Y2:(369,454,13,13,169,0.391)|Y3:(198,192,27,21,567,0.358)","Z:1.001,P:-0.4,-0.4,R:-0.000,C:0.97,TC:0.00,TF:26,WM:False,FPS:False,CB:5,FSME:27,FSMS:0,MDF:20,MSDF:8"
506,20.225,False,3,0,3,YOLO_STABLE,"Y1:(184,193,28,18,504,0.647)|Y2:(399,249,18,27,486,0.583)|Y3:(369,454,13,13,169,0.424)","Z:1.001,P:-0.0,-0.4,R:0.000,C:0.98,TC:0.00,TF:27,WM:False,FPS:False,CB:5,FSME:28,FSMS:0,MDF:20,MSDF:8"
507,20.264,False,3,0,3,YOLO_STABLE,"Y1:(178,191,27,26,702,0.764)|Y2:(400,252,17,24,408,0.559)|Y3:(369,454,13,13,169,0.438)","Z:1.001,P:-0.3,-0.4,R:-0.000,C:0.98,TC:0.00,TF:28,WM:False,FPS:False,CB:5,FSME:29,FSMS:0,MDF:20,MSDF:8"
508,20.304,False,2,0,2,YOLO_STABLE,"Y1:(400,246,18,31,558,0.579)|Y2:(369,454,13,13,169,0.425)","Z:1.001,P:0.2,-0.5,R:-0.000,C:0.98,TC:0.00,TF:29,WM:False,FPS:False,CB:5,FSME:30,FSMS:0,MDF:20,MSDF:8"
509,20.344,False,3,0,3,YOLO_STABLE,"Y1:(180,166,19,35,665,0.772)|Y2:(400,250,16,27,432,0.579)|Y3:(370,455,12,13,156,0.510)","Z:1.001,P:-0.2,0.2,R:0.000,C:0.97,TC:0.00,TF:30,WM:False,FPS:False,CB:5,FSME:31,FSMS:0,MDF:20,MSDF:8"
510,20.384,False,3,0,3,YOLO_STABLE,"Y1:(164,176,28,23,644,0.793)|Y2:(370,455,13,13,169,0.561)|Y3:(401,251,15,26,390,0.512)","Z:1.001,P:-0.2,-0.2,R:-0.000,C:0.97,TC:0.00,TF:31,WM:False,FPS:False,CB:5,FSME:32,FSMS:0,MDF:20,MSDF:8"
511,20.424,False,2,0,2,YOLO_STABLE,"Y1:(370,455,12,13,156,0.513)|Y2:(401,254,15,22,330,0.353)","Z:1.001,P:-0.1,-0.1,R:-0.000,C:0.99,TC:0.00,TF:32,WM:False,FPS:False,CB:5,FSME:33,FSMS:0,MDF:20,MSDF:8"
512,20.464,False,3,0,3,YOLO_STABLE,"Y1:(143,180,30,25,750,0.804)|Y2:(401,254,15,22,330,0.547)|Y3:(370,455,13,13,169,0.531)","Z:1.001,P:-0.1,-0.3,R:-0.000,C:1.00,TC:0.00,TF:33,WM:False,FPS:False,CB:5,FSME:34,FSMS:0,MDF:20,MSDF:8"
513,20.504,False,3,0,3,YOLO_STABLE,"Y1:(141,174,23,22,506,0.756)|Y2:(401,255,14,22,308,0.588)|Y3:(370,455,13,13,169,0.525)","Z:1.001,P:-0.3,-0.5,R:-0.000,C:0.97,TC:0.00,TF:34,WM:False,FPS:False,CB:5,FSME:35,FSMS:0,MDF:20,MSDF:8"
514,20.544,False,3,0,3,YOLO_STABLE,"Y1:(140,163,24,21,504,0.616)|Y2:(370,455,13,13,169,0.544)|Y3:(401,254,15,20,300,0.451)","Z:1.001,P:-0.0,-0.6,R:-0.000,C:0.98,TC:0.00,TF:35,WM:False,FPS:False,CB:5,FSME:36,FSMS:0,MDF:20,MSDF:8"
515,20.584,False,4,0,4,YOLO_STABLE,"Y1:(128,159,29,26,754,0.781)|Y2:(370,455,13,13,169,0.521)|Y3:(401,254,15,20,300,0.398)|Y4:(489,282,12,22,264,0.304)","Z:1.000,P:-0.1,-0.2,R:-0.000,C:1.00,TC:0.00,TF:36,WM:False,FPS:False,CB:5,FSME:37,FSMS:0,MDF:20,MSDF:8"
516,20.625,False,2,0,2,YOLO_STABLE,"Y1:(370,456,13,13,169,0.542)|Y2:(400,253,16,22,352,0.380)","Z:1.001,P:-0.0,-0.2,R:-0.000,C:0.97,TC:0.00,TF:37,WM:False,FPS:False,CB:5,FSME:38,FSMS:0,MDF:20,MSDF:8"
517,20.664,False,4,0,4,YOLO_STABLE,"Y1:(101,168,37,20,740,0.773)|Y2:(400,252,17,23,391,0.501)|Y3:(370,456,13,13,169,0.491)|Y4:(489,283,12,22,264,0.340)","Z:1.000,P:-0.1,-0.3,R:-0.000,C:0.97,TC:0.00,TF:38,WM:False,FPS:False,CB:5,FSME:39,FSMS:0,MDF:20,MSDF:8"
518,20.704,False,4,0,4,YOLO_STABLE,"Y1:(100,164,27,21,567,0.808)|Y2:(400,252,17,23,391,0.519)|Y3:(371,456,12,13,156,0.482)|Y4:(489,283,12,22,264,0.371)","Z:1.000,P:-0.0,-0.1,R:-0.000,C:0.96,TC:0.00,TF:39,WM:False,FPS:False,CB:5,FSME:40,FSMS:0,MDF:20,MSDF:8"
519,20.744,False,3,0,3,YOLO_STABLE,"Y1:(400,254,16,23,368,0.583)|Y2:(370,456,13,14,182,0.529)|Y3:(489,283,12,22,264,0.363)","Z:1.000,P:-0.1,-0.0,R:-0.000,C:0.98,TC:0.00,TF:40,WM:False,FPS:False,CB:5,FSME:41,FSMS:0,MDF:20,MSDF:8"
520,20.784,False,4,0,4,YOLO_STABLE,"Y1:(400,250,18,27,486,0.626)|Y2:(86,145,37,31,1147,0.610)|Y3:(370,456,13,14,182,0.512)|Y4:(490,283,12,22,264,0.365)","Z:1.000,P:0.1,-0.1,R:-0.000,C:0.99,TC:0.00,TF:41,WM:False,FPS:False,CB:5,FSME:42,FSMS:0,MDF:20,MSDF:8"
521,20.824,False,3,0,3,YOLO_STABLE,"Y1:(400,251,17,26,442,0.623)|Y2:(370,456,13,13,169,0.503)|Y3:(489,283,12,22,264,0.337)","Z:1.000,P:-0.1,0.0,R:-0.000,C:1.00,TC:0.00,TF:42,WM:False,FPS:False,CB:5,FSME:43,FSMS:0,MDF:20,MSDF:8"
522,20.864,False,4,0,4,YOLO_STABLE,"Y1:(400,250,17,26,442,0.673)|Y2:(370,456,13,14,182,0.533)|Y3:(73,156,29,16,464,0.522)|Y4:(489,282,12,22,264,0.335)","Z:1.001,P:-0.1,-0.5,R:-0.000,C:0.99,TC:0.00,TF:43,WM:False,FPS:False,CB:5,FSME:44,FSMS:0,MDF:20,MSDF:8"
523,20.904,False,3,0,3,YOLO_STABLE,"Y1:(400,251,17,25,425,0.674)|Y2:(62,152,30,22,660,0.629)|Y3:(370,456,13,13,169,0.564)","Z:1.000,P:0.1,-0.1,R:0.000,C:0.97,TC:0.00,TF:44,WM:False,FPS:False,CB:5,FSME:45,FSMS:0,MDF:20,MSDF:8"
524,20.944,False,3,0,3,YOLO_STABLE,"Y1:(400,252,16,24,384,0.643)|Y2:(370,456,13,13,169,0.617)|Y3:(63,139,25,32,800,0.560)","Z:1.000,P:0.1,-0.1,R:-0.000,C:0.98,TC:0.00,TF:45,WM:False,FPS:False,CB:5,FSME:46,FSMS:0,MDF:20,MSDF:8"
525,20.984,False,3,0,3,YOLO_STABLE,"Y1:(67,139,26,26,676,0.687)|Y2:(400,252,16,24,384,0.659)|Y3:(370,456,13,13,169,0.562)","Z:1.000,P:0.3,0.1,R:-0.000,C:0.98,TC:0.00,TF:46,WM:False,FPS:False,CB:5,FSME:47,FSMS:0,MDF:20,MSDF:8"
526,21.024,False,4,0,4,YOLO_STABLE,"Y1:(46,131,39,32,1248,0.760)|Y2:(400,251,16,27,432,0.655)|Y3:(370,456,13,13,169,0.578)|Y4:(490,283,12,23,276,0.450)","Z:1.000,P:0.3,0.2,R:-0.000,C:0.97,TC:0.00,TF:47,WM:False,FPS:False,CB:5,FSME:48,FSMS:0,MDF:20,MSDF:8"
527,21.064,False,2,0,2,YOLO_STABLE,"Y1:(400,250,17,27,459,0.638)|Y2:(370,457,13,13,169,0.573)","Z:1.000,P:-0.2,0.0,R:-0.000,C:0.97,TC:0.00,TF:48,WM:False,FPS:False,CB:5,FSME:49,FSMS:0,MDF:20,MSDF:8"
528,21.104,False,4,0,4,YOLO_STABLE,"Y1:(400,248,18,28,504,0.703)|Y2:(31,140,33,18,594,0.699)|Y3:(370,456,14,14,196,0.562)|Y4:(490,283,12,23,276,0.300)","Z:1.000,P:0.2,-0.6,R:-0.000,C:0.98,TC:0.00,TF:49,WM:False,FPS:False,CB:5,FSME:50,FSMS:0,MDF:20,MSDF:8"
529,21.144,False,3,0,3,YOLO_STABLE,"Y1:(26,137,31,20,620,0.779)|Y2:(399,247,18,30,540,0.750)|Y3:(370,456,13,13,169,0.615)","Z:1.000,P:-0.0,-0.1,R:-0.000,C:0.98,TC:0.00,TF:50,WM:False,FPS:False,CB:5,FSME:51,FSMS:0,MDF:20,MSDF:8"
530,21.184,False,3,0,3,YOLO_STABLE,"Y1:(399,247,18,30,540,0.740)|Y2:(370,456,13,13,169,0.610)|Y3:(26,127,32,22,704,0.447)","Z:1.000,P:0.2,-0.2,R:-0.000,C:0.97,TC:0.00,TF:51,WM:False,FPS:False,CB:5,FSME:52,FSMS:0,MDF:20,MSDF:8"
531,21.224,False,4,0,4,YOLO_STABLE,"Y1:(21,121,34,27,918,0.784)|Y2:(400,250,18,28,504,0.726)|Y3:(370,456,13,13,169,0.616)|Y4:(490,282,12,22,264,0.443)","Z:1.000,P:0.4,0.3,R:-0.000,C:0.97,TC:0.00,TF:52,WM:False,FPS:False,CB:5,FSME:53,FSMS:0,MDF:20,MSDF:8"
532,21.264,False,4,0,4,YOLO_STABLE,"Y1:(11,123,39,24,936,0.757)|Y2:(400,251,17,28,476,0.741)|Y3:(370,456,14,13,182,0.597)|Y4:(490,282,12,23,276,0.447)","Z:1.000,P:0.2,0.2,R:0.000,C:0.99,TC:0.00,TF:53,WM:False,FPS:False,CB:5,FSME:54,FSMS:0,MDF:20,MSDF:8"
533,21.304,False,3,0,3,YOLO_STABLE,"Y1:(400,253,17,26,442,0.701)|Y2:(370,456,13,14,182,0.556)|Y3:(490,283,12,22,264,0.318)","Z:1.000,P:0.1,-0.0,R:-0.000,C:0.98,TC:0.00,TF:54,WM:False,FPS:False,CB:5,FSME:55,FSMS:0,MDF:20,MSDF:8"
534,21.344,False,4,0,4,YOLO_STABLE,"Y1:(400,252,17,28,476,0.739)|Y2:(0,125,28,20,560,0.597)|Y3:(370,456,13,14,182,0.590)|Y4:(490,282,12,22,264,0.412)","Z:1.000,P:0.2,-0.3,R:-0.000,C:0.98,TC:0.00,TF:55,WM:False,FPS:False,CB:5,FSME:56,FSMS:0,MDF:20,MSDF:8"
535,21.384,False,4,0,4,YOLO_STABLE,"Y1:(400,252,17,28,476,0.743)|Y2:(370,456,13,13,169,0.523)|Y3:(489,282,12,23,276,0.355)|Y4:(0,116,25,24,600,0.347)","Z:1.000,P:0.0,-0.2,R:-0.000,C:1.00,TC:0.00,TF:56,WM:False,FPS:False,CB:5,FSME:57,FSMS:0,MDF:20,MSDF:8"
536,21.424,False,4,0,4,YOLO_STABLE,"Y1:(400,250,17,30,510,0.752)|Y2:(370,456,13,13,169,0.577)|Y3:(1,117,27,16,432,0.569)|Y4:(489,282,12,23,276,0.357)","Z:1.000,P:0.2,-0.0,R:-0.000,C:0.98,TC:0.00,TF:57,WM:False,FPS:False,CB:5,FSME:58,FSMS:0,MDF:20,MSDF:8"
537,21.464,False,3,0,3,YOLO_STABLE,"Y1:(400,250,17,30,510,0.750)|Y2:(370,456,13,13,169,0.509)|Y3:(490,283,12,23,276,0.364)","Z:1.000,P:0.4,0.3,R:-0.000,C:0.99,TC:0.00,TF:58,WM:False,FPS:False,CB:5,FSME:59,FSMS:0,MDF:20,MSDF:8"
538,21.504,False,3,0,3,YOLO_STABLE,"Y1:(400,251,15,29,435,0.741)|Y2:(370,456,13,14,182,0.480)|Y3:(490,283,12,23,276,0.394)","Z:1.000,P:0.2,0.2,R:-0.000,C:1.00,TC:0.00,TF:59,WM:False,FPS:False,CB:5,FSME:60,FSMS:0,MDF:20,MSDF:8"
539,21.543,False,3,0,3,YOLO_STABLE,"Y1:(400,253,15,26,390,0.742)|Y2:(370,456,14,13,182,0.469)|Y3:(490,282,12,23,276,0.304)","Z:1.000,P:0.0,-0.2,R:-0.000,C:0.99,TC:0.00,TF:60,WM:False,FPS:False,CB:5,FSME:61,FSMS:0,MDF:20,MSDF:8"
540,21.583,False,2,0,2,YOLO_STABLE,"Y1:(400,252,15,26,390,0.765)|Y2:(370,455,14,13,182,0.632)","Z:1.000,P:0.2,-0.4,R:-0.000,C:0.97,TC:0.00,TF:61,WM:False,FPS:False,CB:5,FSME:62,FSMS:0,MDF:20,MSDF:8"
541,21.623,False,2,0,2,YOLO_STABLE,"Y1:(400,251,15,26,390,0.759)|Y2:(369,455,14,13,182,0.596)","Z:1.000,P:0.1,-0.4,R:-0.001,C:0.99,TC:0.00,TF:62,WM:False,FPS:False,CB:5,FSME:63,FSMS:0,MDF:20,MSDF:8"
542,21.663,False,2,0,2,YOLO_STABLE,"Y1:(400,252,15,26,390,0.737)|Y2:(369,455,14,13,182,0.540)","Z:1.000,P:0.3,0.2,R:0.000,C:0.96,TC:0.00,TF:63,WM:False,FPS:False,CB:5,FSME:64,FSMS:0,MDF:20,MSDF:8"
543,21.703,False,2,0,2,YOLO_STABLE,"Y1:(401,253,15,27,405,0.725)|Y2:(369,456,14,14,196,0.431)","Z:1.000,P:0.2,0.3,R:-0.000,C:0.96,TC:0.00,TF:64,WM:False,FPS:False,CB:5,FSME:65,FSMS:0,MDF:20,MSDF:8"
544,21.743,False,2,0,2,YOLO_STABLE,"Y1:(401,254,15,27,405,0.678)|Y2:(369,456,14,14,196,0.389)","Z:1.000,P:0.2,0.3,R:-0.000,C:0.96,TC:0.00,TF:65,WM:False,FPS:False,CB:5,FSME:66,FSMS:0,MDF:20,MSDF:8"
545,21.783,False,2,0,2,YOLO_STABLE,"Y1:(401,253,15,26,390,0.649)|Y2:(370,456,14,14,196,0.417)","Z:1.000,P:0.1,-0.1,R:0.000,C:0.99,TC:0.00,TF:66,WM:False,FPS:False,CB:5,FSME:67,FSMS:0,MDF:20,MSDF:8"
546,21.823,False,2,0,2,YOLO_STABLE,"Y1:(400,252,16,26,416,0.693)|Y2:(369,456,14,15,210,0.515)","Z:1.000,P:0.0,-0.5,R:-0.000,C:1.00,TC:0.00,TF:67,WM:False,FPS:False,CB:5,FSME:68,FSMS:0,MDF:20,MSDF:8"
547,21.863,False,2,0,2,YOLO_STABLE,"Y1:(400,251,15,26,390,0.723)|Y2:(369,455,14,14,196,0.512)","Z:1.000,P:0.1,-0.5,R:-0.000,C:1.00,TC:0.00,TF:68,WM:False,FPS:False,CB:5,FSME:69,FSMS:0,MDF:20,MSDF:8"
548,21.903,False,2,0,2,YOLO_STABLE,"Y1:(400,252,16,26,416,0.723)|Y2:(369,455,15,15,225,0.558)","Z:1.000,P:0.3,0.2,R:-0.000,C:0.99,TC:0.00,TF:69,WM:False,FPS:False,CB:5,FSME:70,FSMS:0,MDF:20,MSDF:8"
549,21.943,False,2,0,2,YOLO_STABLE,"Y1:(400,252,16,27,432,0.700)|Y2:(369,456,15,14,210,0.559)","Z:1.000,P:0.3,0.5,R:-0.000,C:0.97,TC:0.00,TF:70,WM:False,FPS:False,CB:5,FSME:71,FSMS:0,MDF:20,MSDF:8"
550,21.983,False,2,0,2,YOLO_STABLE,"Y1:(402,251,15,30,450,0.640)|Y2:(369,457,13,15,195,0.507)","Z:1.000,P:0.7,0.5,R:-0.000,C:0.93,TC:0.00,TF:71,WM:False,FPS:False,CB:5,FSME:72,FSMS:0,MDF:20,MSDF:8"
551,22.023,False,2,0,2,YOLO_STABLE,"Y1:(402,251,15,30,450,0.635)|Y2:(370,457,13,15,195,0.524)","Z:1.000,P:-0.1,0.1,R:0.000,C:0.96,TC:0.00,TF:72,WM:False,FPS:False,CB:5,FSME:73,FSMS:0,MDF:20,MSDF:8"
552,22.063,False,2,0,2,YOLO_STABLE,"Y1:(402,253,14,26,364,0.553)|Y2:(370,457,12,15,180,0.501)","Z:1.000,P:-0.2,-0.6,R:-0.000,C:0.98,TC:0.00,TF:73,WM:False,FPS:False,CB:5,FSME:74,FSMS:0,MDF:20,MSDF:8"
553,22.103,False,2,0,2,YOLO_STABLE,"Y1:(370,455,12,14,168,0.496)|Y2:(401,251,14,24,336,0.426)","Z:1.000,P:0.3,-0.7,R:-0.000,C:0.99,TC:0.00,TF:74,WM:False,FPS:False,CB:5,FSME:75,FSMS:0,MDF:20,MSDF:8"
554,22.143,False,2,0,2,YOLO_STABLE,"Y1:(370,455,13,13,169,0.490)|Y2:(401,252,14,24,336,0.458)","Z:1.000,P:0.1,0.0,R:-0.000,C:0.98,TC:0.00,TF:75,WM:False,FPS:False,CB:5,FSME:76,FSMS:0,MDF:20,MSDF:8"
555,22.183,False,2,0,2,YOLO_STABLE,"Y1:(370,456,13,13,169,0.401)|Y2:(401,253,15,24,360,0.364)","Z:1.000,P:0.5,0.7,R:-0.000,C:1.00,TC:0.00,TF:76,WM:False,FPS:False,CB:5,FSME:77,FSMS:0,MDF:20,MSDF:8"
556,22.223,False,2,0,2,YOLO_STABLE,"Y1:(370,457,13,13,169,0.322)|Y2:(402,252,16,25,400,0.318)","Z:1.000,P:0.2,0.4,R:-0.000,C:0.98,TC:0.00,TF:77,WM:False,FPS:False,CB:5,FSME:78,FSMS:0,MDF:20,MSDF:8"
557,22.263,False,1,0,1,YOLO_STABLE,"Y1:(370,457,13,13,169,0.306)","Z:1.000,P:0.0,-0.1,R:-0.000,C:0.99,TC:0.00,TF:78,WM:False,FPS:False,CB:5,FSME:79,FSMS:0,MDF:20,MSDF:8"
558,22.303,False,1,0,1,YOLO_STABLE,"Y1:(370,457,13,13,169,0.367)","Z:0.999,P:0.1,0.3,R:-0.000,C:1.00,TC:0.00,TF:79,WM:False,FPS:False,CB:5,FSME:80,FSMS:0,MDF:20,MSDF:8"
559,22.343,False,1,0,1,YOLO_STABLE,"Y1:(369,457,13,13,169,0.322)","Z:1.000,P:-0.2,-0.4,R:-0.000,C:0.99,TC:0.00,TF:80,WM:False,FPS:False,CB:5,FSME:81,FSMS:0,MDF:20,MSDF:8"
560,22.383,False,1,0,1,YOLO_STABLE,"Y1:(370,456,13,13,169,0.483)","Z:1.000,P:0.1,-0.3,R:-0.000,C:1.00,TC:0.00,TF:81,WM:False,FPS:False,CB:5,FSME:82,FSMS:0,MDF:20,MSDF:8"
561,22.423,False,1,0,1,YOLO_STABLE,"Y1:(370,457,13,13,169,0.415)","Z:1.000,P:0.2,0.2,R:0.000,C:0.99,TC:0.00,TF:82,WM:False,FPS:False,CB:5,FSME:83,FSMS:0,MDF:20,MSDF:8"
562,22.463,False,1,0,1,YOLO_STABLE,"Y1:(370,456,13,14,182,0.511)","Z:1.000,P:0.4,-0.2,R:-0.000,C:0.98,TC:0.00,TF:83,WM:False,FPS:False,CB:5,FSME:84,FSMS:0,MDF:20,MSDF:8"
563,22.503,False,1,0,1,YOLO_STABLE,"Y1:(370,457,13,13,169,0.452)","Z:1.000,P:0.3,0.4,R:0.000,C:0.99,TC:0.00,TF:84,WM:False,FPS:False,CB:5,FSME:85,FSMS:0,MDF:20,MSDF:8"
564,22.543,False,1,0,1,YOLO_STABLE,"Y1:(370,457,13,13,169,0.470)","Z:1.000,P:-0.1,-0.1,R:-0.000,C:0.99,TC:0.00,TF:85,WM:False,FPS:False,CB:5,FSME:86,FSMS:0,MDF:20,MSDF:8"
565,22.583,False,1,0,1,YOLO_STABLE,"Y1:(370,457,13,13,169,0.477)","Z:0.999,P:0.3,0.3,R:-0.000,C:0.98,TC:0.00,TF:86,WM:False,FPS:False,CB:5,FSME:87,FSMS:0,MDF:20,MSDF:8"
566,22.623,False,1,0,1,YOLO_STABLE,"Y1:(370,456,13,13,169,0.435)","Z:1.000,P:-0.1,-0.5,R:-0.000,C:0.99,TC:0.00,TF:87,WM:False,FPS:False,CB:5,FSME:88,FSMS:0,MDF:20,MSDF:8"
567,22.663,False,1,0,1,YOLO_STABLE,"Y1:(370,456,13,13,169,0.401)","Z:1.000,P:-0.0,-0.2,R:-0.000,C:1.00,TC:0.00,TF:88,WM:False,FPS:False,CB:5,FSME:89,FSMS:0,MDF:20,MSDF:8"
568,22.703,False,1,0,1,YOLO_STABLE,"Y1:(370,456,13,12,156,0.491)","Z:1.000,P:0.2,-0.2,R:-0.000,C:0.98,TC:0.00,TF:89,WM:False,FPS:False,CB:5,FSME:90,FSMS:0,MDF:20,MSDF:8"
569,22.743,False,1,0,1,YOLO_STABLE,"Y1:(370,456,13,12,156,0.459)","Z:0.999,P:0.4,0.3,R:-0.000,C:1.00,TC:0.00,TF:90,WM:False,FPS:False,CB:5,FSME:91,FSMS:0,MDF:20,MSDF:8"
570,22.783,False,1,0,1,YOLO_STABLE,"Y1:(370,456,13,12,156,0.355)","Z:1.000,P:0.3,0.1,R:-0.000,C:1.00,TC:0.00,TF:91,WM:False,FPS:False,CB:5,FSME:92,FSMS:0,MDF:20,MSDF:8"
571,22.823,False,0,2,2,TEMPORAL,"T1:(392,197,12,6,16.5,0.600,1,1)|T2:(499,196,9,4,12.0,0.600,1,1)","Z:1.000,P:-0.1,-0.0,R:-0.000,C:0.99,TC:0.00,TF:92,WM:False,FPS:False,CB:5,FSME:93,FSMS:0,MDF:20,MSDF:8"
572,22.862,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.2,0.0,R:-0.000,C:0.99,TC:0.00,TF:93,WM:False,FPS:False,CB:5,FSME:94,FSMS:0,MDF:20,MSDF:8"
573,22.902,False,0,4,4,TEMPORAL,"T1:(51,458,6,5,12.5,0.700,2,2)|T2:(371,201,10,3,9.0,0.825,4,4)|T3:(193,155,8,3,8.0,0.833,3,3)|T4:(34,13,31,16,254.5,0.700,2,2)","Z:1.000,P:0.1,-0.1,R:-0.000,C:0.99,TC:0.00,TF:94,WM:False,FPS:False,CB:5,FSME:95,FSMS:0,MDF:20,MSDF:8"
574,22.942,False,0,7,7,TEMPORAL,"T1:(38,487,24,17,80.5,0.800,3,2)|T2:(11,425,6,3,8.0,0.800,3,3)|T3:(376,408,6,4,8.0,0.600,1,1)|T4:(194,155,8,3,9.0,0.767,3,2)|T5:(198,81,14,37,110.0,0.800,3,2)|T6:(34,17,17,10,90.0,0.650,4,2)|T7:(39,4,37,17,214.5,0.833,3,3)","Z:1.000,P:0.3,-0.2,R:-0.000,C:0.99,TC:0.00,TF:95,WM:False,FPS:False,CB:5,FSME:96,FSMS:0,MDF:20,MSDF:8"
575,22.982,False,0,9,9,TEMPORAL,"T1:(37,499,11,6,24.5,0.800,4,4)|T2:(55,487,8,6,21.0,0.800,4,4)|T3:(53,459,10,14,33.0,0.833,3,3)|T4:(25,423,11,6,21.5,0.775,4,3)|T5:(283,285,7,5,9.5,0.800,2,2)|T6:(199,95,16,23,77.0,0.825,4,4)|T7:(200,81,11,12,36.0,0.800,4,4)|T8:(35,18,16,9,81.0,0.675,4,2)|T9:(57,0,27,14,125.0,0.700,2,2)","Z:1.000,P:0.4,-0.0,R:-0.000,C:0.98,TC:0.00,TF:96,WM:False,FPS:False,CB:5,FSME:97,FSMS:0,MDF:20,MSDF:8"
576,23.022,False,0,11,11,TEMPORAL,"T1:(37,495,12,11,32.0,0.675,4,2)|T2:(49,494,8,4,12.0,0.675,4,2)|T3:(56,459,8,14,34.0,0.767,3,2)|T4:(27,423,12,6,27.0,0.800,4,3)|T5:(7,234,8,5,18.0,0.725,4,2)|T6:(405,196,13,6,19.0,0.800,2,2)|T7:(388,177,5,5,10.5,0.750,2,2)|T8:(205,133,8,10,36.0,0.750,4,2)|T9:(201,95,16,24,81.5,0.825,4,3)|T10:(202,81,11,11,47.0,0.750,4,3)|T11:(62,0,26,15,136.5,0.733,3,2)","Z:1.000,P:0.2,0.2,R:-0.000,C:0.98,TC:0.00,TF:97,WM:False,FPS:False,CB:5,FSME:98,FSMS:0,MDF:20,MSDF:8"
577,23.062,False,2,0,2,YOLO_STABLE,"Y1:(370,457,13,13,169,0.349)|Y2:(402,254,14,23,322,0.332)","Z:1.000,P:0.1,-0.0,R:-0.000,C:1.00,TC:0.00,TF:98,WM:False,FPS:False,CB:5,FSME:99,FSMS:0,MDF:20,MSDF:8"
578,23.102,False,2,0,2,YOLO_STABLE,"Y1:(370,457,13,13,169,0.356)|Y2:(402,254,14,23,322,0.330)","Z:1.000,P:0.0,-0.2,R:-0.000,C:0.99,TC:0.00,TF:99,WM:False,FPS:False,CB:5,FSME:100,FSMS:0,MDF:20,MSDF:8"
579,23.142,False,2,0,2,YOLO_STABLE,"Y1:(370,456,13,12,156,0.448)|Y2:(402,255,14,22,308,0.336)","Z:0.999,P:0.4,-0.1,R:-0.000,C:0.99,TC:0.00,TF:100,WM:False,FPS:False,CB:5,FSME:101,FSMS:0,MDF:20,MSDF:8"
580,23.182,False,1,0,1,YOLO_STABLE,"Y1:(370,456,13,12,156,0.337)","Z:1.000,P:0.5,0.3,R:-0.000,C:0.99,TC:0.00,TF:101,WM:False,FPS:False,CB:5,FSME:102,FSMS:0,MDF:20,MSDF:8"
581,23.222,False,2,0,2,YOLO_STABLE,"Y1:(370,457,13,13,169,0.359)|Y2:(402,255,14,23,322,0.311)","Z:1.000,P:0.0,-0.1,R:-0.000,C:1.00,TC:0.00,TF:102,WM:False,FPS:False,CB:5,FSME:103,FSMS:0,MDF:20,MSDF:8"
582,23.262,False,1,0,1,YOLO_STABLE,"Y1:(402,254,15,23,345,0.360)","Z:1.000,P:0.2,-0.4,R:-0.000,C:0.99,TC:0.00,TF:103,WM:False,FPS:False,CB:5,FSME:104,FSMS:0,MDF:20,MSDF:8"
583,23.302,False,2,0,2,YOLO_STABLE,"Y1:(370,456,13,12,156,0.424)|Y2:(401,254,15,23,345,0.319)","Z:0.999,P:0.3,0.2,R:-0.000,C:0.99,TC:0.00,TF:104,WM:False,FPS:False,CB:5,FSME:105,FSMS:0,MDF:20,MSDF:8"
584,23.342,False,2,0,2,YOLO_STABLE,"Y1:(402,254,15,24,360,0.435)|Y2:(369,457,13,13,169,0.333)","Z:1.000,P:0.0,0.0,R:-0.000,C:0.99,TC:0.00,TF:105,WM:False,FPS:False,CB:5,FSME:106,FSMS:0,MDF:20,MSDF:8"
585,23.382,False,2,0,2,YOLO_STABLE,"Y1:(402,255,15,24,360,0.444)|Y2:(370,456,13,12,156,0.366)","Z:0.999,P:0.3,0.1,R:-0.000,C:1.00,TC:0.00,TF:106,WM:False,FPS:False,CB:5,FSME:107,FSMS:0,MDF:20,MSDF:8"
586,23.422,False,2,0,2,YOLO_STABLE,"Y1:(402,254,15,24,360,0.438)|Y2:(370,456,13,12,156,0.384)","Z:1.000,P:0.2,-0.2,R:-0.000,C:1.00,TC:0.00,TF:107,WM:False,FPS:False,CB:5,FSME:108,FSMS:0,MDF:20,MSDF:8"
587,23.462,False,2,0,2,YOLO_STABLE,"Y1:(402,254,14,26,364,0.523)|Y2:(370,457,13,13,169,0.345)","Z:1.000,P:0.2,0.2,R:-0.000,C:0.99,TC:0.00,TF:108,WM:False,FPS:False,CB:5,FSME:109,FSMS:0,MDF:20,MSDF:8"
588,23.502,False,2,0,2,YOLO_STABLE,"Y1:(402,254,15,26,390,0.531)|Y2:(370,456,13,12,156,0.403)","Z:1.000,P:0.2,-0.2,R:-0.000,C:0.99,TC:0.00,TF:109,WM:False,FPS:False,CB:5,FSME:110,FSMS:0,MDF:20,MSDF:8"
589,23.542,False,2,0,2,YOLO_STABLE,"Y1:(402,254,14,25,350,0.455)|Y2:(370,456,13,12,156,0.432)","Z:1.000,P:0.3,-0.1,R:-0.000,C:0.99,TC:0.00,TF:110,WM:False,FPS:False,CB:5,FSME:111,FSMS:0,MDF:20,MSDF:8"
590,23.582,False,2,0,2,YOLO_STABLE,"Y1:(402,253,15,27,405,0.547)|Y2:(370,456,13,12,156,0.359)","Z:1.000,P:0.3,0.2,R:-0.000,C:1.00,TC:0.00,TF:111,WM:False,FPS:False,CB:5,FSME:112,FSMS:0,MDF:20,MSDF:8"
591,23.622,False,2,0,2,YOLO_STABLE,"Y1:(370,456,14,12,168,0.349)|Y2:(401,251,15,28,420,0.304)","Z:1.000,P:0.2,-0.2,R:-0.000,C:1.00,TC:0.00,TF:112,WM:False,FPS:False,CB:5,FSME:113,FSMS:0,MDF:20,MSDF:8"
592,23.662,False,2,0,2,YOLO_STABLE,"Y1:(401,253,15,27,405,0.420)|Y2:(370,456,13,12,156,0.387)","Z:1.000,P:-0.0,0.1,R:-0.000,C:0.99,TC:0.00,TF:113,WM:False,FPS:False,CB:5,FSME:114,FSMS:0,MDF:20,MSDF:8"
593,23.702,False,2,0,2,YOLO_STABLE,"Y1:(401,253,15,27,405,0.455)|Y2:(370,456,13,12,156,0.357)","Z:1.000,P:0.2,0.0,R:-0.000,C:0.99,TC:0.00,TF:114,WM:False,FPS:False,CB:5,FSME:115,FSMS:0,MDF:20,MSDF:8"
594,23.742,False,2,0,2,YOLO_STABLE,"Y1:(402,253,15,30,450,0.541)|Y2:(369,456,14,12,168,0.332)","Z:1.000,P:0.4,-0.2,R:-0.000,C:0.98,TC:0.00,TF:115,WM:False,FPS:False,CB:5,FSME:116,FSMS:0,MDF:20,MSDF:8"
595,23.782,False,2,0,2,YOLO_STABLE,"Y1:(402,253,15,29,435,0.425)|Y2:(369,456,14,13,182,0.384)","Z:1.000,P:0.1,0.3,R:-0.000,C:0.99,TC:0.00,TF:116,WM:False,FPS:False,CB:5,FSME:117,FSMS:0,MDF:20,MSDF:8"
596,23.822,False,2,0,2,YOLO_STABLE,"Y1:(369,456,14,13,182,0.421)|Y2:(401,252,15,30,450,0.404)","Z:1.000,P:0.1,-0.1,R:-0.000,C:0.99,TC:0.00,TF:117,WM:False,FPS:False,CB:5,FSME:118,FSMS:0,MDF:20,MSDF:8"
597,23.862,False,2,0,2,YOLO_STABLE,"Y1:(401,241,16,40,640,0.507)|Y2:(370,456,14,13,182,0.411)","Z:1.000,P:0.0,0.1,R:0.000,C:1.00,TC:0.00,TF:118,WM:False,FPS:False,CB:5,FSME:119,FSMS:0,MDF:20,MSDF:8"
598,23.902,False,3,0,3,YOLO_STABLE,"Y1:(369,456,14,12,168,0.422)|Y2:(401,241,15,39,585,0.366)|Y3:(272,125,18,18,324,0.330)","Z:0.999,P:0.3,0.1,R:-0.000,C:0.93,TC:0.00,TF:119,WM:False,FPS:False,CB:5,FSME:120,FSMS:0,MDF:20,MSDF:8"
599,23.942,False,3,0,3,YOLO_STABLE,"Y1:(369,456,14,12,168,0.396)|Y2:(401,242,15,37,555,0.368)|Y3:(272,124,19,18,342,0.313)","Z:1.000,P:-0.0,-0.2,R:-0.000,C:0.99,TC:0.00,TF:120,WM:False,FPS:False,CB:5,FSME:121,FSMS:0,MDF:20,MSDF:8"
600,23.982,False,1,0,1,YOLO_STABLE,"Y1:(401,249,13,29,377,0.444)","Z:1.000,P:-0.0,-0.5,R:-0.000,C:0.95,TC:0.00,TF:121,WM:False,FPS:False,CB:5,FSME:122,FSMS:0,MDF:20,MSDF:8"
601,24.022,False,1,0,1,YOLO_STABLE,"Y1:(370,456,12,12,144,0.340)","Z:0.999,P:0.4,0.1,R:-0.001,C:1.00,TC:0.00,TF:122,WM:False,FPS:False,CB:5,FSME:123,FSMS:0,MDF:20,MSDF:8"
602,24.062,False,0,1,1,TEMPORAL,"T1:(254,127,5,5,12.5,0.600,1,1)","Z:1.000,P:0.2,0.3,R:0.000,C:0.99,TC:0.00,TF:123,WM:False,FPS:False,CB:5,FSME:124,FSMS:0,MDF:20,MSDF:8"
603,24.102,False,0,0,0,BUILDING_CONFIDENCE,None,"Z:1.000,P:0.1,0.1,R:-0.000,C:0.99,TC:0.00,TF:124,WM:False,FPS:False,CB:5,FSME:125,FSMS:0,MDF:20,MSDF:8"
604,24.142,False,1,0,1,YOLO_STABLE,"Y1:(490,284,12,19,228,0.341)","Z:1.000,P:0.2,-0.1,R:-0.000,C:1.00,TC:0.00,TF:125,WM:False,FPS:False,CB:5,FSME:126,FSMS:0,MDF:20,MSDF:8"
605,24.181,False,1,0,1,YOLO_STABLE,"Y1:(490,284,12,19,228,0.393)","Z:1.000,P:0.0,-0.3,R:-0.000,C:0.99,TC:0.00,TF:126,WM:False,FPS:False,CB:5,FSME:127,FSMS:0,MDF:20,MSDF:8"
606,24.221,True,4,0,0,MOVEMENT_START_DELAY_1/5,None,"Z:0.998,P:-0.5,0.9,R:0.001,C:0.97,TC:0.00,TF:0,WM:True,FPS:False,CB:5,FSME:0,FSMS:1,MDF:20,MSDF:9"
607,24.261,True,2,0,0,MOVEMENT_START_DELAY_2/5,None,"Z:1.000,P:-3.1,2.2,R:-0.001,C:0.96,TC:0.20,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:2,MDF:20,MSDF:10"
608,24.301,True,2,0,0,MOVEMENT_START_DELAY_3/5,None,"Z:1.000,P:-2.1,0.8,R:0.001,C:0.93,TC:0.40,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:3,MDF:20,MSDF:11"
609,24.342,True,3,0,0,MOVEMENT_START_DELAY_4/5,None,"Z:1.000,P:-6.0,3.4,R:0.002,C:0.87,TC:0.60,TF:3,WM:False,FPS:False,CB:5,FSME:0,FSMS:4,MDF:20,MSDF:12"
610,24.381,True,2,0,2,YOLO_MOVING,"Y1:(383,257,14,29,406,0.413)|Y2:(355,462,11,12,132,0.312)","Z:1.001,P:-6.9,0.3,R:0.001,C:0.95,TC:0.80,TF:4,WM:False,FPS:False,CB:5,FSME:0,FSMS:5,MDF:20,MSDF:12"
611,24.421,True,1,0,1,YOLO_MOVING,"Y1:(373,261,14,28,392,0.675)","Z:0.998,P:-8.9,2.6,R:0.000,C:0.82,TC:1.00,TF:5,WM:False,FPS:False,CB:5,FSME:0,FSMS:6,MDF:20,MSDF:12"
612,24.461,True,1,0,1,YOLO_MOVING,"Y1:(363,262,13,25,325,0.533)","Z:1.002,P:-11.7,0.9,R:0.000,C:0.93,TC:1.00,TF:6,WM:False,FPS:False,CB:5,FSME:0,FSMS:7,MDF:20,MSDF:12"
613,24.501,True,2,0,2,YOLO_MOVING,"Y1:(355,266,15,28,420,0.687)|Y2:(443,297,12,19,228,0.321)","Z:1.004,P:-8.1,1.2,R:-0.003,C:0.75,TC:1.00,TF:7,WM:False,FPS:False,CB:5,FSME:0,FSMS:8,MDF:20,MSDF:12"
614,24.541,True,1,0,1,YOLO_MOVING,"Y1:(352,269,14,25,350,0.486)","Z:1.003,P:-4.1,1.3,R:-0.001,C:0.97,TC:1.00,TF:8,WM:False,FPS:False,CB:5,FSME:0,FSMS:9,MDF:20,MSDF:12"
615,24.581,True,2,0,2,YOLO_MOVING,"Y1:(349,271,14,27,378,0.509)|Y2:(318,475,13,12,156,0.314)","Z:1.007,P:-4.3,-0.9,R:-0.001,C:0.93,TC:1.00,TF:9,WM:False,FPS:False,CB:5,FSME:0,FSMS:10,MDF:20,MSDF:12"
616,24.621,True,3,0,3,YOLO_MOVING,"Y1:(347,271,14,27,378,0.655)|Y2:(438,302,12,22,264,0.423)|Y3:(315,479,13,12,156,0.337)","Z:1.011,P:-6.2,-2.0,R:0.001,C:0.97,TC:1.00,TF:10,WM:True,FPS:False,CB:5,FSME:0,FSMS:11,MDF:20,MSDF:12"
617,24.661,True,3,0,3,YOLO_MOVING,"Y1:(346,271,14,28,392,0.651)|Y2:(438,303,12,22,264,0.524)|Y3:(314,481,12,12,144,0.464)","Z:1.015,P:-6.1,-4.8,R:-0.001,C:0.92,TC:1.00,TF:11,WM:True,FPS:False,CB:5,FSME:0,FSMS:12,MDF:20,MSDF:12"
618,24.701,True,3,0,3,YOLO_MOVING,"Y1:(345,272,14,28,392,0.608)|Y2:(311,485,14,13,182,0.424)|Y3:(438,303,13,22,286,0.323)","Z:1.022,P:-9.4,-5.4,R:0.001,C:0.78,TC:1.00,TF:12,WM:True,FPS:False,CB:5,FSME:0,FSMS:13,MDF:20,MSDF:12"
619,24.741,True,2,0,2,YOLO_MOVING,"Y1:(344,272,14,28,392,0.524)|Y2:(440,304,13,24,312,0.314)","Z:1.028,P:-9.9,-7.5,R:-0.001,C:0.85,TC:1.00,TF:13,WM:False,FPS:False,CB:5,FSME:0,FSMS:14,MDF:20,MSDF:12"
620,24.781,True,2,0,2,YOLO_MOVING,"Y1:(344,272,14,27,378,0.498)|Y2:(443,306,13,23,299,0.389)","Z:1.032,P:-11.6,-8.5,R:0.000,C:0.88,TC:1.00,TF:14,WM:False,FPS:False,CB:5,FSME:0,FSMS:15,MDF:20,MSDF:12"
621,24.821,True,3,0,3,YOLO_MOVING,"Y1:(343,272,15,29,435,0.571)|Y2:(307,507,14,13,182,0.440)|Y3:(445,307,13,24,312,0.379)","Z:1.030,P:-11.0,-8.2,R:-0.000,C:0.91,TC:1.00,TF:15,WM:True,FPS:False,CB:5,FSME:0,FSMS:16,MDF:20,MSDF:12"
622,24.861,True,3,0,3,YOLO_MOVING,"Y1:(342,272,16,33,528,0.695)|Y2:(305,514,14,14,196,0.370)|Y3:(448,307,13,26,338,0.351)","Z:1.030,P:-11.3,-8.0,R:0.000,C:0.87,TC:1.00,TF:16,WM:True,FPS:False,CB:5,FSME:0,FSMS:17,MDF:20,MSDF:12"
623,24.901,True,3,0,3,YOLO_MOVING,"Y1:(342,270,16,34,544,0.665)|Y2:(303,520,15,13,195,0.539)|Y3:(450,307,13,26,338,0.479)","Z:1.030,P:-10.3,-9.7,R:-0.000,C:0.84,TC:1.00,TF:17,WM:False,FPS:False,CB:5,FSME:0,FSMS:18,MDF:20,MSDF:12"
624,24.941,True,4,0,4,YOLO_MOVING,"Y1:(342,270,17,34,578,0.615)|Y2:(452,308,14,27,378,0.564)|Y3:(302,526,15,14,210,0.500)|Y4:(277,453,12,13,156,0.362)","Z:1.025,P:-8.9,-7.6,R:0.000,C:0.89,TC:1.00,TF:18,WM:False,FPS:False,CB:5,FSME:0,FSMS:19,MDF:20,MSDF:12"
625,24.981,True,4,0,4,YOLO_MOVING,"Y1:(340,270,18,36,648,0.634)|Y2:(454,309,14,27,378,0.440)|Y3:(299,531,17,16,272,0.439)|Y4:(274,459,12,14,168,0.302)","Z:1.025,P:-9.4,-6.2,R:-0.001,C:0.86,TC:1.00,TF:19,WM:False,FPS:False,CB:5,FSME:0,FSMS:20,MDF:20,MSDF:12"
626,25.021,True,3,0,3,YOLO_MOVING,"Y1:(340,265,19,41,779,0.398)|Y2:(297,536,18,16,288,0.392)|Y3:(457,308,14,29,406,0.358)","Z:1.026,P:-8.6,-9.2,R:-0.000,C:0.87,TC:1.00,TF:20,WM:False,FPS:False,CB:5,FSME:0,FSMS:21,MDF:20,MSDF:12"
627,25.061,True,2,0,2,YOLO_MOVING,"Y1:(296,542,19,15,285,0.441)|Y2:(339,263,19,43,817,0.401)","Z:1.023,P:-8.7,-5.7,R:0.001,C:0.88,TC:1.00,TF:21,WM:False,FPS:False,CB:5,FSME:0,FSMS:22,MDF:20,MSDF:12"
628,25.101,True,3,0,3,YOLO_MOVING,"Y1:(461,308,15,30,450,0.508)|Y2:(294,548,18,16,288,0.435)|Y3:(340,268,18,38,684,0.399)","Z:1.021,P:-7.5,-7.6,R:-0.000,C:0.82,TC:1.00,TF:22,WM:False,FPS:False,CB:5,FSME:0,FSMS:23,MDF:20,MSDF:12"
629,25.141,True,1,0,1,YOLO_MOVING,"Y1:(464,309,14,30,420,0.368)","Z:1.020,P:-7.5,-6.0,R:0.000,C:0.88,TC:1.00,TF:23,WM:False,FPS:False,CB:5,FSME:0,FSMS:24,MDF:20,MSDF:12"
630,25.181,True,4,0,4,YOLO_MOVING,"Y1:(466,308,15,30,450,0.523)|Y2:(339,266,19,39,741,0.334)|Y3:(471,230,11,16,176,0.323)|Y4:(291,554,19,17,323,0.315)","Z:1.017,P:-5.9,-6.0,R:-0.000,C:0.72,TC:1.00,TF:24,WM:False,FPS:False,CB:5,FSME:0,FSMS:25,MDF:20,MSDF:12"
631,25.221,True,3,0,3,YOLO_MOVING,"Y1:(338,268,20,36,720,0.570)|Y2:(468,308,15,30,450,0.423)|Y3:(472,228,11,17,187,0.300)","Z:1.021,P:-7.9,-6.0,R:0.000,C:0.92,TC:1.00,TF:25,WM:False,FPS:False,CB:5,FSME:0,FSMS:26,MDF:20,MSDF:12"
632,25.261,True,2,0,2,YOLO_MOVING,"Y1:(339,267,20,36,720,0.520)|Y2:(261,481,14,13,182,0.414)","Z:1.023,P:-7.3,-5.9,R:-0.000,C:0.79,TC:1.00,TF:26,WM:False,FPS:False,CB:5,FSME:0,FSMS:27,MDF:20,MSDF:12"
633,25.301,True,2,0,2,YOLO_MOVING,"Y1:(338,266,21,38,798,0.500)|Y2:(258,484,15,14,210,0.342)","Z:1.022,P:-7.4,-7.9,R:-0.001,C:0.71,TC:1.00,TF:27,WM:False,FPS:False,CB:5,FSME:0,FSMS:28,MDF:20,MSDF:12"
634,25.341,True,3,0,3,YOLO_MOVING,"Y1:(475,309,16,34,544,0.437)|Y2:(337,262,21,39,819,0.433)|Y3:(255,489,15,14,210,0.342)","Z:1.024,P:-9.2,-8.7,R:-0.000,C:0.50,TC:0.80,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:29,MDF:20,MSDF:12"
635,25.381,True,3,0,3,YOLO_MOVING,"Y1:(336,254,23,49,1127,0.343)|Y2:(479,309,16,34,544,0.321)|Y3:(254,491,15,15,225,0.320)","Z:1.017,P:-4.3,-6.1,R:-0.001,C:0.54,TC:0.60,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:30,MDF:20,MSDF:12"
636,25.421,True,3,0,3,YOLO_MOVING,"Y1:(337,260,21,42,882,0.723)|Y2:(250,496,16,19,304,0.476)|Y3:(479,309,16,33,528,0.356)","Z:1.023,P:-8.8,-7.6,R:-0.001,C:0.83,TC:0.60,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:31,MDF:20,MSDF:12"
637,25.461,True,2,0,2,YOLO_MOVING,"Y1:(336,260,22,42,924,0.378)|Y2:(249,501,17,17,289,0.372)","Z:1.025,P:-8.1,-7.5,R:0.000,C:0.67,TC:0.60,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:32,MDF:20,MSDF:12"
638,25.501,True,1,0,1,YOLO_MOVING,"Y1:(337,260,23,41,943,0.722)","Z:1.018,P:-6.0,-4.5,R:0.000,C:0.60,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:33,MDF:20,MSDF:12"
639,25.540,True,4,0,4,YOLO_MOVING,"Y1:(336,259,22,42,924,0.724)|Y2:(490,310,17,31,527,0.512)|Y3:(246,508,18,18,324,0.441)|Y4:(202,475,25,19,475,0.396)","Z:1.018,P:-7.1,-4.3,R:0.003,C:0.72,TC:0.60,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:34,MDF:20,MSDF:12"
640,25.581,True,4,0,4,YOLO_MOVING,"Y1:(337,260,23,41,943,0.693)|Y2:(492,311,17,32,544,0.553)|Y3:(242,513,18,16,288,0.433)|Y4:(195,478,27,20,540,0.329)","Z:1.021,P:-7.0,-6.7,R:-0.002,C:0.58,TC:0.60,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:35,MDF:20,MSDF:12"
641,25.620,True,3,0,3,YOLO_MOVING,"Y1:(336,258,25,43,1075,0.730)|Y2:(242,517,18,18,324,0.513)|Y3:(495,310,18,33,594,0.450)","Z:1.020,P:-7.2,-7.3,R:0.001,C:0.78,TC:0.60,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:36,MDF:20,MSDF:12"
642,25.660,True,5,0,5,YOLO_MOVING,"Y1:(334,258,26,44,1144,0.606)|Y2:(497,310,19,35,665,0.540)|Y3:(239,521,18,18,324,0.508)|Y4:(190,485,27,22,594,0.351)|Y5:(328,230,32,71,2272,0.301)","Z:1.020,P:-7.1,-5.5,R:0.000,C:0.89,TC:0.60,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:37,MDF:20,MSDF:12"
643,25.700,True,4,0,4,YOLO_MOVING,"Y1:(326,227,35,80,2800,0.736)|Y2:(527,562,31,12,372,0.580)|Y3:(236,525,18,20,360,0.424)|Y4:(501,310,18,36,648,0.397)","Z:1.020,P:-6.3,-6.5,R:-0.001,C:0.85,TC:0.80,TF:3,WM:False,FPS:False,CB:5,FSME:0,FSMS:38,MDF:20,MSDF:12"
644,25.740,True,4,0,4,YOLO_MOVING,"Y1:(325,226,36,81,2916,0.756)|Y2:(502,310,20,37,740,0.450)|Y3:(185,491,29,25,725,0.400)|Y4:(235,528,18,19,342,0.371)","Z:1.017,P:-6.2,-4.9,R:0.001,C:0.85,TC:0.80,TF:4,WM:False,FPS:False,CB:5,FSME:0,FSMS:39,MDF:20,MSDF:12"
645,25.780,True,2,0,2,YOLO_MOVING,"Y1:(327,226,34,81,2754,0.780)|Y2:(506,311,19,38,722,0.466)","Z:1.019,P:-6.8,-5.2,R:-0.001,C:0.79,TC:1.00,TF:5,WM:False,FPS:False,CB:5,FSME:0,FSMS:40,MDF:20,MSDF:12"
646,25.820,True,2,0,2,YOLO_MOVING,"Y1:(325,224,37,83,3071,0.791)|Y2:(509,309,21,38,798,0.562)","Z:1.016,P:-4.5,-7.0,R:-0.001,C:0.72,TC:1.00,TF:6,WM:False,FPS:False,CB:5,FSME:0,FSMS:41,MDF:20,MSDF:12"
647,25.860,True,2,0,2,YOLO_MOVING,"Y1:(325,225,37,81,2997,0.748)|Y2:(512,310,21,40,840,0.643)","Z:1.022,P:-6.9,-7.1,R:-0.002,C:0.56,TC:0.80,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:42,MDF:20,MSDF:12"
648,25.900,True,3,0,3,YOLO_MOVING,"Y1:(324,226,38,82,3116,0.787)|Y2:(516,311,20,43,860,0.728)|Y3:(169,507,32,26,832,0.322)","Z:1.016,P:-6.5,-3.8,R:-0.001,C:0.58,TC:0.60,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:43,MDF:20,MSDF:12"
649,25.940,True,4,0,4,YOLO_MOVING,"Y1:(323,223,39,82,3198,0.791)|Y2:(520,312,20,42,840,0.750)|Y3:(168,513,31,29,899,0.330)|Y4:(594,548,10,15,150,0.300)","Z:1.021,P:-5.6,-7.2,R:-0.002,C:0.46,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:44,MDF:20,MSDF:12"
650,25.980,False,1,0,1,YOLO_STABLE,"Y1:(325,221,38,87,3306,0.820)","Z:1.012,P:-4.1,-5.3,R:0.001,C:0.23,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:1,FSMS:0,MDF:20,MSDF:12"
651,26.020,False,1,0,1,YOLO_STABLE,"Y1:(324,219,38,86,3268,0.804)","Z:1.020,P:-7.3,-7.3,R:-0.002,C:0.59,TC:0.00,TF:1,WM:False,FPS:False,CB:5,FSME:2,FSMS:0,MDF:20,MSDF:12"
652,26.060,True,1,0,0,MOVEMENT_START_DELAY_1/5,None,"Z:1.014,P:-4.8,-5.2,R:0.000,C:0.91,TC:0.00,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:1,MDF:20,MSDF:13"
653,26.100,True,1,0,0,MOVEMENT_START_DELAY_2/5,None,"Z:1.011,P:-3.8,-3.2,R:-0.002,C:0.77,TC:0.20,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:2,MDF:20,MSDF:14"
654,26.140,False,1,0,1,YOLO_STABLE,"Y1:(318,214,44,94,4136,0.819)","Z:1.013,P:-3.8,-2.7,R:0.000,C:0.53,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:1,FSMS:0,MDF:20,MSDF:14"
655,26.180,True,1,0,0,MOVEMENT_START_DELAY_1/5,None,"Z:1.014,P:-3.3,-4.2,R:-0.001,C:0.70,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:1,MDF:20,MSDF:15"
656,26.221,False,1,0,1,YOLO_STABLE,"Y1:(317,212,46,97,4462,0.799)","Z:0.362,P:620.3,219.8,R:-1.582,C:0.10,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:1,FSMS:0,MDF:20,MSDF:15"
657,26.260,True,1,0,0,MOVEMENT_START_DELAY_1/5,None,"Z:1.016,P:-4.6,-6.7,R:-0.001,C:0.72,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:1,MDF:20,MSDF:16"
658,26.300,True,1,0,0,MOVEMENT_START_DELAY_2/5,None,"Z:1.007,P:-3.1,-3.1,R:0.001,C:0.65,TC:0.40,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:2,MDF:20,MSDF:17"
659,26.340,True,2,0,0,MOVEMENT_START_DELAY_3/5,None,"Z:1.007,P:-2.8,-0.5,R:0.001,C:0.85,TC:0.60,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:3,MDF:20,MSDF:18"
660,26.380,True,1,0,0,MOVEMENT_START_DELAY_4/5,None,"Z:1.007,P:-1.6,-1.6,R:-0.001,C:0.74,TC:0.60,TF:3,WM:False,FPS:False,CB:5,FSME:0,FSMS:4,MDF:20,MSDF:19"
661,26.420,True,1,0,1,YOLO_MOVING,"Y1:(317,205,48,104,4992,0.800)","Z:1.005,P:-1.1,-0.4,R:0.002,C:0.82,TC:0.60,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:5,MDF:20,MSDF:19"
662,26.460,True,2,0,2,YOLO_MOVING,"Y1:(317,206,48,105,5040,0.802)|Y2:(546,315,23,43,989,0.338)","Z:1.005,P:-2.0,-0.1,R:-0.001,C:0.82,TC:0.80,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:6,MDF:20,MSDF:19"
663,26.500,True,3,0,3,YOLO_MOVING,"Y1:(317,207,47,101,4747,0.811)|Y2:(547,314,23,41,943,0.617)|Y3:(134,550,36,24,864,0.472)","Z:1.005,P:-2.0,-3.1,R:0.001,C:0.83,TC:0.80,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:7,MDF:20,MSDF:19"
664,26.540,True,2,0,2,YOLO_MOVING,"Y1:(316,205,48,102,4896,0.803)|Y2:(134,549,36,25,900,0.370)","Z:1.004,P:-1.7,-2.7,R:-0.000,C:0.91,TC:0.80,TF:3,WM:False,FPS:False,CB:5,FSME:0,FSMS:8,MDF:20,MSDF:19"
665,26.580,True,2,0,2,YOLO_MOVING,"Y1:(314,205,50,101,5050,0.801)|Y2:(131,550,37,25,925,0.426)","Z:1.003,P:-1.1,-1.1,R:0.000,C:0.96,TC:0.80,TF:4,WM:False,FPS:False,CB:5,FSME:0,FSMS:9,MDF:20,MSDF:19"
666,26.620,True,2,0,2,YOLO_MOVING,"Y1:(316,207,48,99,4752,0.816)|Y2:(130,551,37,24,888,0.562)","Z:1.005,P:-0.8,-0.3,R:-0.001,C:0.89,TC:0.80,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:10,MDF:20,MSDF:19"
667,26.660,True,2,0,2,YOLO_MOVING,"Y1:(318,208,47,100,4700,0.815)|Y2:(131,554,34,21,714,0.395)","Z:1.004,P:-0.8,0.3,R:0.001,C:0.91,TC:0.60,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:11,MDF:20,MSDF:19"
668,26.700,True,2,0,2,YOLO_MOVING,"Y1:(318,209,45,100,4500,0.801)|Y2:(134,554,26,21,546,0.303)","Z:1.003,P:-1.7,-0.2,R:0.000,C:0.91,TC:0.60,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:12,MDF:20,MSDF:19"
669,26.740,True,1,0,1,YOLO_MOVING,"Y1:(319,209,45,97,4365,0.795)","Z:1.004,P:-1.2,-2.9,R:-0.000,C:0.97,TC:0.60,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:13,MDF:20,MSDF:19"
670,26.780,True,1,0,1,YOLO_MOVING,"Y1:(322,210,42,96,4032,0.787)","Z:1.001,P:-0.8,-1.6,R:0.000,C:0.86,TC:0.60,TF:3,WM:False,FPS:False,CB:5,FSME:0,FSMS:14,MDF:20,MSDF:19"
671,26.820,True,1,0,1,YOLO_MOVING,"Y1:(322,209,41,97,3977,0.771)","Z:1.004,P:-1.2,-1.0,R:-0.001,C:0.89,TC:0.80,TF:4,WM:False,FPS:False,CB:5,FSME:0,FSMS:15,MDF:20,MSDF:19"
672,26.861,True,1,0,1,YOLO_MOVING,"Y1:(317,202,46,110,5060,0.823)","Z:1.001,P:-0.7,-0.3,R:0.000,C:0.09,TC:0.80,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:16,MDF:20,MSDF:19"
673,26.899,True,2,0,2,YOLO_MOVING,"Y1:(317,203,46,110,5060,0.823)|Y2:(557,315,24,50,1200,0.415)","Z:1.004,P:-1.4,0.8,R:0.000,C:0.77,TC:0.80,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:17,MDF:20,MSDF:19"
674,26.939,True,1,0,1,YOLO_MOVING,"Y1:(318,203,45,111,4995,0.825)","Z:1.006,P:-2.1,-2.0,R:0.001,C:0.84,TC:0.80,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:18,MDF:20,MSDF:19"
675,26.979,True,1,0,1,YOLO_MOVING,"Y1:(316,199,47,115,5405,0.812)","Z:1.006,P:-3.0,-3.7,R:-0.000,C:0.92,TC:0.80,TF:3,WM:False,FPS:False,CB:5,FSME:0,FSMS:19,MDF:20,MSDF:19"
676,27.019,True,1,0,1,YOLO_MOVING,"Y1:(315,197,49,116,5684,0.795)","Z:1.004,P:-1.8,-3.0,R:-0.000,C:0.83,TC:0.80,TF:4,WM:False,FPS:False,CB:5,FSME:0,FSMS:20,MDF:20,MSDF:19"
677,27.059,True,1,0,1,YOLO_MOVING,"Y1:(313,197,50,117,5850,0.786)","Z:1.002,P:-0.7,-0.7,R:-0.000,C:0.89,TC:0.80,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:21,MDF:20,MSDF:19"
678,27.099,True,1,0,1,YOLO_MOVING,"Y1:(315,198,49,120,5880,0.785)","Z:1.004,P:-0.5,0.9,R:0.000,C:0.74,TC:0.60,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:22,MDF:20,MSDF:19"
679,27.139,True,1,0,1,YOLO_MOVING,"Y1:(314,200,50,115,5750,0.793)","Z:1.004,P:-0.6,0.3,R:0.000,C:0.88,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:23,MDF:20,MSDF:19"
680,27.179,True,1,0,1,YOLO_MOVING,"Y1:(314,199,51,116,5916,0.811)","Z:1.004,P:-1.6,-0.5,R:0.001,C:0.81,TC:0.40,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:24,MDF:20,MSDF:19"
681,27.219,True,1,0,1,YOLO_MOVING,"Y1:(313,195,52,121,6292,0.796)","Z:1.005,P:-1.7,-4.0,R:-0.001,C:0.84,TC:0.40,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:25,MDF:20,MSDF:19"
682,27.259,True,1,0,1,YOLO_MOVING,"Y1:(312,193,53,122,6466,0.789)","Z:1.002,P:-1.5,-2.2,R:-0.000,C:0.87,TC:0.60,TF:3,WM:False,FPS:False,CB:5,FSME:0,FSMS:26,MDF:20,MSDF:19"
683,27.299,True,1,0,1,YOLO_MOVING,"Y1:(311,193,55,120,6600,0.807)","Z:1.003,P:-0.9,-1.3,R:-0.001,C:0.84,TC:0.80,TF:4,WM:False,FPS:False,CB:5,FSME:0,FSMS:27,MDF:20,MSDF:19"
684,27.339,True,1,0,1,YOLO_MOVING,"Y1:(313,195,53,116,6148,0.822)","Z:1.006,P:-0.9,-0.5,R:-0.000,C:0.75,TC:0.80,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:28,MDF:20,MSDF:19"
685,27.379,True,1,0,1,YOLO_MOVING,"Y1:(314,196,54,117,6318,0.812)","Z:1.007,P:-1.9,0.4,R:0.000,C:0.82,TC:0.80,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:29,MDF:20,MSDF:19"
686,27.419,True,1,0,1,YOLO_MOVING,"Y1:(313,195,54,119,6426,0.806)","Z:1.006,P:-1.7,-1.8,R:-0.000,C:0.74,TC:0.80,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:30,MDF:20,MSDF:19"
687,27.459,True,1,0,1,YOLO_MOVING,"Y1:(315,193,51,122,6222,0.798)","Z:1.004,P:-1.8,-2.6,R:0.001,C:0.82,TC:0.80,TF:3,WM:False,FPS:False,CB:5,FSME:0,FSMS:31,MDF:20,MSDF:19"
688,27.499,True,1,0,1,YOLO_MOVING,"Y1:(317,192,47,128,6016,0.780)","Z:1.006,P:-2.3,-4.1,R:-0.001,C:0.82,TC:0.80,TF:4,WM:False,FPS:False,CB:5,FSME:0,FSMS:32,MDF:20,MSDF:19"
689,27.539,True,2,0,2,YOLO_MOVING,"Y1:(315,191,48,136,6528,0.773)|Y2:(571,313,26,47,1222,0.322)","Z:1.002,P:-0.5,-1.0,R:0.000,C:0.94,TC:0.80,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:33,MDF:20,MSDF:19"
690,27.579,True,1,0,1,YOLO_MOVING,"Y1:(316,192,50,122,6100,0.808)","Z:1.003,P:-0.6,-0.1,R:-0.001,C:0.85,TC:0.60,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:34,MDF:20,MSDF:19"
691,27.619,True,1,0,1,YOLO_MOVING,"Y1:(318,193,50,122,6100,0.809)","Z:1.004,P:-1.2,1.5,R:0.002,C:0.84,TC:0.60,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:35,MDF:20,MSDF:19"
692,27.659,True,1,0,1,YOLO_MOVING,"Y1:(315,193,56,120,6720,0.800)","Z:1.002,P:-0.9,-0.7,R:0.000,C:0.16,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:36,MDF:20,MSDF:19"
693,27.699,False,1,0,1,YOLO_STABLE,"Y1:(314,194,55,121,6655,0.808)","Z:1.005,P:-2.1,-0.5,R:0.001,C:0.70,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:1,FSMS:0,MDF:20,MSDF:19"
694,27.739,True,1,0,0,MOVEMENT_START_DELAY_1/5,None,"Z:1.006,P:-2.3,-5.3,R:-0.002,C:0.77,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:1,MDF:20,MSDF:20"
695,27.779,True,1,0,0,MOVEMENT_START_DELAY_2/5,None,"Z:1.005,P:-1.5,-2.2,R:0.000,C:0.89,TC:0.40,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:2,MDF:20,MSDF:21"
696,27.819,True,1,0,0,MOVEMENT_START_DELAY_3/5,None,"Z:1.005,P:-1.9,-0.2,R:-0.000,C:0.91,TC:0.60,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:3,MDF:20,MSDF:22"
697,27.859,True,1,0,0,MOVEMENT_START_DELAY_4/5,None,"Z:1.006,P:-1.3,-0.7,R:-0.000,C:0.82,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:4,MDF:20,MSDF:23"
698,27.899,True,1,0,1,YOLO_MOVING,"Y1:(316,190,52,136,7072,0.756)","Z:1.005,P:-1.4,-1.1,R:0.000,C:0.78,TC:0.60,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:5,MDF:20,MSDF:23"
699,27.939,True,1,0,1,YOLO_MOVING,"Y1:(314,189,55,156,8580,0.753)","Z:1.005,P:-2.5,-2.3,R:0.000,C:0.83,TC:0.80,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:6,MDF:20,MSDF:23"
700,27.979,True,1,0,1,YOLO_MOVING,"Y1:(314,187,54,124,6696,0.851)","Z:1.003,P:-0.9,-1.7,R:-0.001,C:0.41,TC:0.60,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:7,MDF:20,MSDF:23"
701,28.019,True,1,0,1,YOLO_MOVING,"Y1:(315,187,53,123,6519,0.852)","Z:1.002,P:-0.3,-1.2,R:-0.000,C:0.88,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:8,MDF:20,MSDF:23"
702,28.059,True,1,0,1,YOLO_MOVING,"Y1:(315,188,53,124,6572,0.852)","Z:1.002,P:-0.6,0.5,R:0.000,C:0.94,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:9,MDF:20,MSDF:23"
703,28.099,True,1,0,1,YOLO_MOVING,"Y1:(314,188,52,125,6500,0.846)","Z:1.005,P:-1.6,-1.5,R:-0.001,C:0.85,TC:0.40,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:10,MDF:20,MSDF:23"
704,28.139,True,1,0,1,YOLO_MOVING,"Y1:(314,186,54,125,6750,0.842)","Z:1.002,P:0.1,-2.2,R:-0.000,C:0.72,TC:0.40,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:11,MDF:20,MSDF:23"
705,28.179,True,1,0,1,YOLO_MOVING,"Y1:(312,187,53,126,6678,0.834)","Z:1.004,P:-2.6,-0.4,R:0.000,C:0.69,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:12,MDF:20,MSDF:23"
706,28.218,True,1,0,1,YOLO_MOVING,"Y1:(313,186,54,127,6858,0.837)","Z:1.001,P:1.6,-0.9,R:0.000,C:0.71,TC:0.60,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:13,MDF:20,MSDF:23"
707,28.258,True,1,0,1,YOLO_MOVING,"Y1:(314,188,52,127,6604,0.847)","Z:1.001,P:-0.8,1.6,R:0.000,C:0.93,TC:0.80,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:14,MDF:20,MSDF:23"
708,28.298,True,1,0,1,YOLO_MOVING,"Y1:(313,187,54,128,6912,0.850)","Z:1.015,P:-4.7,-4.1,R:0.001,C:0.92,TC:0.80,TF:3,WM:False,FPS:False,CB:5,FSME:0,FSMS:15,MDF:20,MSDF:23"
709,28.338,True,1,0,1,YOLO_MOVING,"Y1:(312,184,56,130,7280,0.852)","Z:1.016,P:-5.8,-4.8,R:0.001,C:0.73,TC:0.80,TF:4,WM:False,FPS:False,CB:5,FSME:0,FSMS:16,MDF:20,MSDF:23"
710,28.378,True,1,0,1,YOLO_MOVING,"Y1:(310,183,59,134,7906,0.848)","Z:1.012,P:-3.8,-5.2,R:-0.002,C:0.18,TC:0.80,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:17,MDF:20,MSDF:23"
711,28.418,True,1,0,1,YOLO_MOVING,"Y1:(310,181,59,134,7906,0.835)","Z:1.007,P:-3.2,-2.5,R:-0.000,C:0.86,TC:0.80,TF:1,WM:True,FPS:False,CB:5,FSME:0,FSMS:18,MDF:20,MSDF:23"
712,28.458,True,1,0,1,YOLO_MOVING,"Y1:(310,179,61,140,8540,0.846)","Z:1.013,P:-3.1,-4.8,R:-0.001,C:0.78,TC:0.80,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:19,MDF:20,MSDF:23"
713,28.498,True,1,0,1,YOLO_MOVING,"Y1:(310,180,60,147,8820,0.849)","Z:1.013,P:-4.5,-2.3,R:0.000,C:0.88,TC:0.80,TF:3,WM:False,FPS:False,CB:5,FSME:0,FSMS:20,MDF:20,MSDF:23"
714,28.538,True,1,0,1,YOLO_MOVING,"Y1:(310,180,61,141,8601,0.858)","Z:1.014,P:-4.3,-2.3,R:0.000,C:0.92,TC:0.80,TF:4,WM:False,FPS:False,CB:5,FSME:0,FSMS:21,MDF:20,MSDF:23"
715,28.578,True,1,0,1,YOLO_MOVING,"Y1:(310,178,60,144,8640,0.853)","Z:1.016,P:-5.4,-5.0,R:0.000,C:0.79,TC:1.00,TF:5,WM:False,FPS:False,CB:5,FSME:0,FSMS:22,MDF:20,MSDF:23"
716,28.618,True,1,0,1,YOLO_MOVING,"Y1:(308,174,63,152,9576,0.843)","Z:1.013,P:-4.8,-5.0,R:0.001,C:0.90,TC:1.00,TF:6,WM:False,FPS:False,CB:5,FSME:0,FSMS:23,MDF:20,MSDF:23"
717,28.658,True,1,0,1,YOLO_MOVING,"Y1:(307,173,61,153,9333,0.844)","Z:1.012,P:-4.9,-4.3,R:0.000,C:0.82,TC:1.00,TF:7,WM:False,FPS:False,CB:5,FSME:0,FSMS:24,MDF:20,MSDF:23"
718,28.699,True,1,0,1,YOLO_MOVING,"Y1:(307,171,61,145,8845,0.843)","Z:1.011,P:-2.3,-4.0,R:-0.001,C:0.79,TC:1.00,TF:8,WM:False,FPS:False,CB:5,FSME:0,FSMS:25,MDF:20,MSDF:23"
719,28.738,True,1,0,1,YOLO_MOVING,"Y1:(306,172,63,149,9387,0.854)","Z:1.005,P:-2.0,1.1,R:-0.000,C:0.87,TC:1.00,TF:9,WM:False,FPS:False,CB:5,FSME:0,FSMS:26,MDF:20,MSDF:23"
720,28.778,True,1,0,1,YOLO_MOVING,"Y1:(306,171,65,151,9815,0.859)","Z:1.016,P:-4.5,-4.5,R:0.000,C:0.91,TC:1.00,TF:10,WM:True,FPS:False,CB:5,FSME:0,FSMS:27,MDF:20,MSDF:23"
721,28.818,True,1,0,1,YOLO_MOVING,"Y1:(306,169,64,150,9600,0.850)","Z:1.012,P:-4.6,-3.4,R:0.002,C:0.70,TC:1.00,TF:11,WM:False,FPS:False,CB:5,FSME:0,FSMS:28,MDF:20,MSDF:23"
722,28.858,True,1,0,1,YOLO_MOVING,"Y1:(304,167,66,152,10032,0.851)","Z:1.013,P:-5.3,-4.9,R:0.000,C:0.88,TC:1.00,TF:12,WM:False,FPS:False,CB:5,FSME:0,FSMS:29,MDF:20,MSDF:23"
723,28.898,True,1,0,1,YOLO_MOVING,"Y1:(305,164,65,154,10010,0.845)","Z:1.013,P:-4.2,-4.4,R:0.000,C:0.93,TC:1.00,TF:13,WM:False,FPS:False,CB:5,FSME:0,FSMS:30,MDF:20,MSDF:23"
724,28.938,True,1,0,1,YOLO_MOVING,"Y1:(303,164,68,160,10880,0.840)","Z:1.013,P:-4.1,-2.8,R:-0.000,C:0.93,TC:1.00,TF:14,WM:False,FPS:False,CB:5,FSME:0,FSMS:31,MDF:20,MSDF:23"
725,28.978,True,1,0,1,YOLO_MOVING,"Y1:(304,162,69,166,11454,0.853)","Z:1.012,P:-3.2,-2.1,R:0.001,C:0.88,TC:1.00,TF:15,WM:False,FPS:False,CB:5,FSME:0,FSMS:32,MDF:20,MSDF:23"
726,29.018,True,2,0,2,YOLO_MOVING,"Y1:(303,160,70,172,12040,0.853)|Y2:(670,111,27,26,702,0.311)","Z:1.014,P:-4.7,-2.1,R:-0.001,C:0.48,TC:0.80,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:33,MDF:20,MSDF:23"
727,29.058,True,2,0,2,YOLO_MOVING,"Y1:(305,160,69,165,11385,0.866)|Y2:(672,111,29,27,783,0.392)","Z:1.005,P:-1.6,-1.2,R:0.001,C:0.62,TC:0.60,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:34,MDF:20,MSDF:23"
728,29.098,True,1,0,1,YOLO_MOVING,"Y1:(305,155,65,168,10920,0.856)","Z:1.010,P:-4.0,-7.4,R:-0.003,C:0.62,TC:0.60,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:35,MDF:20,MSDF:23"
729,29.138,True,1,0,1,YOLO_MOVING,"Y1:(302,151,69,173,11937,0.866)","Z:1.015,P:-4.8,-4.3,R:0.000,C:0.57,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:36,MDF:20,MSDF:23"
730,29.178,True,1,0,1,YOLO_MOVING,"Y1:(305,154,68,175,11900,0.847)","Z:1.000,P:1.2,0.9,R:0.000,C:0.57,TC:0.40,TF:1,WM:True,FPS:False,CB:5,FSME:0,FSMS:37,MDF:20,MSDF:23"
731,29.218,True,1,0,1,YOLO_MOVING,"Y1:(303,156,68,194,13192,0.830)","Z:1.001,P:-1.0,3.9,R:0.001,C:0.88,TC:0.60,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:38,MDF:20,MSDF:23"
732,29.258,True,1,0,1,YOLO_MOVING,"Y1:(307,156,68,177,12036,0.855)","Z:1.001,P:3.2,-1.2,R:-0.000,C:0.89,TC:0.80,TF:3,WM:False,FPS:False,CB:5,FSME:0,FSMS:39,MDF:20,MSDF:23"
733,29.298,True,1,0,1,YOLO_MOVING,"Y1:(303,155,68,196,13328,0.826)","Z:0.999,P:-3.5,-0.4,R:0.001,C:0.75,TC:0.80,TF:4,WM:False,FPS:False,CB:5,FSME:0,FSMS:40,MDF:20,MSDF:23"
734,29.338,True,2,0,2,YOLO_MOVING,"Y1:(303,151,69,194,13386,0.827)|Y2:(682,100,21,28,588,0.513)","Z:1.002,P:-0.0,-3.8,R:-0.001,C:0.86,TC:1.00,TF:5,WM:False,FPS:False,CB:5,FSME:0,FSMS:41,MDF:20,MSDF:23"
735,29.378,True,2,0,2,YOLO_MOVING,"Y1:(302,149,71,185,13135,0.845)|Y2:(681,99,22,28,616,0.475)","Z:0.996,P:1.8,-1.5,R:-0.000,C:0.72,TC:1.00,TF:6,WM:False,FPS:False,CB:5,FSME:0,FSMS:42,MDF:20,MSDF:23"
736,29.418,True,2,0,2,YOLO_MOVING,"Y1:(304,153,69,179,12351,0.840)|Y2:(683,102,20,27,540,0.406)","Z:1.000,P:1.2,2.6,R:-0.001,C:0.80,TC:1.00,TF:7,WM:False,FPS:False,CB:5,FSME:0,FSMS:43,MDF:20,MSDF:23"
737,29.458,True,1,0,1,YOLO_MOVING,"Y1:(304,156,70,184,12880,0.848)","Z:1.000,P:0.9,3.0,R:0.001,C:0.86,TC:1.00,TF:8,WM:False,FPS:False,CB:5,FSME:0,FSMS:44,MDF:20,MSDF:23"
738,29.498,True,1,0,1,YOLO_MOVING,"Y1:(302,156,73,182,13286,0.864)","Z:1.000,P:1.2,1.6,R:-0.000,C:0.91,TC:1.00,TF:9,WM:False,FPS:False,CB:5,FSME:0,FSMS:45,MDF:20,MSDF:23"
739,29.537,True,2,0,2,YOLO_MOVING,"Y1:(300,156,75,182,13650,0.866)|Y2:(683,108,20,28,560,0.337)","Z:1.000,P:-0.5,0.2,R:0.001,C:0.96,TC:0.80,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:46,MDF:20,MSDF:23"
740,29.577,True,2,0,2,YOLO_MOVING,"Y1:(298,154,74,177,13098,0.879)|Y2:(683,104,20,27,540,0.389)","Z:1.000,P:-1.1,-2.5,R:-0.001,C:0.76,TC:0.80,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:47,MDF:20,MSDF:23"
741,29.617,True,2,0,2,YOLO_MOVING,"Y1:(297,151,72,178,12816,0.877)|Y2:(682,101,20,28,560,0.535)","Z:1.000,P:0.3,-1.8,R:-0.000,C:0.93,TC:0.80,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:48,MDF:20,MSDF:23"
742,29.657,True,2,0,2,YOLO_MOVING,"Y1:(297,153,71,173,12283,0.881)|Y2:(683,102,20,27,540,0.520)","Z:1.001,P:-0.7,0.3,R:0.000,C:0.85,TC:0.60,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:49,MDF:20,MSDF:23"
743,29.697,True,2,0,2,YOLO_MOVING,"Y1:(297,153,71,178,12638,0.882)|Y2:(684,103,19,29,551,0.353)","Z:1.001,P:-0.1,1.2,R:0.000,C:0.87,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:50,MDF:20,MSDF:23"
744,29.737,True,1,0,1,YOLO_MOVING,"Y1:(299,156,72,190,13680,0.852)","Z:1.005,P:0.1,-0.0,R:0.000,C:0.75,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:51,MDF:20,MSDF:23"
745,29.777,True,1,0,1,YOLO_MOVING,"Y1:(299,156,73,211,15403,0.830)","Z:1.002,P:-0.7,1.7,R:-0.000,C:0.87,TC:0.40,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:52,MDF:20,MSDF:23"
746,29.817,True,1,0,1,YOLO_MOVING,"Y1:(297,154,75,185,13875,0.878)","Z:1.002,P:-1.8,-2.1,R:0.001,C:0.87,TC:0.40,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:53,MDF:20,MSDF:23"
747,29.858,True,1,0,1,YOLO_MOVING,"Y1:(296,151,75,174,13050,0.879)","Z:1.001,P:-1.0,-2.8,R:0.001,C:0.72,TC:0.60,TF:3,WM:False,FPS:False,CB:5,FSME:0,FSMS:54,MDF:20,MSDF:23"
748,29.897,True,1,0,1,YOLO_MOVING,"Y1:(296,152,73,175,12775,0.877)","Z:0.999,P:0.2,0.2,R:0.000,C:0.78,TC:0.60,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:55,MDF:20,MSDF:23"
749,29.937,True,1,0,1,YOLO_MOVING,"Y1:(297,152,72,173,12456,0.881)","Z:0.994,P:2.7,1.1,R:-0.001,C:0.62,TC:0.60,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:56,MDF:20,MSDF:23"
750,29.978,True,1,0,1,YOLO_MOVING,"Y1:(297,155,73,183,13359,0.880)","Z:0.996,P:2.5,3.2,R:0.000,C:0.55,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:57,MDF:20,MSDF:23"
751,30.017,True,1,0,1,YOLO_MOVING,"Y1:(298,157,73,182,13286,0.885)","Z:0.997,P:1.9,1.5,R:-0.000,C:0.81,TC:0.40,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:58,MDF:20,MSDF:23"
752,30.057,False,1,0,1,YOLO_STABLE,"Y1:(298,157,73,177,12921,0.884)","Z:0.998,P:-0.0,-0.3,R:0.001,C:0.90,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:1,FSMS:0,MDF:20,MSDF:23"
753,30.097,False,1,0,1,YOLO_STABLE,"Y1:(296,155,75,171,12825,0.888)","Z:0.996,P:0.8,-0.7,R:-0.001,C:0.79,TC:0.20,TF:1,WM:False,FPS:False,CB:5,FSME:2,FSMS:0,MDF:20,MSDF:23"
754,30.137,False,1,0,1,YOLO_STABLE,"Y1:(297,155,74,171,12654,0.887)","Z:0.999,P:0.6,-0.4,R:-0.001,C:0.94,TC:0.20,TF:2,WM:False,FPS:False,CB:5,FSME:3,FSMS:0,MDF:20,MSDF:23"
755,30.177,False,2,0,2,YOLO_STABLE,"Y1:(297,155,74,166,12284,0.892)|Y2:(683,103,20,27,540,0.316)","Z:0.999,P:0.7,-0.5,R:0.000,C:0.89,TC:0.20,TF:3,WM:False,FPS:False,CB:5,FSME:4,FSMS:0,MDF:20,MSDF:23"
756,30.217,False,1,0,1,YOLO_STABLE,"Y1:(298,155,75,165,12375,0.898)","Z:0.999,P:1.2,1.3,R:0.000,C:0.80,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:5,FSMS:0,MDF:20,MSDF:23"
757,30.257,False,2,0,2,YOLO_STABLE,"Y1:(298,155,73,167,12191,0.888)|Y2:(684,105,19,27,513,0.319)","Z:1.001,P:-0.1,0.7,R:0.000,C:0.84,TC:0.20,TF:1,WM:False,FPS:False,CB:5,FSME:6,FSMS:0,MDF:20,MSDF:23"
758,30.297,False,2,0,2,YOLO_STABLE,"Y1:(297,155,73,169,12337,0.884)|Y2:(683,104,20,28,560,0.331)","Z:1.002,P:-1.0,-1.1,R:-0.000,C:0.88,TC:0.20,TF:2,WM:False,FPS:False,CB:5,FSME:7,FSMS:0,MDF:20,MSDF:23"
759,30.337,False,2,0,2,YOLO_STABLE,"Y1:(297,158,74,164,12136,0.885)|Y2:(682,105,21,29,609,0.307)","Z:0.998,P:1.4,1.5,R:0.000,C:0.87,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:8,FSMS:0,MDF:20,MSDF:23"
760,30.377,False,2,0,2,YOLO_STABLE,"Y1:(297,155,74,169,12506,0.879)|Y2:(682,104,21,28,588,0.326)","Z:1.002,P:-0.4,-1.7,R:-0.001,C:0.94,TC:0.60,TF:0,WM:False,FPS:False,CB:5,FSME:9,FSMS:0,MDF:20,MSDF:23"
761,30.417,False,1,0,1,YOLO_STABLE,"Y1:(297,158,73,169,12337,0.873)","Z:0.998,P:1.2,0.9,R:-0.001,C:0.87,TC:0.60,TF:0,WM:False,FPS:False,CB:5,FSME:10,FSMS:0,MDF:20,MSDF:23"
762,30.457,False,2,0,2,YOLO_STABLE,"Y1:(298,157,73,166,12118,0.880)|Y2:(684,104,19,29,551,0.337)","Z:1.005,P:-1.2,0.8,R:0.002,C:0.60,TC:0.60,TF:1,WM:False,FPS:False,CB:5,FSME:11,FSMS:0,MDF:20,MSDF:23"
763,30.497,False,1,0,1,YOLO_STABLE,"Y1:(299,158,73,175,12775,0.866)","Z:0.999,P:0.8,0.7,R:-0.000,C:0.89,TC:0.60,TF:2,WM:False,FPS:False,CB:5,FSME:12,FSMS:0,MDF:20,MSDF:23"
764,30.537,False,2,0,2,YOLO_STABLE,"Y1:(298,156,75,168,12600,0.885)|Y2:(683,104,20,28,560,0.352)","Z:1.000,P:-0.3,-1.1,R:0.000,C:0.89,TC:0.40,TF:3,WM:False,FPS:False,CB:5,FSME:13,FSMS:0,MDF:20,MSDF:23"
765,30.577,False,2,0,2,YOLO_STABLE,"Y1:(297,154,74,168,12432,0.889)|Y2:(682,103,21,27,567,0.395)","Z:0.999,P:-0.6,-1.8,R:0.000,C:0.79,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:14,FSMS:0,MDF:20,MSDF:23"
766,30.617,False,2,0,2,YOLO_STABLE,"Y1:(298,154,72,169,12168,0.879)|Y2:(683,103,20,27,540,0.395)","Z:1.000,P:0.6,-0.3,R:-0.001,C:0.88,TC:0.20,TF:1,WM:False,FPS:False,CB:5,FSME:15,FSMS:0,MDF:20,MSDF:23"
767,30.657,False,2,0,2,YOLO_STABLE,"Y1:(298,155,73,171,12483,0.865)|Y2:(684,103,19,28,532,0.304)","Z:1.000,P:0.9,0.8,R:-0.000,C:0.86,TC:0.20,TF:2,WM:False,FPS:False,CB:5,FSME:16,FSMS:0,MDF:20,MSDF:23"
768,30.697,False,2,0,2,YOLO_STABLE,"Y1:(298,158,70,171,11970,0.857)|Y2:(683,105,20,30,600,0.361)","Z:0.999,P:0.6,2.4,R:0.000,C:0.89,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:17,FSMS:0,MDF:20,MSDF:23"
769,30.737,False,2,0,2,YOLO_STABLE,"Y1:(297,157,73,170,12410,0.866)|Y2:(683,105,20,29,580,0.384)","Z:1.000,P:0.0,-0.3,R:0.001,C:0.95,TC:0.40,TF:1,WM:False,FPS:False,CB:5,FSME:18,FSMS:0,MDF:20,MSDF:23"
770,30.777,False,2,0,2,YOLO_STABLE,"Y1:(297,157,72,177,12744,0.863)|Y2:(683,105,20,29,580,0.390)","Z:1.000,P:0.3,0.1,R:-0.000,C:0.94,TC:0.20,TF:2,WM:False,FPS:False,CB:5,FSME:19,FSMS:0,MDF:20,MSDF:23"
771,30.817,False,2,0,2,YOLO_STABLE,"Y1:(297,156,73,171,12483,0.871)|Y2:(683,104,20,28,560,0.352)","Z:1.001,P:-0.8,-1.5,R:0.001,C:0.85,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:20,FSMS:0,MDF:20,MSDF:23"
772,30.857,False,2,0,2,YOLO_STABLE,"Y1:(297,156,73,174,12702,0.869)|Y2:(684,104,19,28,532,0.304)","Z:0.999,P:0.4,-0.4,R:-0.001,C:0.91,TC:0.40,TF:1,WM:False,FPS:False,CB:5,FSME:21,FSMS:0,MDF:20,MSDF:23"
773,30.897,False,2,0,2,YOLO_STABLE,"Y1:(297,156,72,176,12672,0.858)|Y2:(684,103,19,28,532,0.319)","Z:0.999,P:1.0,-0.6,R:-0.000,C:0.82,TC:0.20,TF:2,WM:False,FPS:False,CB:5,FSME:22,FSMS:0,MDF:20,MSDF:23"
774,30.936,False,2,0,2,YOLO_STABLE,"Y1:(297,157,72,173,12456,0.868)|Y2:(683,104,20,27,540,0.354)","Z:1.001,P:-0.5,0.6,R:-0.000,C:0.92,TC:0.20,TF:3,WM:False,FPS:False,CB:5,FSME:23,FSMS:0,MDF:20,MSDF:23"
775,30.976,False,2,0,2,YOLO_STABLE,"Y1:(297,158,73,178,12994,0.870)|Y2:(684,105,19,29,551,0.333)","Z:1.001,P:0.8,1.6,R:0.000,C:0.83,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:24,FSMS:0,MDF:20,MSDF:23"
776,31.016,False,2,0,2,YOLO_STABLE,"Y1:(296,158,74,176,13024,0.869)|Y2:(683,105,20,29,580,0.322)","Z:1.000,P:-0.3,0.2,R:0.001,C:0.97,TC:0.20,TF:1,WM:False,FPS:False,CB:5,FSME:25,FSMS:0,MDF:20,MSDF:23"
777,31.056,False,2,0,2,YOLO_STABLE,"Y1:(296,158,74,181,13394,0.875)|Y2:(683,105,20,28,560,0.301)","Z:1.001,P:-0.3,-0.3,R:-0.001,C:0.95,TC:0.20,TF:2,WM:False,FPS:False,CB:5,FSME:26,FSMS:0,MDF:20,MSDF:23"
778,31.096,False,2,0,2,YOLO_STABLE,"Y1:(296,155,75,172,12900,0.883)|Y2:(683,102,20,28,560,0.394)","Z:1.000,P:0.0,-1.8,R:0.000,C:0.91,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:27,FSMS:0,MDF:20,MSDF:23"
779,31.136,True,2,0,0,MOVEMENT_START_DELAY_1/5,None,"Z:0.998,P:1.2,0.9,R:-0.000,C:0.80,TC:0.40,TF:0,WM:True,FPS:False,CB:5,FSME:0,FSMS:1,MDF:20,MSDF:24"
780,31.176,True,1,0,0,MOVEMENT_START_DELAY_2/5,None,"Z:0.998,P:0.7,1.2,R:0.000,C:0.90,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:2,MDF:20,MSDF:25"
781,31.216,False,1,0,1,YOLO_STABLE,"Y1:(297,158,75,170,12750,0.876)","Z:1.001,P:0.7,0.2,R:0.000,C:0.81,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:1,FSMS:0,MDF:20,MSDF:25"
782,31.256,False,2,0,2,YOLO_STABLE,"Y1:(297,159,76,179,13604,0.879)|Y2:(684,106,19,30,570,0.366)","Z:1.000,P:0.2,0.9,R:0.001,C:0.96,TC:0.20,TF:1,WM:False,FPS:False,CB:5,FSME:2,FSMS:0,MDF:20,MSDF:25"
783,31.296,False,1,0,1,YOLO_STABLE,"Y1:(297,158,77,181,13937,0.872)","Z:1.000,P:0.5,-0.6,R:-0.001,C:0.78,TC:0.20,TF:2,WM:False,FPS:False,CB:5,FSME:3,FSMS:0,MDF:20,MSDF:25"
784,31.336,False,2,0,2,YOLO_STABLE,"Y1:(297,157,77,181,13937,0.872)|Y2:(684,105,19,31,589,0.320)","Z:1.001,P:-0.0,0.1,R:0.000,C:0.95,TC:0.00,TF:3,WM:False,FPS:False,CB:5,FSME:4,FSMS:0,MDF:20,MSDF:25"
785,31.376,False,1,0,1,YOLO_STABLE,"Y1:(298,157,77,181,13937,0.872)","Z:1.000,P:0.5,-0.4,R:-0.001,C:0.73,TC:0.00,TF:4,WM:False,FPS:False,CB:5,FSME:5,FSMS:0,MDF:20,MSDF:25"
786,31.416,False,2,0,2,YOLO_STABLE,"Y1:(298,158,76,182,13832,0.878)|Y2:(684,104,19,32,608,0.302)","Z:1.000,P:0.2,-0.1,R:-0.000,C:0.95,TC:0.00,TF:5,WM:False,FPS:False,CB:5,FSME:6,FSMS:0,MDF:20,MSDF:25"
787,31.456,False,1,0,1,YOLO_STABLE,"Y1:(297,157,76,173,13148,0.876)","Z:1.001,P:-0.9,-0.5,R:0.001,C:0.66,TC:0.00,TF:6,WM:False,FPS:False,CB:5,FSME:7,FSMS:0,MDF:20,MSDF:25"
788,31.496,False,1,0,1,YOLO_STABLE,"Y1:(297,157,75,175,13125,0.876)","Z:1.000,P:0.7,-0.6,R:-0.002,C:0.85,TC:0.00,TF:7,WM:False,FPS:False,CB:5,FSME:8,FSMS:0,MDF:20,MSDF:25"
789,31.536,False,2,0,2,YOLO_STABLE,"Y1:(296,156,76,171,12996,0.879)|Y2:(683,104,20,30,600,0.313)","Z:1.001,P:-0.8,-0.5,R:0.001,C:0.95,TC:0.00,TF:8,WM:False,FPS:False,CB:5,FSME:9,FSMS:0,MDF:20,MSDF:25"
790,31.576,False,2,0,2,YOLO_STABLE,"Y1:(297,157,76,176,13376,0.876)|Y2:(683,105,20,29,580,0.314)","Z:0.999,P:0.2,0.7,R:0.000,C:0.92,TC:0.00,TF:9,WM:False,FPS:False,CB:5,FSME:10,FSMS:0,MDF:20,MSDF:25"
791,31.616,False,2,0,2,YOLO_STABLE,"Y1:(297,158,76,181,13756,0.879)|Y2:(683,106,20,31,620,0.357)","Z:1.000,P:0.6,0.9,R:-0.000,C:0.92,TC:0.00,TF:10,WM:False,FPS:False,CB:5,FSME:11,FSMS:0,MDF:20,MSDF:25"
792,31.656,False,2,0,2,YOLO_STABLE,"Y1:(297,157,75,187,14025,0.881)|Y2:(684,105,19,32,608,0.379)","Z:1.003,P:-0.1,-0.9,R:0.000,C:0.82,TC:0.00,TF:11,WM:False,FPS:False,CB:5,FSME:12,FSMS:0,MDF:20,MSDF:25"
793,31.696,False,2,0,2,YOLO_STABLE,"Y1:(296,156,77,187,14399,0.884)|Y2:(683,104,20,30,600,0.325)","Z:1.000,P:-0.5,-0.1,R:0.001,C:0.97,TC:0.00,TF:12,WM:False,FPS:False,CB:5,FSME:13,FSMS:0,MDF:20,MSDF:25"
794,31.736,False,2,0,2,YOLO_STABLE,"Y1:(296,157,76,184,13984,0.883)|Y2:(682,105,21,30,630,0.365)","Z:0.997,P:1.0,0.0,R:-0.001,C:0.76,TC:0.00,TF:13,WM:False,FPS:False,CB:5,FSME:14,FSMS:0,MDF:20,MSDF:25"
795,31.776,False,2,0,2,YOLO_STABLE,"Y1:(296,157,75,182,13650,0.887)|Y2:(683,105,20,31,620,0.350)","Z:1.000,P:-0.2,0.2,R:0.001,C:0.98,TC:0.00,TF:14,WM:False,FPS:False,CB:5,FSME:15,FSMS:0,MDF:20,MSDF:25"
796,31.817,False,2,0,2,YOLO_STABLE,"Y1:(299,156,72,179,12888,0.882)|Y2:(683,102,20,32,640,0.485)","Z:1.000,P:0.7,-1.0,R:-0.000,C:0.90,TC:0.00,TF:15,WM:False,FPS:False,CB:5,FSME:16,FSMS:0,MDF:20,MSDF:25"
797,31.856,False,2,0,2,YOLO_STABLE,"Y1:(300,159,71,180,12780,0.877)|Y2:(683,105,20,31,620,0.453)","Z:0.998,P:0.8,1.5,R:-0.000,C:0.92,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:17,FSMS:0,MDF:20,MSDF:25"
798,31.896,False,2,0,2,YOLO_STABLE,"Y1:(300,157,71,174,12354,0.879)|Y2:(683,104,20,31,620,0.439)","Z:1.001,P:-0.1,-1.0,R:0.001,C:0.95,TC:0.20,TF:1,WM:False,FPS:False,CB:5,FSME:18,FSMS:0,MDF:20,MSDF:25"
799,31.936,False,2,0,2,YOLO_STABLE,"Y1:(302,156,70,175,12250,0.881)|Y2:(684,98,18,34,612,0.302)","Z:1.001,P:1.3,-1.5,R:-0.003,C:0.74,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:19,FSMS:0,MDF:20,MSDF:25"
800,31.976,False,2,0,2,YOLO_STABLE,"Y1:(302,156,70,162,11340,0.892)|Y2:(684,104,19,29,551,0.386)","Z:0.999,P:1.4,0.4,R:-0.000,C:0.65,TC:0.40,TF:1,WM:False,FPS:False,CB:5,FSME:20,FSMS:0,MDF:20,MSDF:25"
801,32.016,False,2,0,2,YOLO_STABLE,"Y1:(301,156,69,168,11592,0.874)|Y2:(684,104,19,30,570,0.414)","Z:1.001,P:-0.5,-0.6,R:-0.000,C:0.94,TC:0.40,TF:2,WM:False,FPS:False,CB:5,FSME:21,FSMS:0,MDF:20,MSDF:25"
802,32.056,False,2,0,2,YOLO_STABLE,"Y1:(302,157,69,172,11868,0.868)|Y2:(683,105,20,28,560,0.470)","Z:0.999,P:0.0,1.4,R:0.001,C:0.95,TC:0.20,TF:3,WM:False,FPS:False,CB:5,FSME:22,FSMS:0,MDF:20,MSDF:25"
803,32.096,False,2,0,2,YOLO_STABLE,"Y1:(302,157,67,168,11256,0.871)|Y2:(684,105,19,29,551,0.423)","Z:1.001,P:0.3,-0.6,R:-0.001,C:0.99,TC:0.20,TF:4,WM:False,FPS:False,CB:5,FSME:23,FSMS:0,MDF:20,MSDF:25"
804,32.136,False,2,0,2,YOLO_STABLE,"Y1:(303,157,68,167,11356,0.870)|Y2:(684,104,19,30,570,0.413)","Z:1.001,P:0.1,-0.3,R:-0.000,C:0.95,TC:0.00,TF:5,WM:False,FPS:False,CB:5,FSME:24,FSMS:0,MDF:20,MSDF:25"
805,32.176,False,2,0,2,YOLO_STABLE,"Y1:(301,158,69,168,11592,0.856)|Y2:(683,105,20,28,560,0.464)","Z:1.000,P:-0.9,1.5,R:0.002,C:0.75,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:25,FSMS:0,MDF:20,MSDF:25"
806,32.216,False,2,0,2,YOLO_STABLE,"Y1:(301,158,69,169,11661,0.856)|Y2:(683,106,20,28,560,0.465)","Z:0.999,P:1.0,0.0,R:-0.001,C:0.93,TC:0.20,TF:1,WM:False,FPS:False,CB:5,FSME:26,FSMS:0,MDF:20,MSDF:25"
807,32.255,False,2,0,2,YOLO_STABLE,"Y1:(303,158,68,164,11152,0.855)|Y2:(684,106,19,29,551,0.397)","Z:1.000,P:0.4,-0.0,R:-0.000,C:0.97,TC:0.20,TF:2,WM:False,FPS:False,CB:5,FSME:27,FSMS:0,MDF:20,MSDF:25"
808,32.295,False,2,0,2,YOLO_STABLE,"Y1:(302,158,68,163,11084,0.860)|Y2:(683,105,20,28,560,0.424)","Z:1.000,P:-0.1,-0.1,R:0.000,C:0.97,TC:0.20,TF:3,WM:False,FPS:False,CB:5,FSME:28,FSMS:0,MDF:20,MSDF:25"
809,32.335,False,2,0,2,YOLO_STABLE,"Y1:(301,159,70,160,11200,0.868)|Y2:(684,106,19,28,532,0.365)","Z:1.000,P:0.4,0.2,R:0.000,C:0.95,TC:0.20,TF:4,WM:False,FPS:False,CB:5,FSME:29,FSMS:0,MDF:20,MSDF:25"
810,32.375,False,2,0,2,YOLO_STABLE,"Y1:(301,157,69,165,11385,0.873)|Y2:(683,104,20,28,560,0.410)","Z:1.001,P:-0.7,-1.8,R:0.000,C:0.81,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:30,FSMS:0,MDF:20,MSDF:25"
811,32.415,False,2,0,2,YOLO_STABLE,"Y1:(302,158,69,167,11523,0.865)|Y2:(684,106,19,27,513,0.362)","Z:1.000,P:0.3,-0.0,R:-0.001,C:0.84,TC:0.20,TF:1,WM:False,FPS:False,CB:5,FSME:31,FSMS:0,MDF:20,MSDF:25"
812,32.456,False,2,0,2,YOLO_STABLE,"Y1:(303,156,68,171,11628,0.871)|Y2:(684,103,19,27,513,0.343)","Z:1.002,P:0.0,-2.1,R:0.001,C:0.72,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:32,FSMS:0,MDF:20,MSDF:25"
813,32.495,False,2,0,2,YOLO_STABLE,"Y1:(303,158,66,172,11352,0.862)|Y2:(684,103,19,28,532,0.372)","Z:1.000,P:0.1,0.6,R:0.000,C:0.95,TC:0.40,TF:1,WM:False,FPS:False,CB:5,FSME:33,FSMS:0,MDF:20,MSDF:25"
814,32.536,False,2,0,2,YOLO_STABLE,"Y1:(302,158,67,173,11591,0.864)|Y2:(684,104,19,28,532,0.324)","Z:1.001,P:-0.1,0.8,R:0.001,C:0.91,TC:0.40,TF:2,WM:False,FPS:False,CB:5,FSME:34,FSMS:0,MDF:20,MSDF:25"
815,32.575,False,2,0,2,YOLO_STABLE,"Y1:(302,158,67,175,11725,0.859)|Y2:(683,105,20,27,540,0.398)","Z:1.000,P:-0.4,-0.1,R:0.000,C:0.97,TC:0.20,TF:3,WM:False,FPS:False,CB:5,FSME:35,FSMS:0,MDF:20,MSDF:25"
816,32.615,False,2,0,2,YOLO_STABLE,"Y1:(305,156,65,177,11505,0.854)|Y2:(684,98,19,31,589,0.372)","Z:1.001,P:1.3,-2.3,R:-0.000,C:0.85,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:36,FSMS:0,MDF:20,MSDF:25"
817,32.655,False,2,0,2,YOLO_STABLE,"Y1:(304,156,66,178,11748,0.858)|Y2:(684,102,19,28,532,0.475)","Z:1.000,P:-0.4,0.2,R:-0.000,C:0.95,TC:0.20,TF:1,WM:False,FPS:False,CB:5,FSME:37,FSMS:0,MDF:20,MSDF:25"
818,32.695,False,2,0,2,YOLO_STABLE,"Y1:(304,156,68,181,12308,0.847)|Y2:(684,102,19,28,532,0.429)","Z:1.001,P:-0.2,0.2,R:0.000,C:0.94,TC:0.20,TF:2,WM:False,FPS:False,CB:5,FSME:38,FSMS:0,MDF:20,MSDF:25"
819,32.735,False,2,0,2,YOLO_STABLE,"Y1:(304,156,69,180,12420,0.850)|Y2:(684,101,19,28,532,0.402)","Z:0.999,P:1.0,-0.4,R:-0.001,C:0.83,TC:0.20,TF:3,WM:False,FPS:False,CB:5,FSME:39,FSMS:0,MDF:20,MSDF:25"
820,32.775,False,2,0,2,YOLO_STABLE,"Y1:(304,157,70,182,12740,0.844)|Y2:(685,102,18,29,522,0.352)","Z:0.999,P:0.9,1.4,R:0.000,C:0.92,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:40,FSMS:0,MDF:20,MSDF:25"
821,32.815,False,2,0,2,YOLO_STABLE,"Y1:(304,157,73,181,13213,0.838)|Y2:(684,102,19,29,551,0.382)","Z:1.001,P:-0.2,-1.1,R:-0.000,C:0.97,TC:0.20,TF:1,WM:False,FPS:False,CB:5,FSME:41,FSMS:0,MDF:20,MSDF:25"
822,32.855,False,2,0,2,YOLO_STABLE,"Y1:(303,158,75,173,12975,0.856)|Y2:(684,100,19,30,570,0.443)","Z:1.000,P:-0.2,-0.5,R:0.000,C:0.93,TC:0.20,TF:2,WM:False,FPS:False,CB:5,FSME:42,FSMS:0,MDF:20,MSDF:25"
823,32.895,False,2,0,2,YOLO_STABLE,"Y1:(297,159,80,173,13840,0.864)|Y2:(684,101,19,28,532,0.476)","Z:1.000,P:0.0,-0.7,R:0.000,C:0.95,TC:0.20,TF:3,WM:False,FPS:False,CB:5,FSME:43,FSMS:0,MDF:20,MSDF:25"
824,32.935,False,2,0,2,YOLO_STABLE,"Y1:(292,167,85,168,14280,0.871)|Y2:(684,102,19,29,551,0.391)","Z:1.000,P:0.3,0.6,R:0.000,C:0.92,TC:0.20,TF:4,WM:False,FPS:False,CB:5,FSME:44,FSMS:0,MDF:20,MSDF:25"
825,32.975,False,2,0,2,YOLO_STABLE,"Y1:(287,177,89,155,13795,0.873)|Y2:(684,103,19,26,494,0.463)","Z:0.999,P:0.2,0.2,R:-0.001,C:0.79,TC:0.00,TF:5,WM:False,FPS:False,CB:5,FSME:45,FSMS:0,MDF:20,MSDF:25"
826,33.015,False,2,0,2,YOLO_STABLE,"Y1:(287,190,92,140,12880,0.854)|Y2:(685,104,18,26,468,0.424)","Z:1.001,P:0.3,0.5,R:0.000,C:0.88,TC:0.00,TF:6,WM:False,FPS:False,CB:5,FSME:46,FSMS:0,MDF:20,MSDF:25"
827,33.055,False,2,0,2,YOLO_STABLE,"Y1:(284,210,95,124,11780,0.846)|Y2:(685,103,18,27,486,0.414)","Z:1.000,P:0.5,0.1,R:-0.000,C:0.97,TC:0.00,TF:7,WM:False,FPS:False,CB:5,FSME:47,FSMS:0,MDF:20,MSDF:25"
828,33.095,False,2,0,2,YOLO_STABLE,"Y1:(281,230,94,106,9964,0.856)|Y2:(684,103,19,28,532,0.478)","Z:1.004,P:-1.2,-0.2,R:0.001,C:0.66,TC:0.00,TF:8,WM:False,FPS:False,CB:5,FSME:48,FSMS:0,MDF:20,MSDF:25"
829,33.135,False,2,0,2,YOLO_STABLE,"Y1:(278,246,94,90,8460,0.794)|Y2:(683,102,20,27,540,0.649)","Z:1.000,P:-1.2,-1.5,R:0.001,C:0.80,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:49,FSMS:0,MDF:20,MSDF:25"
830,33.175,False,2,0,2,YOLO_STABLE,"Y1:(277,249,88,87,7656,0.846)|Y2:(684,102,19,27,513,0.589)","Z:1.001,P:1.1,-0.5,R:-0.002,C:0.71,TC:0.20,TF:1,WM:False,FPS:False,CB:5,FSME:50,FSMS:0,MDF:20,MSDF:25"
831,33.215,False,2,0,2,YOLO_STABLE,"Y1:(269,242,88,96,8448,0.813)|Y2:(684,103,19,27,513,0.540)","Z:0.999,P:0.1,-0.2,R:-0.000,C:0.95,TC:0.20,TF:2,WM:False,FPS:False,CB:5,FSME:51,FSMS:0,MDF:20,MSDF:25"
832,33.255,False,2,0,2,YOLO_STABLE,"Y1:(251,191,107,142,15194,0.781)|Y2:(685,102,18,28,504,0.455)","Z:1.000,P:0.3,0.2,R:0.000,C:0.88,TC:0.20,TF:3,WM:False,FPS:False,CB:5,FSME:52,FSMS:0,MDF:20,MSDF:25"
833,33.295,False,2,0,2,YOLO_STABLE,"Y1:(684,102,19,27,513,0.543)|Y2:(207,97,148,259,38332,0.501)","Z:1.001,P:-0.3,-1.1,R:-0.000,C:0.88,TC:0.20,TF:4,WM:False,FPS:False,CB:5,FSME:53,FSMS:0,MDF:20,MSDF:25"
834,33.335,False,2,0,2,YOLO_STABLE,"Y1:(192,94,146,216,31536,0.865)|Y2:(684,103,19,27,513,0.506)","Z:1.000,P:0.1,1.0,R:0.001,C:0.91,TC:0.00,TF:5,WM:False,FPS:False,CB:5,FSME:54,FSMS:0,MDF:20,MSDF:25"
835,33.375,False,2,0,2,YOLO_STABLE,"Y1:(167,140,149,152,22648,0.896)|Y2:(683,103,20,26,520,0.603)","Z:1.000,P:-0.4,-0.5,R:-0.001,C:0.94,TC:0.00,TF:6,WM:False,FPS:False,CB:5,FSME:55,FSMS:0,MDF:20,MSDF:25"
836,33.415,False,2,0,2,YOLO_STABLE,"Y1:(133,123,153,139,21267,0.891)|Y2:(684,104,19,26,494,0.505)","Z:1.000,P:0.1,1.3,R:0.001,C:0.79,TC:0.00,TF:7,WM:False,FPS:False,CB:5,FSME:56,FSMS:0,MDF:20,MSDF:25"
837,33.455,False,2,0,2,YOLO_STABLE,"Y1:(121,107,130,136,17680,0.921)|Y2:(684,104,19,26,494,0.532)","Z:1.001,P:0.3,-0.7,R:-0.001,C:0.87,TC:0.00,TF:8,WM:False,FPS:False,CB:5,FSME:57,FSMS:0,MDF:20,MSDF:25"
838,33.495,False,2,0,2,YOLO_STABLE,"Y1:(97,47,137,188,25756,0.756)|Y2:(684,105,19,26,494,0.557)","Z:0.999,P:1.0,0.8,R:0.000,C:0.86,TC:0.00,TF:9,WM:False,FPS:False,CB:5,FSME:58,FSMS:0,MDF:20,MSDF:25"
839,33.534,False,2,0,2,YOLO_STABLE,"Y1:(684,104,19,27,513,0.545)|Y2:(87,0,119,231,27489,0.325)","Z:1.001,P:-0.1,-0.2,R:0.000,C:0.90,TC:0.00,TF:10,WM:False,FPS:False,CB:5,FSME:59,FSMS:0,MDF:20,MSDF:25"
840,33.574,False,2,0,2,YOLO_STABLE,"Y1:(57,41,116,157,18212,0.867)|Y2:(684,103,19,26,494,0.613)","Z:1.002,P:-1.1,-1.8,R:-0.001,C:0.79,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:60,FSMS:0,MDF:20,MSDF:25"
841,33.614,False,2,0,2,YOLO_STABLE,"Y1:(9,37,148,137,20276,0.854)|Y2:(684,101,19,28,532,0.688)","Z:1.000,P:0.3,-1.0,R:0.001,C:0.83,TC:0.20,TF:1,WM:False,FPS:False,CB:5,FSME:61,FSMS:0,MDF:20,MSDF:25"
842,33.655,False,2,0,2,YOLO_STABLE,"Y1:(3,40,124,93,11532,0.744)|Y2:(684,103,19,27,513,0.646)","Z:0.999,P:0.1,0.9,R:-0.001,C:0.80,TC:0.20,TF:2,WM:False,FPS:False,CB:5,FSME:62,FSMS:0,MDF:20,MSDF:25"
843,33.694,True,2,0,0,MOVEMENT_START_DELAY_1/5,None,"Z:1.002,P:-0.3,-0.4,R:0.001,C:0.94,TC:0.20,TF:0,WM:True,FPS:False,CB:5,FSME:0,FSMS:1,MDF:20,MSDF:26"
844,33.735,True,1,0,0,MOVEMENT_START_DELAY_2/5,None,"Z:0.999,P:1.1,1.3,R:0.001,C:0.93,TC:0.40,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:2,MDF:20,MSDF:27"
845,33.774,True,1,0,0,MOVEMENT_START_DELAY_3/5,None,"Z:1.002,P:-1.2,0.5,R:-0.000,C:0.86,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:3,MDF:20,MSDF:28"
846,33.814,False,1,0,1,YOLO_STABLE,"Y1:(684,103,19,27,513,0.668)","Z:1.001,P:-0.3,-1.9,R:0.001,C:0.70,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:1,FSMS:0,MDF:20,MSDF:28"
847,33.855,False,1,0,1,YOLO_STABLE,"Y1:(684,101,19,28,532,0.663)","Z:1.000,P:0.5,-1.4,R:-0.001,C:0.89,TC:0.20,TF:1,WM:False,FPS:False,CB:5,FSME:2,FSMS:0,MDF:20,MSDF:28"
848,33.894,False,1,0,1,YOLO_STABLE,"Y1:(684,100,19,28,532,0.678)","Z:1.000,P:0.2,-1.2,R:0.000,C:0.95,TC:0.20,TF:2,WM:False,FPS:False,CB:5,FSME:3,FSMS:0,MDF:20,MSDF:28"
849,33.934,False,1,0,1,YOLO_STABLE,"Y1:(683,102,20,27,540,0.665)","Z:0.999,P:-0.1,1.6,R:0.000,C:0.95,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:4,FSMS:0,MDF:20,MSDF:28"
850,33.974,False,1,0,1,YOLO_STABLE,"Y1:(684,104,19,25,475,0.588)","Z:1.000,P:-0.2,1.0,R:0.000,C:0.81,TC:0.20,TF:1,WM:False,FPS:False,CB:5,FSME:5,FSMS:0,MDF:20,MSDF:28"
851,34.014,False,1,0,1,YOLO_STABLE,"Y1:(684,104,19,25,475,0.584)","Z:1.001,P:0.0,1.2,R:0.001,C:0.88,TC:0.20,TF:2,WM:False,FPS:False,CB:5,FSME:6,FSMS:0,MDF:20,MSDF:28"
852,34.054,False,1,0,1,YOLO_STABLE,"Y1:(685,103,18,26,468,0.579)","Z:0.999,P:0.8,-0.4,R:0.001,C:0.94,TC:0.20,TF:3,WM:False,FPS:False,CB:5,FSME:7,FSMS:0,MDF:20,MSDF:28"
853,34.094,False,1,0,1,YOLO_STABLE,"Y1:(684,102,19,27,513,0.635)","Z:1.002,P:-0.4,-1.3,R:-0.001,C:0.72,TC:0.20,TF:4,WM:False,FPS:False,CB:5,FSME:8,FSMS:0,MDF:20,MSDF:28"
854,34.134,False,1,0,1,YOLO_STABLE,"Y1:(684,101,19,27,513,0.653)","Z:1.000,P:0.2,-0.9,R:-0.001,C:0.92,TC:0.00,TF:5,WM:False,FPS:False,CB:5,FSME:9,FSMS:0,MDF:20,MSDF:28"
855,34.174,False,1,0,1,YOLO_STABLE,"Y1:(684,102,19,27,513,0.626)","Z:0.998,P:0.7,-0.3,R:0.001,C:0.83,TC:0.00,TF:6,WM:False,FPS:False,CB:5,FSME:10,FSMS:0,MDF:20,MSDF:28"
856,34.214,False,1,0,1,YOLO_STABLE,"Y1:(685,102,18,27,486,0.591)","Z:1.002,P:-0.5,0.9,R:0.000,C:0.84,TC:0.00,TF:7,WM:False,FPS:False,CB:5,FSME:11,FSMS:0,MDF:20,MSDF:28"
857,34.254,False,1,0,1,YOLO_STABLE,"Y1:(684,104,19,26,494,0.633)","Z:1.000,P:-0.5,1.2,R:0.000,C:0.95,TC:0.00,TF:8,WM:False,FPS:False,CB:5,FSME:12,FSMS:0,MDF:20,MSDF:28"
858,34.294,False,1,0,1,YOLO_STABLE,"Y1:(684,102,19,26,494,0.644)","Z:1.001,P:-0.0,-1.4,R:0.000,C:0.86,TC:0.00,TF:9,WM:False,FPS:False,CB:5,FSME:13,FSMS:0,MDF:20,MSDF:28"
859,34.334,False,1,0,1,YOLO_STABLE,"Y1:(683,104,20,26,520,0.691)","Z:0.998,P:0.2,0.4,R:0.000,C:0.92,TC:0.00,TF:10,WM:False,FPS:False,CB:5,FSME:14,FSMS:0,MDF:20,MSDF:28"
860,34.374,False,1,0,1,YOLO_STABLE,"Y1:(684,102,19,27,513,0.681)","Z:1.001,P:0.1,-1.5,R:-0.000,C:0.97,TC:0.00,TF:11,WM:False,FPS:False,CB:5,FSME:15,FSMS:0,MDF:20,MSDF:28"
861,34.414,False,1,0,1,YOLO_STABLE,"Y1:(683,101,20,27,540,0.708)","Z:1.000,P:-0.3,-0.0,R:0.000,C:0.96,TC:0.00,TF:12,WM:False,FPS:False,CB:5,FSME:16,FSMS:0,MDF:20,MSDF:28"
862,34.454,False,1,0,1,YOLO_STABLE,"Y1:(684,103,19,27,513,0.674)","Z:0.998,P:1.4,1.1,R:-0.000,C:0.85,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:17,FSMS:0,MDF:20,MSDF:28"
863,34.494,False,1,0,1,YOLO_STABLE,"Y1:(684,104,19,27,513,0.648)","Z:1.000,P:0.4,0.8,R:-0.000,C:0.93,TC:0.20,TF:1,WM:False,FPS:False,CB:5,FSME:18,FSMS:0,MDF:20,MSDF:28"
864,34.534,False,1,0,1,YOLO_STABLE,"Y1:(684,101,19,28,532,0.681)","Z:1.002,P:-0.6,-2.0,R:0.001,C:0.93,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:19,FSMS:0,MDF:20,MSDF:28"
865,34.574,False,1,0,1,YOLO_STABLE,"Y1:(684,99,19,28,532,0.695)","Z:0.999,P:0.4,-1.5,R:-0.001,C:0.91,TC:0.60,TF:0,WM:False,FPS:False,CB:5,FSME:20,FSMS:0,MDF:20,MSDF:28"
866,34.614,True,1,0,0,MOVEMENT_START_DELAY_1/5,None,"Z:1.000,P:-1.0,0.4,R:0.000,C:0.98,TC:0.60,TF:0,WM:True,FPS:False,CB:5,FSME:0,FSMS:1,MDF:20,MSDF:29"
867,34.654,True,1,0,0,MOVEMENT_START_DELAY_2/5,None,"Z:0.999,P:1.5,1.0,R:-0.000,C:0.88,TC:0.80,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:2,MDF:20,MSDF:30"
868,34.694,True,1,0,0,MOVEMENT_START_DELAY_3/5,None,"Z:1.000,P:-0.5,2.0,R:0.001,C:0.96,TC:0.80,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:3,MDF:20,MSDF:31"
869,34.734,True,1,0,0,MOVEMENT_START_DELAY_4/5,None,"Z:1.002,P:-0.0,-0.8,R:0.001,C:0.78,TC:0.80,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:4,MDF:20,MSDF:32"
870,34.774,True,1,0,1,YOLO_MOVING,"Y1:(684,101,19,28,532,0.686)","Z:0.999,P:0.0,0.4,R:0.001,C:0.94,TC:0.60,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:5,MDF:20,MSDF:32"
871,34.814,True,1,0,1,YOLO_MOVING,"Y1:(683,102,19,27,513,0.699)","Z:0.999,P:-0.1,-0.9,R:-0.000,C:0.96,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:6,MDF:20,MSDF:32"
872,34.854,False,1,0,1,YOLO_STABLE,"Y1:(683,101,20,27,540,0.724)","Z:1.000,P:0.1,-0.8,R:-0.000,C:0.96,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:1,FSMS:0,MDF:20,MSDF:32"
873,34.893,False,1,0,1,YOLO_STABLE,"Y1:(683,101,20,27,540,0.691)","Z:1.002,P:-0.3,0.5,R:0.001,C:0.92,TC:0.00,TF:1,WM:False,FPS:False,CB:5,FSME:2,FSMS:0,MDF:20,MSDF:32"
874,34.933,False,1,0,1,YOLO_STABLE,"Y1:(685,102,18,29,522,0.597)","Z:0.999,P:1.4,1.1,R:-0.000,C:0.93,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:3,FSMS:0,MDF:20,MSDF:32"
875,34.973,False,1,0,1,YOLO_STABLE,"Y1:(684,103,19,27,513,0.616)","Z:1.001,P:-0.5,0.4,R:0.000,C:0.97,TC:0.20,TF:1,WM:False,FPS:False,CB:5,FSME:4,FSMS:0,MDF:20,MSDF:32"
876,35.014,False,1,0,1,YOLO_STABLE,"Y1:(684,103,19,28,532,0.609)","Z:0.999,P:0.5,0.0,R:0.001,C:0.92,TC:0.20,TF:2,WM:False,FPS:False,CB:5,FSME:5,FSMS:0,MDF:20,MSDF:32"
877,35.053,False,1,0,1,YOLO_STABLE,"Y1:(684,100,19,29,551,0.600)","Z:1.000,P:-0.2,-1.2,R:-0.001,C:0.96,TC:0.20,TF:3,WM:False,FPS:False,CB:5,FSME:6,FSMS:0,MDF:20,MSDF:32"
878,35.093,False,1,0,1,YOLO_STABLE,"Y1:(684,99,19,28,532,0.670)","Z:1.002,P:-0.9,-2.4,R:0.000,C:0.86,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:7,FSMS:0,MDF:20,MSDF:32"
879,35.133,False,1,0,1,YOLO_STABLE,"Y1:(683,101,20,28,560,0.688)","Z:0.998,P:1.0,1.8,R:-0.001,C:0.82,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:8,FSMS:0,MDF:20,MSDF:32"
880,35.173,False,1,0,1,YOLO_STABLE,"Y1:(684,102,18,28,504,0.595)","Z:1.001,P:1.2,1.9,R:0.001,C:0.76,TC:0.60,TF:0,WM:False,FPS:False,CB:5,FSME:9,FSMS:0,MDF:20,MSDF:32"
881,35.213,False,1,0,1,YOLO_STABLE,"Y1:(684,103,19,28,532,0.578)","Z:1.001,P:-0.2,0.9,R:0.000,C:0.95,TC:0.60,TF:1,WM:False,FPS:False,CB:5,FSME:10,FSMS:0,MDF:20,MSDF:32"
882,35.253,True,1,0,0,MOVEMENT_START_DELAY_1/5,None,"Z:1.000,P:-0.7,-1.8,R:0.000,C:0.96,TC:0.80,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:1,MDF:20,MSDF:33"
883,35.293,True,1,0,0,MOVEMENT_START_DELAY_2/5,None,"Z:1.000,P:0.3,-2.0,R:-0.001,C:0.92,TC:0.80,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:2,MDF:20,MSDF:34"
884,35.333,True,1,0,0,MOVEMENT_START_DELAY_3/5,None,"Z:0.998,P:-0.8,0.2,R:0.001,C:0.90,TC:0.80,TF:2,WM:True,FPS:False,CB:5,FSME:0,FSMS:3,MDF:20,MSDF:35"
885,35.373,True,1,0,0,MOVEMENT_START_DELAY_4/5,None,"Z:1.001,P:0.4,-0.6,R:0.000,C:0.82,TC:0.80,TF:3,WM:True,FPS:False,CB:5,FSME:0,FSMS:4,MDF:20,MSDF:36"
886,35.413,True,1,0,1,YOLO_MOVING,"Y1:(684,103,19,26,494,0.635)","Z:0.998,P:1.1,3.0,R:-0.000,C:0.90,TC:1.00,TF:4,WM:False,FPS:False,CB:5,FSME:0,FSMS:5,MDF:20,MSDF:36"
887,35.453,True,2,0,2,YOLO_MOVING,"Y1:(683,104,19,27,513,0.639)|Y2:(505,343,20,10,200,0.394)","Z:1.000,P:0.2,1.0,R:0.001,C:0.77,TC:0.80,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:6,MDF:20,MSDF:36"
888,35.493,True,2,0,2,YOLO_MOVING,"Y1:(683,101,20,27,540,0.675)|Y2:(504,342,20,10,200,0.313)","Z:1.001,P:-0.8,-1.6,R:-0.000,C:0.96,TC:0.80,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:7,MDF:20,MSDF:36"
889,35.534,True,1,0,1,YOLO_MOVING,"Y1:(684,100,19,28,532,0.644)","Z:1.000,P:1.3,-0.3,R:0.001,C:0.93,TC:0.60,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:8,MDF:20,MSDF:36"
890,35.573,True,1,0,1,YOLO_MOVING,"Y1:(683,99,19,27,513,0.732)","Z:1.000,P:-0.8,-2.3,R:0.000,C:0.92,TC:0.60,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:9,MDF:20,MSDF:36"
891,35.613,True,1,0,1,YOLO_MOVING,"Y1:(683,101,20,28,560,0.698)","Z:0.999,P:0.4,1.5,R:-0.000,C:0.93,TC:0.60,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:10,MDF:20,MSDF:36"
892,35.653,True,1,0,1,YOLO_MOVING,"Y1:(684,101,19,28,532,0.648)","Z:0.999,P:1.0,0.8,R:0.000,C:0.96,TC:0.60,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:11,MDF:20,MSDF:36"
893,35.693,True,1,0,1,YOLO_MOVING,"Y1:(684,101,19,27,513,0.625)","Z:1.000,P:0.6,0.5,R:-0.000,C:0.96,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:12,MDF:20,MSDF:36"
894,35.733,True,1,0,1,YOLO_MOVING,"Y1:(683,103,20,26,520,0.672)","Z:1.001,P:-0.6,0.3,R:0.000,C:0.87,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:13,MDF:20,MSDF:36"
895,35.773,False,1,0,1,YOLO_STABLE,"Y1:(683,101,20,27,540,0.684)","Z:1.000,P:-0.1,-0.3,R:0.001,C:0.93,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:1,FSMS:0,MDF:20,MSDF:36"
896,35.813,False,1,0,1,YOLO_STABLE,"Y1:(682,100,21,27,567,0.714)","Z:1.001,P:-0.5,-0.8,R:-0.000,C:0.94,TC:0.00,TF:1,WM:False,FPS:False,CB:5,FSME:2,FSMS:0,MDF:20,MSDF:36"
897,35.853,False,1,0,1,YOLO_STABLE,"Y1:(684,100,19,28,532,0.633)","Z:1.000,P:1.2,0.4,R:-0.001,C:0.97,TC:0.00,TF:2,WM:False,FPS:False,CB:5,FSME:3,FSMS:0,MDF:20,MSDF:36"
898,35.893,False,1,0,1,YOLO_STABLE,"Y1:(683,102,20,27,540,0.627)","Z:1.000,P:-0.6,1.0,R:0.001,C:0.94,TC:0.00,TF:3,WM:False,FPS:False,CB:5,FSME:4,FSMS:0,MDF:20,MSDF:36"
899,35.933,True,1,0,0,MOVEMENT_START_DELAY_1/5,None,"Z:0.998,P:1.3,0.7,R:0.000,C:0.88,TC:0.00,TF:0,WM:True,FPS:False,CB:5,FSME:0,FSMS:1,MDF:20,MSDF:37"
900,35.973,False,1,0,1,YOLO_STABLE,"Y1:(684,101,19,28,532,0.539)","Z:1.001,P:-0.0,0.1,R:0.001,C:0.85,TC:0.00,TF:0,WM:False,FPS:False,CB:5,FSME:1,FSMS:0,MDF:20,MSDF:37"
901,36.013,False,1,0,1,YOLO_STABLE,"Y1:(684,99,19,28,532,0.612)","Z:0.999,P:0.5,-1.8,R:0.000,C:0.95,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:2,FSMS:0,MDF:20,MSDF:37"
902,36.053,False,1,0,1,YOLO_STABLE,"Y1:(684,101,19,26,494,0.645)","Z:1.001,P:-0.6,0.9,R:-0.000,C:0.98,TC:0.20,TF:1,WM:False,FPS:False,CB:5,FSME:3,FSMS:0,MDF:20,MSDF:37"
903,36.093,True,1,0,0,MOVEMENT_START_DELAY_1/5,None,"Z:0.998,P:1.2,2.7,R:0.002,C:0.88,TC:0.20,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:1,MDF:20,MSDF:38"
904,36.133,True,1,0,0,MOVEMENT_START_DELAY_2/5,None,"Z:1.000,P:0.1,-1.9,R:-0.001,C:0.94,TC:0.40,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:2,MDF:20,MSDF:39"
905,36.173,True,1,0,0,MOVEMENT_START_DELAY_3/5,None,"Z:1.004,P:-3.2,0.3,R:0.002,C:0.59,TC:0.40,TF:0,WM:False,FPS:False,CB:5,FSME:0,FSMS:3,MDF:20,MSDF:40"
906,36.212,True,1,0,0,MOVEMENT_START_DELAY_4/5,None,"Z:0.996,P:3.0,-1.2,R:-0.001,C:0.67,TC:0.60,TF:1,WM:False,FPS:False,CB:5,FSME:0,FSMS:4,MDF:20,MSDF:41"
907,36.252,True,1,0,1,YOLO_MOVING,"Y1:(683,101,20,27,540,0.693)","Z:1.000,P:-1.3,0.8,R:0.001,C:0.93,TC:0.60,TF:2,WM:False,FPS:False,CB:5,FSME:0,FSMS:5,MDF:20,MSDF:41"
